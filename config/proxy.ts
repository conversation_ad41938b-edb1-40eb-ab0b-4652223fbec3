/**
 * 在生产环境 代理是无法生效的，所以这里没有生产环境的配置
 * The agent cannot take effect in the production environment
 * so there is no configuration of the production environment
 * For details, please see
 * https://pro.ant.design/docs/deploy
 */

//  国内：http://api.petkit.cn/6     测试 http://api-sandbox.petkit.com/6
//  新加坡：正式 http://api.petktasia.com/6     测试 http://api-sandbox.petktasia.com/6
//  美西： 正式 http://api.petkt.com/6     测试 http://api-sandbox.petkt.com/6

const localDomain = '192.168.15.18:18080';
const mockRootDomain = 'yapi.petkit.com/mock';
const domains: { [key: string]: string } = {
  mockDomains: `${mockRootDomain}/${387}/`,
  sandboxDomains: `api-sandbox.petkit.com/6`,
  sandboxDomains2: `api-sandbox2.petkit.com/6`,
  onlineDomains: `oms.petkit.cn/lastest`,
};

const getProxyApiInfo = (domain: string) => {
  return {
    '/adm/app/locales': {
      target: 'http://api-sandbox.petkit.com/6',
      changeOrigin: true,
      pathRewrite: { '^': '' },
    },
    '/adm/family': {
      target: 'https://api-sandbox2.petkit.com/6',
      changeOrigin: true,
      pathRewrite: { '^': '' },
    },
    '/adm': {
      target: domain,
      changeOrigin: true,
      pathRewrite: { '^': '' },
    },
    '/tag': {
      target: domain,
      changeOrigin: true,
      pathRewrite: { '^': '' },
    },
  };
};

const proxy: { [key: string]: { [key: string]: any } } = {
  local: {
    ...getProxyApiInfo(`http://${localDomain}`),
  },
  mock: {
    ...getProxyApiInfo(`http://${domains.mockDomains}`),
    // '/adm/app/upload_image_token': {
    //   target: 'http://api-sandbox.petkit.com/6',
    //   changeOrigin: true,
    //   pathRewrite: { '^': '' },
    // },
  },
  sandbox: {
    ...getProxyApiInfo(`http://${domains.sandboxDomains}`),
  },
  sandbox2: {
    ...getProxyApiInfo(`http://${domains.sandboxDomains2}`),
  },
  online: {
    ...getProxyApiInfo(`http://${domains.onlineDomains}`),
  },
};

export default proxy;
