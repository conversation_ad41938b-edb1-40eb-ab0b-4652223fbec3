export interface Route {
  /**
   * Any valid URL path
   */
  path?: string;
  /**
   * A React component to render only when the location matches.
   */
  component?: string | (() => any);
  wrappers?: string[];
  /**
   * navigate to a new location
   */
  redirect?: string;
  /**
   * When true, the active class/style will only be applied if the location is matched exactly.
   */
  exact?: boolean;
  routes?: Route[];
  [k: string]: any;
}

const routes: Route[] = [
  {
    path: `/`,
    redirect: '/welcome',
  },
  {
    path: `/welcome`,
    component: `@/pages/Welcome`,
  },
  {
    path: `/admin/group`,
    component: `@/pages/Configuration/AdminGroup`,
    routes: [
      {
        path: `/admin/group/`,
        redirect: `/admin/group/list`,
      },
      {
        path: `/admin/group/list`,
        component: `@/pages/Configuration/AdminGroup/List`,
      },
      {
        path: `/admin/group/edit/:id`,
        component: `@/pages/Configuration/AdminGroup/Detail`,
      },
    ],
  },
  {
    path: `/user/group`,
    component: `@/pages/UserGroup`,
    routes: [
      {
        path: `/user/group/`,
        redirect: `/user/group/list`,
      },
      {
        path: `/user/group/list`,
        component: `@/pages/UserGroup/List`,
      },
      {
        path: `/user/group/edit/:id`,
        component: `@/pages/UserGroup/Edit`,
      },
    ],
  },
  {
    path: `/standard`,
    routes: [
      {
        path: `/standard`,
        redirect: `/standard/brand/list`,
      },
      {
        path: `/standard/brand`,
        component: `@/pages/Standard/Brand`,
        routes: [
          {
            path: `/standard/brand`,
            redirect: `/standard/brand/list`,
          },
          {
            path: `/standard/brand/list`,
            component: `@/pages/Standard/Brand/List`,
          },
          {
            path: `/standard/brand/edit/:id`,
            component: `@/pages/Standard/Brand/Edit`,
          },
        ],
      },
      {
        path: `/standard/food`,
        component: `@/pages/Standard/Food`,
        routes: [
          {
            path: `/standard/food`,
            redirect: `/standard/food/list`,
          },
          {
            path: `/standard/food/list`,
            component: `@/pages/Standard/Food/List`,
          },
          {
            path: `/standard/food/edit/:id`,
            component: `@/pages/Standard/Food/Edit`,
          },
        ],
      },
      {
        path: `/standard/spec`,
        component: `@/pages/Standard/Spec`,
        routes: [
          {
            path: `/standard/spec`,
            redirect: `/standard/spec/list`,
          },
          {
            path: `/standard/spec/list`,
            component: `@/pages/Standard/Spec/List`,
          },
        ],
      },
      {
        path: `/standard/specAttr`,
        component: `@/pages/Standard/SpecAttr`,
        routes: [
          {
            path: `/standard/specAttr`,
            redirect: `/standard/specAttr/list`,
          },
          {
            path: `/standard/specAttr/list`,
            component: `@/pages/Standard/SpecAttr/List`,
          },
        ],
      },
    ],
  },
  {
    path: `/lock`,
    routes: [
      {
        path: `/lock`,
        redirect: `/lock/material/list`,
      },
      {
        path: `/lock/material`,
        component: `@/pages/Lock/Material`,
        routes: [
          {
            path: `/lock/material/`,
            redirect: `/lock/material/list`,
          },
          {
            path: `/lock/material/list`,
            component: `@/pages/Lock/Material/List`,
          },
          {
            path: `/lock/material/edit/:id`,
            component: `@/pages/Lock/Material/Edit`,
          },
        ],
      },
      {
        path: `/lock/device`,
        component: `@/pages/Lock/Device`,
        routes: [
          {
            path: `/lock/device/`,
            redirect: `/lock/device/list`,
          },
          {
            path: `/lock/device/list`,
            component: `@/pages/Lock/Device/List`,
          },
        ],
      },
    ],
  },
  // {
  //   path: `/firmware/:device`,
  //   routes: [
  //     {
  //       path: `/firmware/:device`,
  //       component: `@/pages/Device/Firmware`,
  //       routes: [
  //         {
  //           path: `/firmware/:device`,
  //           redirect: `/firmware/:device/list`,
  //         },
  //         {
  //           path: `/firmware/:device/list`,
  //           component: `@/pages/Device/Firmware/List`,
  //         },
  //         {
  //           path: `/firmware/:device/detail/:id`,
  //           component: `@/pages/Device/Firmware/Detail`,
  //         },
  //       ],
  //     },
  //   ],
  // },
  {
    path: `/device`,
    routes: [
      {
        path: `/device/:device`,
        component: `@/pages/Device`,
        routes: [
          {
            path: `/device/:device`,
            redirect: `/device/:device/bluetooth/list`,
          },
          {
            path: `/device/:device/bluetooth/list`,
            component: `@/pages/Device/Bluetooth`,
          },
          {
            path: `/device/:device/wifi/list`,
            component: `@/pages/Device/Wifi`,
          },
          {
            path: `/device/:device/sn`,
            component: `@/pages/Device/Sn`,
          },
          {
            path: `/device/:device/modules`,
            component: `@/pages/Device/Module`,
          },
          {
            path: `/device/:device/sound_firmware`,
            component: `@/pages/Device/SoundFirmware`,
          },
          {
            path: `/device/:device/sound_firmware/edit/:id`,
            component: `@/pages/Device/SoundFirmware/Edit`,
          },
          {
            // 固件版本(wifi用)
            path: `/device/:device/firmware/version/list`,
            component: `@/pages/Device/FirmwareVersion`,
          },
          {
            // 固件版本编辑(wifi用)
            path: `/device/:device/firmware/version/edit/:id/:hardware`,
            component: `@/pages/Device/FirmwareVersion/Edit`,
          },
          {
            // 固件版本(腾讯声网-wifi用)
            path: `/device/:device/tc-acoustic/firmware/version/list`,
            component: `@/pages/Device/TcAcousticFirmwareVersion`,
          },
          {
            // 固件版本编辑(腾讯声网-wifi用)
            path: `/device/:device/tc-acoustic/firmware/version/edit/:id/:hardware`,
            component: `@/pages/Device/TcAcousticFirmwareVersion/Edit`,
          },
          {
            // 固件版本(蓝牙用)
            path: `/device/:device/firmware/list`,
            component: `@/pages/Device/Firmware`,
          },
          {
            // 固件版本编辑(蓝牙用)
            path: `/device/:device/firmware/edit/:id/:hardware`,
            component: `@/pages/Device/Firmware/Edit`,
          },
          {
            path: `/device/:device/sound_firmware`,
            component: `@/pages/Device/SoundFirmware`,
          },
          {
            path: `/device/:device/sound_firmware/edit/:id`,
            component: `@/pages/Device/SoundFirmware/Edit`,
          },
          { path: '/device/:device/logs', component: '@/pages/Device/Logs' },
          {
            path: '/device/:device/logs-with-file',
            component: '@/pages/Device/LogsWithFile',
          },
          // 垃圾盒NFC管理
          {
            path: '/device/:device/rubbish-box-nfc/list',
            component: '@/pages/Device/RubbishBoxNFC/List',
          },
          // NFC账号管理
          {
            path: '/device/:device/nfc-account/list',
            component: '@/pages/Device/NFCAccount/List',
          },
          // 垃圾袋盒
          {
            path: '/device/:device/rubbish-box/list',
            component: '@/pages/Device/RubbishBox/List',
          },
          // 水晶猫砂盘页面
          {
            path: '/device/:device/crystal-tray-cat-litter/list',
            component: '@/pages/Device/CrystalTrayCatLitter/List',
          },
          // 水晶猫砂盘管理页面
          {
            path: '/device/:device/crystal-tray-cat-litter-management/list',
            component: '@/pages/Device/CrystalTrayCatLitterManagement/List',
          },
          // 水晶猫砂盘查询
          {
            path: '/device/:device/crystal-tray-cat-litter-nfc/list',
            component: '@/pages/Device/CrystalTrayCatLitterNFC/List',
          },
        ],
      },
    ],
  },
  {
    path: `/link_history`,
    routes: [
      {
        path: `/link_history/:device`,
        component: `@/pages/History`,
        routes: [
          {
            path: `/link_history/:device/:deviceId`,
            component: `@/pages/History/LinkList`,
          },
        ],
      },
    ],
  },
  {
    path: `/beta-device`,
    routes: [
      {
        path: `/beta-device/:device`,
        component: `@/pages/Device/BetaDevice`,
        routes: [
          {
            path: `/beta-device/:device`,
            redirect: `/beta-device/:device/list`,
          },
          {
            path: `/beta-device/:device/list`,
            component: `@/pages/Device/BetaDevice/List`,
          },
        ],
      },
    ],
  },
  {
    path: `/family`,
    component: `@/pages/Family`,
    routes: [
      {
        path: `/family`,
        redirect: `/family/list`,
      },
      {
        path: `/family/list`,
        component: `@/pages/Family/List`,
      },
      // {
      //   path: `/family/edit/:id`,
      //   component: `@/pages/Family/Edit`,
      // },
    ],
  },
  {
    path: `/schedule`,
    routes: [
      {
        path: `/schedule`,
        redirect: `/schedule/type`,
      },
      {
        path: `/schedule/type`,
        component: `@/pages/Schedule/Type`,
        routes: [
          {
            path: `/schedule/type`,
            redirect: '/schedule/type/list',
          },
          {
            path: `/schedule/type/list`,
            component: `@/pages/Schedule/Type/List`,
          },
          {
            path: `/schedule/type/edit/:id`,
            component: `@/pages/Schedule/Type/Edit`,
          },
        ],
      },
    ],
  },
  {
    path: `/business`,
    routes: [
      {
        path: `/business/app/version/verify`,
        component: `@/pages/AppVersionVerification`,
        routes: [
          {
            path: '/business/app/version/verify/',
            redirect: '/business/app/version/verify/list',
          },
          {
            path: '/business/app/version/verify/list',
            component: `@/pages/AppVersionVerification/List`,
          },
        ],
      },
      {
        path: `/business/app-banner`,
        component: `@/pages/Business/AppBanner`,
        routes: [
          {
            path: `/business/app-banner`,
            redirect: `/business/app-banner/list`,
          },
          {
            path: `/business/app-banner/list`,
            component: `@/pages/Business/AppBanner/List`,
          },
          {
            path: `/business/app-banner/edit/:id`,
            component: `@/pages/Business/AppBanner/Edit`,
          },
        ],
      },
      // {
      //   path: `/family/edit/:id`,
      //   component: `@/pages/Family/Edit`,
      // },
    ],
  },
  // 虚拟设备体验
  {
    path: '/virtual-device-config',
    component: '@/pages/VirtualDeviceConfig',
    routes: [
      {
        path: '/virtual-device-config',
        redirect: '/virtual-device-config/list',
      },
      {
        path: '/virtual-device-config/list',
        component: '@/pages/VirtualDeviceConfig/List',
      },
    ],
  },
  {
    path: '/dress-up',
    component: '@/pages/DressUp',
    routes: [
      {
        path: '/dress-up/',
        redirect: '/dress-up/list',
      },
      {
        path: '/dress-up/list',
        component: '@/pages/DressUp/List',
      },
      {
        path: '/dress-up/edit/:id',
        component: '@/pages/DressUp/Edit',
      },
    ],
  },
  {
    path: `/license/batch`,
    component: `@/pages/LicenseBatch`,
    routes: [
      {
        path: '/license/batch/',
        redirect: '/license/batch/list',
      },
      {
        path: '/license/batch/list',
        component: '@/pages/LicenseBatch/List',
      },
    ],
  },
  {
    path: '/system',
    routes: [
      {
        path: '/system/tip-info',
        component: '@/pages/System/TipInfo',
        routes: [
          { path: '/system/tip-info/', redirect: '/system/tip-info/list' },
          {
            path: '/system/tip-info/list',
            component: '@/pages/System/TipInfo/List',
          },
          {
            path: '/system/tip-info/edit/:id',
            component: '@/pages/System/TipInfo/Edit',
          },
        ],
      },
    ],
  },
  {
    path: '/pet',
    routes: [
      {
        path: '/pet/photo-identify',
        component: '@/pages/PhotoIdentify',
        routes: [
          {
            path: '/pet/photo-identify/',
            redirect: '/pet/photo-identify/list',
          },
          {
            path: '/pet/photo-identify/list',
            component: '@/pages/PhotoIdentify/List',
          },
        ],
      },
    ],
  },
  {
    paht: '/activity',
    routes: [
      {
        paht: '/activity/ai-collaboration',
        component: '@/pages/Activity/AiCollcaboartion',
        routes: [
          {
            path: '/activity/ai-collaboration/',
            redirect: '/activity/ai-collaboration/list',
          },
          {
            path: '/activity/ai-collaboration/list',
            component: '@/pages/Activity/AiCollcaboartion/List',
          },
          {
            path: '/activity/ai-collaboration/edit/:id',
            component: '@/pages/Activity/AiCollcaboartion/Edit',
          },
          {
            path: '/activity/ai-collaboration/approval/list',
            component: '@/pages/Activity/AiCollcaboartion/Apply/List',
          },
          {
            path: '/activity/ai-collaboration/approval/edit/:id/:userId/:activityId',
            component: '@/pages/Activity/AiCollcaboartion/Apply/Edit',
          },
        ],
      },
      {
        paht: '/activity/materials',
        component: '@/pages/Activity/Materials',
        routes: [
          {
            path: '/activity/materials/',
            redirect: '/activity/materials/list',
          },
          {
            path: '/activity/materials/list',
            component: '@/pages/Activity/Materials/List',
          },
          {
            path: '/activity/materials/edit/:id',
            component: '@/pages/Activity/Materials/Edit',
          },
          {
            path: '/activity/materials/record-list/:deviceType',
            component: '@/pages/Activity/Materials/RecordList',
          },
          {
            path: '/activity/materials/award/list',
            component: '@/pages/Activity/Materials/Award/List',
          },
          {
            path: '/activity/materials/award/edit/:id',
            component: '@/pages/Activity/Materials/Award/Edit',
          },
        ],
      },
      {
        path: '/protocols',
        component: '@/pages/Protocols',
        routes: [
          {
            path: '/protocols/',
            routes: [
              {
                path: '/protocols/private/',
                redirect: '/protocols/private/list',
              },
              {
                path: '/protocols/private/list',
                component: '@/pages/Protocols/Private/List',
              },
              {
                path: '/protocols/private/edit/:id',
                component: '@/pages/Protocols/Private/Edit',
              },
            ],
          },
        ],
      },
    ],
  },
];

export default routes;
