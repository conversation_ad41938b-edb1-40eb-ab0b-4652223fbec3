import { defineConfig } from '@umijs/max';
import proxy from './config/proxy';
import routes from './config/routes';

const { REACT_APP_ENV } = process.env;

export default defineConfig({
  antd: {},
  dva: {},
  locale: {
    default: 'zh-CN',
    antd: true,
    // default true, when it is true, will use `navigator.language` overwrite default
    baseNavigator: true,
  },
  title: '小佩商城',
  proxy: proxy[REACT_APP_ENV || 'sandbox'],
  routes,
  base: '/oms/',
  publicPath: '/oms/',
  // 第三方脚本全局加载
  scripts: [],
  moment2dayjs: {
    preset: 'antd',
  },
  esbuildMinifyIIFE: true,
});
