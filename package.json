{"private": true, "scripts": {"start": "max dev", "build": "max build", "postinstall": "max setup", "prettier": "prettier --write '**/*.{js,jsx,tsx,ts,less,md,json}'", "start:mock": "cross-env REACT_APP_ENV=mock MOCK=none pnpm start", "start:sandbox": "cross-env REACT_APP_ENV=sandbox MOCK=none pnpm start", "start:sandbox2": "cross-env REACT_APP_ENV=sandbox2 MOCK=none pnpm start", "start:online": "cross-env REACT_APP_ENV=online MOCK=none pnpm start", "start:pre": "cross-env REACT_APP_ENV=pre pnpm start", "start:test": "cross-env REACT_APP_ENV=test MOCK=none pnpm start", "start:local": "cross-env REACT_APP_ENV=local MOCK=none pnpm start"}, "dependencies": {"@ahooksjs/use-url-state": "^3.5.1", "@ant-design/icons": "^4.7.0", "@ant-design/pro-components": "^2.6.8", "@mantas/image-result-json": "^2.0.1", "@mantas/request": "^2.8.1", "@umijs/hooks": "^1.9.3", "@umijs/max": "^4.0.29", "@wangeditor/editor": "^5.1.14", "@wangeditor/editor-for-react": "^1.0.4", "ahooks": "^3.7.6", "antd": "~5.26.1", "classnames": "^2.5.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.9", "hls.js": "^1.5.17", "lodash": "^4.17.21", "moment": "^2.29.1", "qiniu-js": "^3.4.1", "qs": "^6.11.0", "react-activation": "^0.11.2", "react-player": "^2.16.0", "react-sortable-hoc": "^2.0.0"}, "devDependencies": {"@types/lodash": "^4.14.178", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "cross-env": "^7.0.3", "eslint-plugin-import": "^2.27.5", "lint-staged": "^10.0.7", "prettier": "^2.2.0", "prettier-plugin-organize-imports": "^3.2.2", "prettier-plugin-packagejson": "^2.4.3", "typescript": "5.1.6", "yorkie": "^2.0.0"}}