import request, { Data } from '@/utils/request';
import { ApiSuccessEnum } from '../common.interface';
import { privacyApiOption } from './api';
import {
  Privacy,
  PrivacyChangeHistory,
  PrivacyChangeHistoryListParam,
  PrivacyDetail,
  PrivacyListParam,
  PrivacyParam,
} from './interface';

// 获取Privacy列表
export const fetchPrivacyList = (
  param: PrivacyListParam,
): Promise<Data<Privacy>> => {
  const config = privacyApiOption.privacyList;
  config.option.params = param;
  return request(config.url, config.option);
};

// 创建Privacy
export const fetchPrivacyCreation = (
  param: PrivacyParam,
): Promise<ApiSuccessEnum> => {
  const config = privacyApiOption.privacyCreation;
  config.option.data = param;
  return request(config.url, config.option);
};

// 更新Privacy
export const fetchPrivacyUpdate = (
  param: PrivacyParam,
): Promise<ApiSuccessEnum> => {
  const config = privacyApiOption.privacyUpdate;
  config.option.data = param;
  return request(config.url, config.option);
};

// 更新Privacy
export const fetchPrivacyDetail = (id: number): Promise<PrivacyDetail> => {
  const config = privacyApiOption.privacyDetail;
  config.option.data = { id };
  return request(config.url, config.option);
};

// 根据id获取Privacy
export const fetchPrivacyChangeHistory = (
  param: PrivacyChangeHistoryListParam,
): Promise<Data<PrivacyChangeHistory>> => {
  const config = privacyApiOption.privacyChangeHistory;
  config.option.data = param;
  return request(config.url, config.option);
};
