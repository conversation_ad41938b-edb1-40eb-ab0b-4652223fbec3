import { defaultOptions } from '@/services/api/util';
import { RequestMethod, RequestOption } from '@mantas/request';

export const privacyApiOption: Record<
  | 'privacyList'
  | 'privacyCreation'
  | 'privacyUpdate'
  | 'privacyDetail'
  | 'privacyChangeHistory',
  RequestOption
> = {
  // 获取Privacy列表
  privacyList: {
    url: '/adm/privacy/list',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 创建Privacy
  privacyCreation: {
    url: '/adm/privacy/create',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 更新Privacy
  privacyUpdate: {
    url: '/adm/privacy/update',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  privacyDetail: {
    url: '/adm/privacy/detail',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 根据Privacy的id获取详情数据
  privacyChangeHistory: {
    url: '/adm/privacy/history',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
};
