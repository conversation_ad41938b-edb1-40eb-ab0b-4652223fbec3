import { PaginatorParam } from '@/utils/request';

// 列表参数数据
export type PrivacyListParam = PaginatorParam & {
  type: number | '*';
};

// 列表数据
export interface Privacy {
  id: number;
  version: string;
  publishStart: number;
  publishEnd: number;
  createdTime: number;
}

// 创建/编辑提交参数
export interface PrivacyParam {
  id?: number;
  type: '*';
  publishStart: number;
  context: string;
}

// 详情数据
export interface PrivacyDetail {
  id: number;
  type: string;
  publishStart: number;
  publishEnd: number;
  context: string;
  versionNo: string;
}

export type PrivacyChangeHistoryListParam = PaginatorParam & {
  id: number;
};

export interface PrivacyChangeHistory {
  operator: string;
  before: string;
  after: string;
  operateTime: number;
  field: string;
}
