import { PaginatorParam } from '@/utils/request';

export enum ReNewEnum {
  RENEW = 1,
  NOT_RENEW = 0,
}

export enum ServiceTimeUnitEnum {
  YEAR = 'YEAR',
  MONTH = 'MONTH',
  DAY = 'DAY',
}

export enum SaleStatusEnum {
  ON_SALE = 1,
  OFF_SALVE = 0,
}

export interface ProductSkuCapacityParam {
  type: string;
  cycleTime?: number;
}

// 关联的SKU列参数
export interface RelationProductSkuParam {
  uniteDeviceTypes: string[];
  uniteCapacities: ProductSkuCapacityParam[];
  isReNew?: ReNewEnum;
  serviceTime?: number;
  serviceTimeUnit?: ServiceTimeUnitEnum;
  level?: number | null;
  skuAlias?: string;
  skuId?: number;
  skuName?: string;
  saleStatus?: SaleStatusEnum;
  price?: number;
}
export interface RelationProductSkuListParam extends PaginatorParam {
  payload: RelationProductSkuParam;
}

// 列表数据
export interface ProductSku {
  id: number;
  name: string;
  shortName: string;
  aliasName: string;
  saleStatus: boolean;
  capacities: Array<{
    type: string;
    cycleTime: number;
    name: string;
  }>;
  serviceTime: number;
  serviceTimeUnit: ServiceTimeUnitEnum;
  deviceTypes: string[];
  cornerMarkIcon: string;
  description: string;
  createTime: number;
  relationSkuId: number;
  price: {
    price: number;
    linePrice: number;
    firstPhasePrice: number;
    isReNew: boolean;
  };
  // sku等级
  level: number;
  // 优先级
  sort: number;
}
