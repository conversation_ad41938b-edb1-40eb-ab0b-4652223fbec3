import { defaultOptions } from '@/services/api/util';
import { RequestMethod, RequestOption } from '@mantas/request';

export const productApiOption: Record<
  'productSkuDetail' | 'associableProductSkuList',
  RequestOption
> = {
  // 获取ProductSku详情
  productSkuDetail: {
    url: '/adm/bs/product/sku/get',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  // 获取关联的Product列表
  associableProductSkuList: {
    url: '/adm/bs/product/sku/getRelationSku',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
};
