import request, { Data } from '@/utils/request';
import { productApiOption } from './api';
import { ProductSku, RelationProductSkuListParam } from './interface';

// 获取sku详情
export const fetchProductSkuDetail = (skuId: number) => {
  const config = productApiOption.productSkuDetail;
  config.option.params = { skuId };
  return request(config.url, config.option);
};

// 获取可关联SKU列表
export const fetchAssociableProductSkuList = (
  param: RelationProductSkuListParam,
): Promise<Data<ProductSku>> => {
  const config = productApiOption.associableProductSkuList;
  config.option.headers = {
    ...config.option.headers,
    'Content-Type': 'application/x-www-form-urlencoded',
  };
  config.option.data = { ...param, payload: JSON.stringify(param.payload) };
  config.option.requestType = 'form';
  return request(config.url, config.option);
};
