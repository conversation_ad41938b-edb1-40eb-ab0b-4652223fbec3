import { initPaginatorParam } from '@/utils/request';
import {
  RelationProductSkuListParam,
  RelationProductSkuParam,
  ServiceTimeUnitEnum,
} from './interface';

export const serviceTimeUnitNameObj: { [key in ServiceTimeUnitEnum]: string } =
  {
    [ServiceTimeUnitEnum.DAY]: '天',
    [ServiceTimeUnitEnum.MONTH]: '月',
    [ServiceTimeUnitEnum.YEAR]: '年',
  };

const initRelationProductSkuParam: RelationProductSkuParam = {
  uniteCapacities: [],
  uniteDeviceTypes: [],
};

export const initRelationProductSkuListParam: RelationProductSkuListParam = {
  ...initPaginatorParam,
  payload: initRelationProductSkuParam,
};
