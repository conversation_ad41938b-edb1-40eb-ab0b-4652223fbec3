import { SelectOption } from '@/models/common.interface';
import { Effect, Reducer } from '@umijs/max';
import { fetchPetTypeList } from './fetch';
import { PetType } from './interface';

export interface StandardState {
  petTypeList: PetType[];
  petTypesMap: Map<string, string>;
  petTypeOptions: SelectOption[];
}

export const initStandardState: StandardState = {
  petTypeList: [],
  petTypesMap: new Map(),
  petTypeOptions: [],
};

export interface StandardModel {
  namespace: 'standard';
  state: StandardState;
  effects: {
    requestPetTypeList: Effect;
  };
  reducers: {
    requestPetTypeListSuccess: Reducer<
      StandardState,
      { type: 'requestPetTypeListSuccess'; payload: PetType[] }
    >;
  };
}

const standardModel: StandardModel = {
  namespace: 'standard',
  state: initStandardState,
  effects: {
    *requestPetTypeList(action, { call, put }) {
      const petTypeList: PetType[] = yield call(fetchPetTypeList);
      yield put({
        type: 'requestPetTypeListSuccess',
        payload: petTypeList,
      });
    },
  },
  reducers: {
    requestPetTypeListSuccess(
      state = initStandardState,
      { payload },
    ): StandardState {
      const map = new Map<string, string>();
      payload.forEach((item) =>
        map.set(item.name, item.name === 'Dog' ? '狗' : '猫'),
      );
      return {
        ...state,
        petTypeList: payload,
        petTypesMap: map,
        petTypeOptions: payload.map((item) => ({
          value: item.name,
          label: item.name,
        })),
      };
    },
  },
};

export default standardModel;
