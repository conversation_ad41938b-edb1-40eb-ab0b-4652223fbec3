import { StatusEnum } from '@/models/common.interface';
import { standardApiOption } from '@/services/api/standard';
import request from '@/utils/request';
import {
  Brand,
  BrandDetail,
  BrandParam,
  Spec,
  SpecAttrParam,
  SpecParam,
  Food,
  FoodDetail,
  SpecAttr,
  PetType,
  FoodParam,
} from './interface';

/**
 * ************************************************************************
 * 品牌相关接口
 */
// 获取品牌列表
export const fetchBrandList = (): Promise<Brand[]> => {
  const config = standardApiOption.foodBrandList;
  return request(config.url, config.option);
};

// 创建品牌
export const fetchBrandCreation = (param: BrandParam): Promise<string> => {
  const config = standardApiOption.foodBrandCreation;
  config.option.data = param;
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 更新品牌
export const fetchBrandUpdating = (param: BrandParam): Promise<string> => {
  const config = standardApiOption.foodBrandUpdating;
  config.option.data = param;
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 根据id获取品牌
export const fetchBrandDetailById = (brandId: number): Promise<BrandDetail> => {
  const config = standardApiOption.foodBrandDetailById;
  config.option.params = { brandId };
  return request(config.url, config.option);
};

// 切换品牌状态
export const fetchBrandStatusSwitch = (brandId: number, status: boolean): Promise<string> => {
  const config = standardApiOption.foodBrandStatusSwitch;
  config.option.data = { brandId, status: +status as StatusEnum };
  config.option.requestType = 'form';
  return request(config.url, config.option);
};
/**
 * ************************************************************************
 */
// /**
//  * ************************************************************************
//  * 主粮相关接口
//  */
// // 获取规格列表
export const fetchFoodList = (brandId: number): Promise<Food[]> => {
  const config = standardApiOption.foodListByBrandId;
  config.option.data = { brandId };
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 创建规格
export const fetchFoodCreation = (param: FoodParam): Promise<string> => {
  const config = standardApiOption.foodCreation;
  config.option.data = param;
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 更新规格
export const fetchFoodUpdating = (param: FoodParam): Promise<string> => {
  const config = standardApiOption.foodUpdating;
  config.option.data = param;
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 根据id获取规格
export const fetchFoodDetailById = (id: number): Promise<FoodDetail> => {
  const config = standardApiOption.foodDetailById;
  config.option.params = { id };
  return request(config.url, config.option);
};

// 切换规格状态
export const fetchFoodStatusSwitch = (id: number, status: boolean): Promise<string> => {
  const config = standardApiOption.foodStatusSwitch;
  config.option.data = { id, status: +status as StatusEnum };
  config.option.requestType = 'form';
  return request(config.url, config.option);
};
/**
 * ************************************************************************
 */
/**
 * ************************************************************************
 * 规格相关接口
 */
// 获取规格列表
export const fetchSpecList = (): Promise<Spec[]> => {
  const config = standardApiOption.specList;
  return request(config.url, config.option);
};

// 创建规格
export const fetchSpecCreation = (param: SpecParam): Promise<string> => {
  const config = standardApiOption.specCreation;
  config.option.data = param;
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 更新规格
export const fetchSpecUpdating = (param: SpecParam): Promise<string> => {
  const config = standardApiOption.specUpdating;
  config.option.data = param;
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// // 根据id获取规格
// export const fetchSpecDetailById = (id: number) => {
//   const config = standardApiOption.specDetailById;
//   config.option.params = { id };
//   return request<StandardDetail>(config.url, config.option);
// };

// 切换规格状态
export const fetchSpecStatusSwitch = (id: number, status: boolean): Promise<string> => {
  const config = standardApiOption.specStatusSwitch;
  config.option.data = { specId: id, status: +status as StatusEnum };
  config.option.requestType = 'form';
  return request(config.url, config.option);
};
/**
 * ************************************************************************
 */

/**
 * ************************************************************************
 * 规格值相关接口
 */
// 获取规格值列表
export const fetchSpecAttrList = (specId: number): Promise<SpecAttr[]> => {
  const config = standardApiOption.specAttrList;
  config.option.params = { specId };
  return request<SpecAttr[]>(config.url, config.option);
};

// 创建规格值
export const fetchSpecAttrCreation = (param: SpecAttrParam): Promise<string> => {
  const config = standardApiOption.specAttrCreation;
  config.option.data = param;
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 更新规格值
export const fetchSpecAttrUpdating = (param: SpecAttrParam): Promise<string> => {
  const config = standardApiOption.specAttrUpdating;
  config.option.data = param;
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// // 根据id获取规格值
// export const fetchSpecAttrDetailById = (id: number) => {
//   const config = standardApiOption.specAttrDetailById;
//   config.option.params = { id };
//   return request<StandardDetail>(config.url, config.option);
// };

// 切换规格值状态
export const fetchSpecAttrStatusSwitch = (id: number, status: boolean): Promise<string> => {
  const config = standardApiOption.specAttrStatusSwitch;
  config.option.data = { attrId: id, status: +status as StatusEnum };
  config.option.requestType = 'form';
  return request(config.url, config.option);
};
/**
 * ************************************************************************
 */

/**
 * ************************************************************************
 * 其它相关接口
 */
// 获取宠物类别列表
export const fetchPetTypeList = (): Promise<PetType[]> => {
  const config = standardApiOption.petTypeList;
  return request<Spec[]>(config.url, config.option);
};
/**
 * ************************************************************************
 */
