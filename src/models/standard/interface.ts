import { StatusEnum } from '@/models/common.interface';
/**
 * ************
 * 主粮品牌接口
 */
export interface Brand {
  description: string;
  icon: string;
  id: number;
  index: string;
  name: string;
  region: string;
  status: StatusEnum;
}
export type BrandDetail = Brand;

export interface BrandParam {
  id?: number;
  name: Object;
  icon: string;
  index: Object;
  description: string;
  status: StatusEnum;
}
/**
 * ************
 */
/**
 * ************
 * 主粮接口
 */
export interface Food {
  id: number;
  brandId: number;
  name: string;
  images: string;
  status: StatusEnum;
  description: string;
  detail: string;
  compose: string;
  index: string;
  breed: string;
  brand: Omit<Brand, 'icon' | 'region'> & { images: string };
  component: FoodComponent;
}
export interface FoodComponent {
  digestRate?: number;
  energy?: number;
  ratio?: number;
  protein?: number;
  fat?: number;
  water?: number;
  fibre?: number;
  other?: OtherFoodComponent;
}

export type OtherFoodComponent = OtherFoodComponentParam;

export type FoodDetail = Food;

export interface FoodParam {
  id?: number;
  brandId: number;
  name: string;
  images: string;
  status: StatusEnum;
  description: string;
  detail: string;
  // compose: FoodComponentParam;
  compose: string;
  index: string;
  breed: string;
}
export interface FoodComponentParam {
  digestRate: number;
  energy: number;
  fat: number;
  fibre: number;
  protein: number;
  water: number;
  other?: OtherFoodComponentParam;
}
export interface OtherFoodComponentParam {
  [key: string]: number;
}
/**
 * ************
 */
/**
 * ************
 * 主粮规格接口
 */
// 规格列表数据
export interface Spec {
  id: number;
  name: string;
  status: StatusEnum;
  sortIndex: string;
}
// 规格创建/编辑提交参数
export interface SpecParam {
  id?: number;
  name: string;
  status: StatusEnum;
  sortIndex: string;
}
/**
 * ************
 */
/**
 * ************
 * 主粮规格值接口
 */
export interface SpecAttr {
  id: number;
  name: string;
  sortIndex: number;
  specId: number;
  status: StatusEnum;
}
export interface SpecAttrParam extends Omit<SpecAttr, 'id'> {
  id?: number;
}
/**
 * ************
 */
/**
 * ************
 * 其它接口
 */
export interface PetType {
  id: string;
  name: string;
  status: string;
}
/**
 * ************
 */
