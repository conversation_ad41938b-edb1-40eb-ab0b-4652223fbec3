import { LocaleObject, StatusEnum } from '../common.interface';

export enum TimeUnitEnum {
  DAY = 'd',
  WEEK = 'w',
  MONTH = 'm',
  YEAR = 'y',
}

export interface ScheduleTypeLocalizedProp {
  msg0: LocaleObject;
  msg1: LocaleObject;
  msg2: LocaleObject;
  msg3: LocaleObject;
  name: LocaleObject;
}

// 列表参数数据
export interface ScheduleTypeListParam {
  enable: StatusEnum;
}

// 列表数据
export interface ScheduleType {
  enable: StatusEnum;
  id: string;
  img: string;
  // 是否自定义
  isCustom: StatusEnum;
  name: string;
  priority: number;
  // 关联设备类型： -1为否  0为所有设备  其它值正常展示
  withDeviceType: string;
  // 是否关联宠物
  withPet: StatusEnum;
  scheduleAppoint: string;
  _localizedProps: ScheduleTypeLocalizedProp;
  // 重复提醒
  rpt: string;
  // 默认重复周期
  repeatOption: string;
}

// 创建/编辑提交参数
export interface ScheduleTypeParam {
  id?: string;
  img: string;
  withDeviceType: string;
  withPet: StatusEnum;
  scheduleAppoint: string;
  priority: number;
  isCustom: StatusEnum;
  enable: StatusEnum;
  _localizedProps: ScheduleTypeLocalizedProp;
  rpt: string;
  repeatOption: string;
}

// 详情数据
export type ScheduleTypeDetail = ScheduleType;

export interface ScheduleTypeEnableParam {
  id: number;
  doc: { enable: StatusEnum };
}
