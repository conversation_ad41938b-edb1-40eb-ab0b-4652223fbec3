import { scheduleApiOption } from '@/services/api/schedule';
import request from '@/utils/request';
import {
  ScheduleType,
  ScheduleTypeDetail,
  ScheduleTypeListParam,
  ScheduleTypeParam,
} from './interface';
import { ApiSuccessEnum, StatusEnum } from '../common.interface';

// 获取ScheduleType列表
export const fetchScheduleTypeList = (param?: ScheduleTypeListParam): Promise<ScheduleType[]> => {
  const config = scheduleApiOption.scheduleTypeList;
  config.option.data = param;
  return request(config.url, config.option);
};

// 创建/更新ScheduleType
export const fetchScheduleTypeSaving = (param: ScheduleTypeParam): Promise<ApiSuccessEnum> => {
  const config = scheduleApiOption.scheduleTypeSaving;
  config.option.data = { type: JSON.stringify(param) };
  return request(config.url, config.option);
};

// 根据id获取ScheduleType
export const fetchScheduleTypeDetail = (id: string): Promise<ScheduleTypeDetail> => {
  const config = scheduleApiOption.scheduleTypeDetail;
  config.option.data = { id };
  return request(config.url, config.option);
};

// 切换ScheduleType状态
export const fetchScheduleTypeEnable = (
  id: string,
  enable: StatusEnum,
): Promise<ApiSuccessEnum> => {
  const config = scheduleApiOption.scheduleTypeEnable;
  config.option.data = { id, doc: JSON.stringify({ enable }) };
  return request<never>(config.url, config.option);
};
