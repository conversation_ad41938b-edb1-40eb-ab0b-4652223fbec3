import { packageApiOption } from '@/services/api/package';
import request, { Data } from '@/utils/request';
import { ApiSuccessEnum } from '../common.interface';
import {
  PackageBatch,
  PackageBatchListParam,
  PackageBatchParam,
  PackageGarbage,
  PackageGarbageListParam,
  PackageOperationDetail,
  PackageResetBagBoxParam,
  PackageSn,
  PackageSnListParam,
  PackageUnlockParam,
} from './interface';

// 获取垃圾袋打包批次列表
export const fetchPackageBatchList = (
  param: PackageBatchListParam,
  deviceType: string,
): Promise<Data<PackageBatch>> => {
  const config = packageApiOption.packageBatchList;
  config.option.params = param;
  config.option.restApi = { device: deviceType };
  return request(config.url, config.option);
};

// 创建垃圾袋打包批次
export const fetchPackageBatchCreate = (
  param: PackageBatchParam,
  deviceType: string,
): Promise<void> => {
  const config = packageApiOption.packageBatchCreate;
  config.option.data = param;
  config.option.restApi = { device: deviceType };
  return request(config.url, config.option);
};

// 获取垃圾袋SN列表
export const fetchPackageSnList = (
  param: PackageSnListParam,
  deviceType: string,
): Promise<Data<PackageSn>> => {
  const config = packageApiOption.packageSnList;
  config.option.params = param;
  config.option.restApi = { device: deviceType };
  return request(config.url, config.option);
};

// 根据id获取Package
export const fetchPackageUnlock = (
  param: PackageUnlockParam,
  deviceType: string,
): Promise<ApiSuccessEnum> => {
  const config = packageApiOption.packageUnlock;
  config.option.data = param;
  config.option.restApi = { device: deviceType };
  return request(config.url, config.option);
};

// 获取垃圾袋盒列表
export const fetchPackageGarbageList = (
  param: PackageGarbageListParam,
  deviceType: string,
): Promise<PackageGarbage[]> => {
  const config = packageApiOption.packageGarbageList;
  config.option.data = param;
  config.option.restApi = { device: deviceType };
  return request(config.url, config.option);
};

// 重置垃圾袋盒次数
export const fetchPackageResetBagBox = (
  param: PackageResetBagBoxParam,
  deviceType: string,
): Promise<ApiSuccessEnum> => {
  const config = packageApiOption.packageResetBagBox;
  config.option.data = param;
  config.option.restApi = { device: deviceType };
  return request(config.url, config.option);
};

// 垃圾袋盒操作记录
export const fetchPackageOperationDetails = (
  packageSn: string,
  deviceType: string,
): Promise<PackageOperationDetail[]> => {
  const config = packageApiOption.packageOperationDetails;
  config.option.data = { packageSn };
  config.option.restApi = { device: deviceType };
  return request(config.url, config.option);
};
