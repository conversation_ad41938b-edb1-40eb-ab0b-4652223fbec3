import { PaginatorParam } from '@/utils/request';
import { WifiSearchTypeEnum } from '../device/interface';

// 垃圾袋类型
export enum PackageTypeEnum {
  TYPE1 = 1,
  TYPE2 = 2,
  TYPE3 = 3,
}

// 垃圾袋NFC账号所属区域
export enum PackageRegionEnum {
  CHINA = 0,
  OVERSEA = 1,
}

// 垃圾袋解锁状态
export enum PackageLockEnum {
  UNLOCKED = 0,
  LOCKED = 1,
}

/**
 * 获取垃圾袋批次号列表参数
 */
export interface PackageBatchListParam extends PaginatorParam {
  batchNo?: string;
  orderNo?: string;
  operator?: string;
  // 10位时间戳
  startTime?: number;
  // 10位时间戳
  endTime?: number;
}

/**
 * 获取垃圾袋批次号列表返回值
 */
export interface PackageBatch {
  id: number;
  batchNo: string;
  orderNo: string;
  number: number;
  operator: string;
  // 10位时间戳
  timestamp: number;
}

/**
 * 创建垃圾袋批次参数
 */
export interface PackageBatchParam {
  orderNo: string;
  num: number;
  region: PackageRegionEnum;
  type: PackageTypeEnum;
}

/**
 * 获取垃圾袋SN列表参数
 */
export interface PackageSnListParam extends PaginatorParam {
  sn?: string;
  // secret?: string;
  // used?: number;
  // // 10位时间戳
  // packageStartTime?: number;
  // // 10位时间戳
  // packageEndTime?: number;
  // // 10位时间戳
  // signupStartTime?: number;
  // // 10位时间戳
  // signupEndTime?: number;
  batchNo?: string;
  region?: PackageRegionEnum;
}

/**
 * 获取垃圾袋SN列表返回值
 */
export interface PackageSn {
  packageSn: string;
  packageSecret: string;
  batchNo: string;
  used: number;
  // 10位时间戳
  baggingTime: number;
  // 10位时间戳
  signTime: number;
  // 解锁状态
  lock: PackageLockEnum;
  region: PackageRegionEnum;
}

// SN解锁参数
export type PackageUnlockParam = {
  sn: string;
  secret: string;
};

/**
 * =======================
 * 垃圾袋盒相关接口
 */
export interface PackageGarbageListParam {
  s?: string;
  packageSn?: string;
  type?: WifiSearchTypeEnum;
}

export interface PackageGarbage {
  userId: number;
  deviceSn: string;
  deviceMac: string;
  garbageSn: string;
  remainingTimes: number;
  opetateTimes: number;
}

export interface PackageResetBagBoxParam {
  operateReason: string;
  packageSn: string;
  deviceSn: string;
}

export interface PackageOperationDetail {
  operateTime: number;
  operateWay: string;
  operateReason: string;
  operater: string;
}
/**
 * =======================
 */
