import { PaginatorParam } from '@/utils/request';
import { LocaleObject, StatusEnum } from '../common.interface';

export enum TipMediaTypeEnum {
  OLD = -1,
  IMAGE = 0,
  VIDEO = 1,
}

// 列表参数数据
export interface SystemLicenseListParam extends PaginatorParam {
  pid?: string;
}

// 列表数据
export interface SystemLicense {
  count: number;
  createdAt: number;
  id: number;
  pid: string;
  remind: number;
  updatedAt: string;
  used: number;
}

// 创建/编辑提交参数
export interface SystemLicenseParam {
  id?: number;
  pid: string;
  count: number;
  used?: number;
  remind?: number;
}

/**
 * ================================
 * 系统提示信息 - start
 */
export enum TipInfoExportTypeEnum {
  TIPS = 'tips',
  TIPSURL = 'tipsUrl',
}

export interface SystemTipInfoListParam extends PaginatorParam {
  keyId?: string;
  enable?: StatusEnum;
}
export interface SystemTipInfo {
  id: number;
  enable: StatusEnum;
  annotation: string;
  keyId: string;
  tipDesc: string;
  url: string;
}

export interface SystemTipInfoEnableParam {
  id: number;
  doc: string;
}

export interface SystemTipInfoParam {
  id?: number;
  keyId: string;
  annotation: string;
  enable: StatusEnum;
  url: string;
  tipDesc: string;
  mediaType: TipMediaTypeEnum;
}

export interface SystemTipInfoDetail {
  id: number;
  keyId: string;
  annotation: string;
  enable: StatusEnum;
  mediaType: TipMediaTypeEnum;
  localizedProps: {
    tipDesc: LocaleObject;
    url: LocaleObject;
  };
}

// 导出参数
export interface SystemTipInfoExportParam {
  bundle: TipInfoExportTypeEnum;
}

/**
 * 系统提示信息 - end
 * ================================
 */
