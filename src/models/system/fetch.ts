import request, { Data } from '@/utils/request';
import { ApiSuccessEnum } from '../common.interface';
import { systemApiOption } from './api';
import {
  SystemLicense,
  SystemLicenseListParam,
  SystemLicenseParam,
  SystemTipInfo,
  SystemTipInfoDetail,
  SystemTipInfoEnableParam,
  SystemTipInfoExportParam,
  SystemTipInfoListParam,
  SystemTipInfoParam,
} from './interface';

// 获取声网批次列表
export const fetchSystemLicenseList = (
  param?: SystemLicenseListParam,
): Promise<Data<SystemLicense>> => {
  const config = systemApiOption.systemLicenseList;
  if (param) config.option.params = param;
  return request(config.url, config.option);
};

// 创建声网批次
export const fetchSystemLicenseCreation = (
  param: SystemLicenseParam,
): Promise<void> => {
  const config = systemApiOption.systemLicenseCreation;
  config.option.data = param;
  return request(config.url, config.option);
};

// 更新声网批次
export const fetchSystemLicenseUpdate = (
  param: SystemLicenseParam,
): Promise<void> => {
  const config = systemApiOption.systemLicenseUpdate;
  config.option.data = param;
  return request(config.url, config.option);
};

// 获取系统提示信息列表
export const fetchSystemTipInfoList = (
  param: SystemTipInfoListParam,
): Promise<Data<SystemTipInfo>> => {
  const config = systemApiOption.systemTipInfoList;
  config.option.data = param;
  return request(config.url, config.option);
};

// 获取系统提示信息详情
export const fetchSystemTipInfoDetail = (
  id: number,
): Promise<SystemTipInfoDetail> => {
  const config = systemApiOption.systemTipInfoDetail;
  config.option.data = { id };
  return request(config.url, config.option);
};

// 编辑系统提示信息
export const fetchSystemTipInfoEdition = (
  param: SystemTipInfoParam,
): Promise<ApiSuccessEnum> => {
  const config = systemApiOption.systemTipInfoEdition;
  config.option.data = param;
  return request(config.url, config.option);
};
// 启用/禁用系统提示信息
export const fetchSystemTipInfoEnable = (
  param: SystemTipInfoEnableParam,
): Promise<ApiSuccessEnum> => {
  const config = systemApiOption.systemTipInfoEnable;
  config.option.data = param;
  return request(config.url, config.option);
};
// 删除系统提示信息
export const fetchSystemTipInfoDeletion = (
  id: number,
): Promise<ApiSuccessEnum> => {
  const config = systemApiOption.systemTipInfoDeletion;
  config.option.data = { id };
  return request(config.url, config.option);
};

export const getSystemTipInfoExportUrl = (param: SystemTipInfoExportParam) => {
  const config = systemApiOption.systemTipInfoExport;
  const urlParam = `X-Admin-Session=${localStorage.getItem(
    'sessionToken',
  )}&bundle=${param.bundle}`;
  const url = `${config.url}?${urlParam}`;
  return url;
};
