import { RequestMethod, RequestOption } from '@mantas/request';
import { defaultOptions } from '../../services/api/util';

export const systemApiOption: Record<
  // 声网批次相关
  | 'systemLicenseList'
  | 'systemLicenseCreation'
  | 'systemLicenseUpdate'
  // 系统 - 提示相关
  | 'systemTipInfoList'
  | 'systemTipInfoDetail'
  | 'systemTipInfoEdition'
  | 'systemTipInfoEnable'
  | 'systemTipInfoDeletion'
  | 'systemTipInfoExport',
  RequestOption
> = {
  // 获取声网批次列表
  systemLicenseList: {
    url: '/adm/system/getLicense',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 创建声网批次
  systemLicenseCreation: {
    url: '/adm/system/saveLicense',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 更新声网批次
  systemLicenseUpdate: {
    url: '/adm/system/updateLicense',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 获取系统提示信息列表
  systemTipInfoList: {
    url: '/adm/system/tipList',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 获取系统提示信息详情
  systemTipInfoDetail: {
    url: '/adm/system/findTipInfo',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 编辑系统提示信息
  systemTipInfoEdition: {
    url: '/adm/system/tipInfo',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 启用/禁用系统提示信息
  systemTipInfoEnable: {
    url: '/adm/system/enableTipInfo',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 删除系统提示信息
  systemTipInfoDeletion: {
    url: '/adm/system/deleteTipInfo',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 导出系统提示信息
  systemTipInfoExport: {
    url: '/adm/system/localebundle_export',
    option: {
      method: RequestMethod.Get,
    },
  },
};
