import { AppState } from './app/model';
import { AdminState } from './admin/model';
import { StandardState } from './standard/model';
import { TagState } from './tag/model';

export interface Route {
  /**
   * Any valid URL path
   */
  path?: string;
  /**
   * A React component to render only when the location matches.
   */
  component?: string | (() => any);
  wrappers?: string[];
  /**
   * navigate to a new location
   */
  redirect?: string;
  /**
   * When true, the active class/style will only be applied if the location is matched exactly.
   */
  exact?: boolean;
  routes?: Route[];
  [k: string]: any;
}

export interface Loading {
  global: boolean;
  effects: { [key: string]: boolean | undefined };
  models: {
    global?: boolean;
    menu?: boolean;
    setting?: boolean;
    user?: boolean;
    register?: boolean;
    login?: boolean;
  };
}

export interface ConnectState {
  loading: Loading;
  tag: TagState;
  standard: StandardState;
  app: AppState;
  admin: AdminState;
}
