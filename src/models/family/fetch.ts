import { FamilyGroupListParam, FamilyGroup } from './interface';
import { familyApiOption } from '@/services/api/family';
import request, { Data } from '@/utils/request';

// 获取Family列表
export const fetchFamilyList = (param: FamilyGroupListParam): Promise<Data<FamilyGroup>> => {
  const config = familyApiOption.familyGroupList;
  config.option.params = param;
  return request(config.url, config.option);
};

// 根据用户Id获取家庭组列表
export const fetchFamilyGroupListByUserId = (userId: string): Promise<FamilyGroup[]> => {
  const config = familyApiOption.familyGroupListByUserId;
  config.option.params = { userId };
  return request(config.url, config.option);
};

// 根据家庭组id获取宠物列表
export const fetchFamilyPetList = (groupId: number) => {
  const config = familyApiOption.familyPetList;
  config.option.params = { groupId };
  return request(config.url, config.option);
};
// 根据家庭组id获取设备列表
export const fetchFamilyDeviceList = (groupId: number) => {
  const config = familyApiOption.familyDeviceList;
  config.option.params = { groupId };
  return request(config.url, config.option);
};
// 根据家庭组id获取用户列表
export const fetchFamilyUserList = (groupId: number) => {
  const config = familyApiOption.familyUserList;
  config.option.params = { groupId };
  return request(config.url, config.option);
};
