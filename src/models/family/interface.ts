import { PaginatorParam } from '@/utils/request';
// 列表参数数据
export interface FamilyGroupListParam extends PaginatorParam {
  groupId?: number;
  name?: string;
}

// 列表数据
export interface FamilyGroup {
  groupId: number;
  name: string;
  owner: number;
  userCount: number;
  deviceCount: number;
  petCount: number;
}

// 创建/编辑提交参数
export interface FamilyParam {}

// 详情数据
export interface FamilyDetail {}
