// 请求方法枚举
export enum RequestMethod {
  Get = 'get',
  Post = 'post',
  Put = 'put',
  Delete = 'delete',
  Patch = 'patch',
  Head = 'head',
  Options = 'options',
}

// 蓝牙设备枚举
export enum BlueToothDeviceEnum {
  W5 = 'w5',
  HG = 'hg',
  CTW3 = 'ctw3',
}

// WiFi设备枚举
export enum WIFIDeviceEnum {
  D3 = 'd3',
  D4 = 'd4',
  D4S = 'd4s',
  D4SH = 'd4sh',
  T3 = 't3',
  T4 = 't4',
  K2 = 'k2',
  AQH1 = 'aqh1',
  H3 = 'h3',
}
/**
 * @enum 应用平台枚举
 * 1 iOS
 * 2 Android
 */
export enum DevicePlatformEnum {
  iOS = 1,
  Android = 2,
}

/**
 * @enum wifi设备的pim状态枚举
 */
export enum WifiPimStatusEnum {
  OFFLINE = 0,
  ONLINE = 1,
  BATTERY = 2,
}

/**
 * @enum 设备代号
 */
export enum DeviceCodeEnum {
  BB = 'BB',
  BA = 'BA',
}

/**
 * @enum 多媒体资源类型
 */
export enum MediaTypeEnum {
  VIDEO = 1,
  IMAGE = 2,
}

/**
 * @enum 布尔值枚举
 */
export enum BooleanEnum {
  FALSE = 0,
  TRUE = 1,
}
