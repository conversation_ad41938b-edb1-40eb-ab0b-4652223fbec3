import { BooleanEnum } from '@/models/common.enum';
import { PaginatorParam } from '@/utils/request';
import { SandTrayRegionEnum, WifiSearchTypeEnum } from '../device/interface';
import { PackageRegionEnum } from '../package/interface';

export enum SandTrayTypeEnum {
  NORMAL = 0,
  PH_CHECK = 1,
}

export enum VersionCodeEnum {
  CB = 'CB',
  CA = 'CA',
}

export const VersionCodeNameMap = {
  [VersionCodeEnum.CB]: 'CB(国内代号)',
  [VersionCodeEnum.CA]: 'CA(海外代号)',
};

// 管理列表参数数据
export interface SandTrayManagementListParam extends PaginatorParam {
  batchNo?: string;
  orderNo?: string;
  operator?: string;
  startTime?: number;
  endTime?: number;
}

// 列表参数数据
export interface SandTrayListParam {
  s?: string;
  sandTraySn?: string;
  type?: WifiSearchTypeEnum.SN | WifiSearchTypeEnum.ID;
}

// 列表数据
export interface SandTrayManagement {
  id: number;
  batchNo: string;
  orderNo: string;
  number: number;
  operator: string;
  timestamp: number;
}

// 创建/编辑提交参数
export interface SandTrayManagementParam {
  orderNo: string;
  num: number;
  type: SandTrayTypeEnum;
  // versionCode: VersionCodeEnum;
  region: PackageRegionEnum;
}

// 水晶猫砂盘查询页面列表参数
export interface SandTraySnListParam extends PaginatorParam {
  sn?: string;
  batchNo?: string;
}

export interface SandTraySn {
  sandTraySn: string;
  sandTraySecret: string;
  batchNo: string;
  used: number;
  region: SandTrayRegionEnum;
  lock: BooleanEnum;
}

// T7设备相关水晶猫砂盘列表数据
export interface SandTray {
  userId: string;
  deviceSn: string;
  deviceMac: string;
  sandTraySn: string;
  remainingTimes: number;
  opetateTimes: number;
  standardTime: number;
}

// 创建/编辑提交参数
export interface SandTrayResetTimeParam {
  operateReason: string;
  sandTray: string;
  deviceSn: string;
}

// 详情数据
export interface SandTrayOperationDetail {
  operateTime: number;
  operateWay: string;
  operateReason: string;
  operater: string;
}
