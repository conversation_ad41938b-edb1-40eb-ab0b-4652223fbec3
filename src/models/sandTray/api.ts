import { defaultOptions } from '@/services/api/util';
import { RequestMethod, RequestOption } from '@mantas/request';

export const sandTrayApiOption: Record<
  | 'sandTrayList'
  | 'sandTrayRegister'
  | 'sandTrayExport'
  | 'sandTraySnList'
  | 'sandTrayManagementList'
  | 'sandTrayResetTime'
  | 'sandTrayOperationDetails',
  RequestOption
> = {
  // 获取猫砂盘列表
  sandTrayList: {
    url: '/adm/:device/sandTray/manageList',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  // 导出水晶猫砂盘
  sandTrayExport: {
    url: '/adm/:device/sandTray/excel/list',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 操作详情
  sandTrayOperationDetails: {
    url: '/adm/:device/sandTray/operationDetails',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 重置次数
  sandTrayResetTime: {
    url: '/adm/:device/sandTray/resetTime',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },

  // 获取猫砂盘管理列表
  sandTrayManagementList: {
    url: '/adm/:device/sandTray/list',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 注册水晶猫砂盘
  sandTrayRegister: {
    url: '/adm/:device/sandTray/create',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 水晶猫砂盘SN batch 列表
  sandTraySnList: {
    url: '/adm/:device/sandTray/batch/list',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
};
