import { getCurrentPrefix } from '@/utils/currentPrefix';
import request, { Data } from '@/utils/request';
import { ApiSuccessEnum } from '../common.interface';
import { sandTrayApiOption } from './api';
import {
  SandTray,
  SandTrayListParam,
  SandTrayManagement,
  SandTrayManagementListParam,
  SandTrayManagementParam,
  SandTrayOperationDetail,
  SandTrayResetTimeParam,
  SandTraySn,
  SandTraySnListParam,
} from './interface';

// 获取SandTray管理列表
export const fetchSandManagementTrayList = (
  param: SandTrayManagementListParam,
  type: string,
): Promise<Data<SandTrayManagement>> => {
  const config = sandTrayApiOption.sandTrayManagementList;
  if (param) {
    config.option.params = param;
  }
  config.option.restApi = { device: type };
  return request(config.url, config.option);
};

// 注册SandTray
export const fetchSandTrayRegister = (
  param: SandTrayManagementParam,
  type: string,
): Promise<ApiSuccessEnum> => {
  const config = sandTrayApiOption.sandTrayRegister;
  config.option.data = param;
  config.option.restApi = { device: type };
  return request(config.url, config.option);
};

// 获取水晶猫砂盘SN列表
export const fetchSandTraySnList = (
  param: SandTraySnListParam,
  type: string,
): Promise<Data<SandTraySn>> => {
  const config = sandTrayApiOption.sandTraySnList;
  config.option.params = param;
  config.option.restApi = { device: type };
  return request(config.url, config.option);
};

// 获取SandTray列表
export const fetchSandTrayList = (
  param: SandTrayListParam,
  type: string,
): Promise<SandTray[]> => {
  const config = sandTrayApiOption.sandTrayList;
  config.option.params = param;
  config.option.restApi = { device: type };
  return request(config.url, config.option);
};

// 导出SandTray
export const getSandTrayExportUrl = (
  batchNo: string,
  orderNo: string,
  type: string,
): string => {
  const config = sandTrayApiOption.sandTrayExport;

  const url = `${window.origin}${getCurrentPrefix()}${config.url.replace(
    ':device',
    type,
  )}?X-Admin-Session=${localStorage.getItem(
    'sessionToken',
  )}&batchNo=${batchNo}&orderNo=${orderNo}`;

  return url;
};

// 重置水晶猫砂盘
export const fetchSandTrayResetTime = (
  param: SandTrayResetTimeParam,
  type: string,
): Promise<ApiSuccessEnum> => {
  const config = sandTrayApiOption.sandTrayResetTime;
  config.option.data = param;
  config.option.restApi = { device: type };
  return request(config.url, config.option);
};

// 获取水晶猫砂盘操作详情
export const fetchSandTrayOperationDetails = (
  sandTraySn: string,
  type: string,
): Promise<SandTrayOperationDetail[]> => {
  const config = sandTrayApiOption.sandTrayOperationDetails;
  config.option.data = { sandTraySn };
  config.option.restApi = { device: type };
  return request(config.url, config.option);
};
