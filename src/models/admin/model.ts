import { TreeNode } from '@/models/common.interface';

import { Reducer } from '@umijs/max';
import { MenuPermission } from './interface';
import { getMenuPermissionInfoTree } from './util';

export interface AdminState {
  menuPermissionTree: TreeNode[];
}

export const initAdminState: AdminState = {
  menuPermissionTree: [],
};

export interface AdminModel {
  namespace: 'admin';
  state: AdminState;
  // effects: {
  // };
  reducers: {
    getMenuPermissionTree: Reducer<
      AdminState,
      { type: 'getMenuPermissionTree' }
    >;
  };
}

const adminModel: AdminModel = {
  namespace: 'admin',
  state: initAdminState,
  // effects: {
  // },
  reducers: {
    getMenuPermissionTree(state = initAdminState): AdminState {
      let menuPermissionTree: TreeNode[] = [];
      const menuTreeStr = localStorage.getItem('menuTree') || '{}';
      const menuNameListStr = localStorage.getItem('menuNameList') || '{}';
      try {
        const menuTree: MenuPermission[] = JSON.parse(menuTreeStr);
        const menuNameList: { [key: string]: string } =
          JSON.parse(menuNameListStr);
        menuPermissionTree = [
          {
            title: '全部权限',
            value: 'allPermission',
            key: 'allPermission',
            children: getMenuPermissionInfoTree(menuTree, menuNameList),
            nativeTitle: '全部',
            nativePermission: 'allPermission',
          },
        ];
      } catch (error) {
        console.log(error);
      }

      return {
        ...state,
        menuPermissionTree,
      };
    },
  },
};

export default adminModel;
