import { PaginatorParam } from '@/utils/request';

// 列表参数数据
export type AdminGroupListParam = PaginatorParam;

// 列表数据
export interface AdminGroup {
  id: number;
  name: string;
  permissions: string;
  root: boolean;
}

// 创建/编辑提交参数
export interface AdminGroupParam {
  id?: number;
  name: string;
  root: boolean;
  permissions: string;
}

// 详情数据
export type AdminGroupDetail = AdminGroup;

export interface Permission {
  key: string;
  name: string;
  permissions: Permission[];
}

// petkit-oms配置的菜单数据结构
export interface MenuPermission {
  title: string;
  permission: string;
  href: string;
  children: MenuPermission[];
}
