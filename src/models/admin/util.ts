import { TreeNode } from '@/models/common.interface';
import { initPaginatorParam } from '@/utils/request';
import { AdminGroupListParam, AdminGroupParam, MenuPermission } from './interface';

export const initAdminGroupListParam: AdminGroupListParam = {
  ...initPaginatorParam,
  limit: 20,
};

export const initAdminParam: AdminGroupParam = {
  name: '',
  root: false,
  permissions: '',
};

let permissionRecordList: Set<string> = new Set();

export const getMenuPermissionInfoTree = (
  menuTree: MenuPermission[],
  menuNameList: { [key: string]: string },
  resetPermissionRecordList = true,
): TreeNode[] => {
  if (resetPermissionRecordList) permissionRecordList = new Set();

  // 两种情况：
  // 1.可能不存在permission
  // 2.存在相同的permission
  const tree: TreeNode[] = menuTree.map((node) => {
    // if (!node.permission) {
    //   debugger;
    // }
    const _node = {
      value: '',
      key: '',
      // key: node.permission ? `${node.permission}-${node.title}` : node.title,
      title: menuNameList[node.title],
      children: node.children ? getMenuPermissionInfoTree(node.children, menuNameList, false) : [],
      nativeTitle: node.title,
      nativePermission: node.permission,
    };
    let key = '';
    if (node.permission && node.permission.indexOf('/') === 0) {
      key = node.permission.substring(1);
    } else if (!node.permission && node.href.indexOf('/') === 0) {
      key = node.href.substring(1);
    } else {
      key = node.permission || node.href;
    }

    if (permissionRecordList.has(key)) {
      key = `${key}-${node.title}`;
    }

    permissionRecordList.add(key);
    _node.key = key;
    return _node;
  });
  // console.log(tree);
  return tree;
};
