import {
  AdminGroup,
  AdminGroupDetail,
  AdminGroupListParam,
  AdminGroupParam,
  Permission,
} from './interface';
import { adminApiOption } from '@/services/api/admin';
import request, { Data } from '@/utils/request';

// 获取Admin列表
export const fetchAdminGroupList = (param?: AdminGroupListParam): Promise<Data<AdminGroup>> => {
  const config = adminApiOption.adminGroupList;
  param && (config.option.data = param);
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 创建/编辑Admin
export const fetchAdminGroupSaving = (param: AdminGroupParam) => {
  const config = adminApiOption.adminGroupSaving;
  config.option.data = param;
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 权限列表
export const fetchPermissionList = (): Promise<Permission[]> => {
  const config = adminApiOption.permissionList;
  return request(config.url, config.option);
};

// admin group详情
export const fetchAdminGroupDetail = (id: number): Promise<AdminGroupDetail> => {
  const config = adminApiOption.adminGroup;
  config.option.data = { id };
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 删除管理组
export const fetchAdminGroupDeleting = (id: number): Promise<void> => {
  const config = adminApiOption.adminGroupDeleting;
  config.option.data = { id };
  config.option.requestType = 'form';
  return request(config.url, config.option);
};
