import { PaginatorParam } from '@/utils/request';
import { AwardDurationUnitEnum } from '../aiCreation/interface';
import { DeviceTypeEnum, StatusEnum } from '../common.interface';

export enum MaterialsRecordTypeEnum {
  Video = 1,
  Image = 2,
}

// 列表数据
export interface Materials {
  id: number;
  deviceType: number;
  participateCount: number;
  reciveCount: number;
  videoCount: number;
  awardId: number;
  name: string;
  disable: number;
  matterId: number;
}

// 创建/编辑提交参数
export interface MaterialsParam {
  id: number;
  info: {
    title: string;
    tagList: { tagName: string }[];
  }[];
}

export interface MaterialsDetail {
  id: number;
  info: { title: string; tagList: { tagName: string }[] }[];
}

export interface MaterialsRecordListParam extends PaginatorParam {
  matterId: number;
  userId?: number;
  deviceId?: number;
  deviceType?: DeviceTypeEnum;
  publishStart?: number;
  publishEnd?: number;
}

// 记录列表数据
export interface MaterialsRecord {
  id: number;
  userId: number;
  deviceId: number;
  deviceType: DeviceTypeEnum;
  publishTime: number;
  eventId?: string;
  combineKey?: string;
  type: MaterialsRecordTypeEnum;
  picUrl?: string;
  aesKey?: string;
}

// 素材奖励
export interface MaterialsAwardListParam extends PaginatorParam {
  awardId?: number;
  deviceType?: DeviceTypeEnum;
  disable?: StatusEnum;
  startTime?: number;
  endTime?: number;
  createStartTime?: number;
  createEndTime?: number;
}

export interface MaterialsAward {
  id: number;
  deviceType: number;
  createTime: number;
  startTime: number;
  endTime: number;
  disable: StatusEnum;
}

export interface MaterialsAwardDetail {
  id: number;
  deviceType: DeviceTypeEnum;
  startTime: number;
  endTime: number;
  threshold: number;
  rule: string;
  disable: StatusEnum;
  award: {
    planId: number;
    planName: string;
    duration: number;
    unit: AwardDurationUnitEnum;
    bsAwardId: number;
  };
}

export interface MaterialsAwardAwardParam {
  planId: number;
  planName: string;
  duration: number;
  unit: AwardDurationUnitEnum;
  bsAwardId?: number;
}
export interface MaterialsAwardParam {
  id?: number;
  deviceType: DeviceTypeEnum;
  startTime: number;
  endTime: number;
  threshold: number;
  rule: string;
  award: string;
}

/**
 * ******************************************
 * @description M3u8视频播放相关
 */
// 获取m3u8播放地址参数
export interface M3u8InfoParam {
  eventId: string;
  combineKey: string;
}
/**
 * ******************************************
 */

export interface MaterialDisableParam {
  id: number;
  disable: StatusEnum;
}
