import { defaultOptions } from '@/services/api/util';
import { RequestMethod, RequestOption } from '@mantas/request';

export const matterApiOption: Record<
  | 'materialsList'
  | 'materialsEdit'
  | 'materialsDetail'
  | 'materialsRecordList'
  | 'materialsExport'
  | 'materialsAwardList'
  | 'materialsAwardCreation'
  | 'materialsAwardUpdate'
  | 'materialsAwardDetail'
  | 'materialsAwardStatusUpdate'
  | 'm3u8VideoInfo'
  | 'materialDisable',
  RequestOption
> = {
  // 获取Materials列表
  materialsList: {
    url: '/adm/matter/list',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 创建Materials
  materialsEdit: {
    url: '/adm/matter/tag/edit',
    option: {
      ...defaultOptions,
      headers: {
        'Content-Type': 'application/json',
        // 'x-api-version': global.apiVersion,
      },
      requestType: 'json',
      method: RequestMethod.Post,
    },
  },
  // Materials详情
  materialsDetail: {
    url: '/adm/matter/tag/detail',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // Materials记录列表
  materialsRecordList: {
    url: '/adm/matter/detail/list',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // Materials导出
  materialsExport: {
    url: '/adm/matter/detail/export',
    option: { ...defaultOptions, method: RequestMethod.Post },
  },
  // 列表素材奖励
  materialsAwardList: {
    url: '/adm/matter/award/list',
    option: { ...defaultOptions, method: RequestMethod.Post },
  },
  // 素材奖励创建
  materialsAwardCreation: {
    url: '/adm/matter/award/create',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
      // headers: {
      //   'Content-Type': 'application/json',
      //   // 'x-api-version': global.apiVersion,
      // },
      // requestType: 'json',
    },
  },
  // 素材奖励更新
  materialsAwardUpdate: {
    url: '/adm/matter/award/edit',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
      // headers: {
      //   'Content-Type': 'application/json',
      //   // 'x-api-version': global.apiVersion,
      // },
      // requestType: 'json',
    },
  },
  // 素材奖励详情
  materialsAwardDetail: {
    url: '/adm/matter/award/detail',
    option: { ...defaultOptions, method: RequestMethod.Post },
  },
  materialsAwardStatusUpdate: {
    url: '/adm/matter/award/disable',
    option: { ...defaultOptions, method: RequestMethod.Post },
  },
  // M3U8视频数据
  m3u8VideoInfo: {
    url: '/adm/matter/:device/getM3u8ForAdm',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
      requestType: 'form',
    },
  },
  materialDisable: {
    url: '/adm/matter/list/disable',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    }
  }
};
