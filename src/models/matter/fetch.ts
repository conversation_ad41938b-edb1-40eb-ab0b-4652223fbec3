import { getCurrentPrefix } from '@/utils/currentPrefix';
import request, { Data } from '@/utils/request';
import { ApiSuccessEnum, StatusEnum } from '../common.interface';
import { matterApiOption } from './api';
import {
  M3u8InfoParam,
  MaterialDisableParam,
  Materials,
  MaterialsAward,
  MaterialsAwardDetail,
  MaterialsAwardListParam,
  MaterialsAwardParam,
  MaterialsDetail,
  MaterialsParam,
  MaterialsRecord,
  MaterialsRecordListParam,
} from './interface';

// 获取Material列表
export const fetchMaterialsList = (): Promise<Materials[]> => {
  const config = matterApiOption.materialsList;
  return request(config.url, config.option);
};

// 编辑Material
export const fetchMaterialsEdit = (
  param: MaterialsParam,
): Promise<ApiSuccessEnum> => {
  const config = matterApiOption.materialsEdit;
  config.option.data = param;
  return request(config.url, config.option);
};

// Material详情
export const fetchMaterialsDetail = (id: number): Promise<MaterialsDetail> => {
  const config = matterApiOption.materialsDetail;
  config.option.params = { id };
  return request(config.url, config.option);
};

// Materials记录列表
export const fetchMaterialsRecordList = (
  param: MaterialsRecordListParam,
): Promise<Data<MaterialsRecord>> => {
  const config = matterApiOption.materialsRecordList;
  config.option.params = param;
  return request(config.url, config.option);
};

// Materials导出
export const getMaterialsExportUrl = (): string => {
  const config = matterApiOption.materialsExport;
  return config.url;
};

// 列表素材奖励
export const fetchMaterialsAwardList = (
  param: MaterialsAwardListParam,
): Promise<Data<MaterialsAward>> => {
  const config = matterApiOption.materialsAwardList;
  config.option.params = param;
  return request<never>(config.url, config.option);
};
// 素材奖励创建
export const fetchMaterialsAwardCreation = (
  param: MaterialsAwardParam,
): Promise<ApiSuccessEnum> => {
  const config = matterApiOption.materialsAwardCreation;
  config.option.data = param;
  return request<never>(config.url, config.option);
};
// 素材奖励更新
export const fetchMaterialsAwardUpdate = (
  param: MaterialsAwardParam,
): Promise<ApiSuccessEnum> => {
  const config = matterApiOption.materialsAwardUpdate;
  config.option.data = param;
  return request<never>(config.url, config.option);
};
// 素材奖励详情
export const fetchMaterialsAwardDetail = (
  id: number,
): Promise<MaterialsAwardDetail> => {
  const config = matterApiOption.materialsAwardDetail;
  config.option.params = { awardId: id };
  return request<never>(config.url, config.option);
};

// 切换奖励状态
export const fetchMaterialsAwardStatusUpdate = (
  id: number,
  disable: StatusEnum,
): Promise<ApiSuccessEnum> => {
  const config = matterApiOption.materialsAwardStatusUpdate;
  config.option.params = { awardId: id, disable };
  return request<never>(config.url, config.option);
};

// 获取m3u8播放地址
export const getM3u8FileUrl = (param: M3u8InfoParam, device: string) => {
  const config = matterApiOption.m3u8VideoInfo;
  const url = `${getCurrentPrefix()}${config.url.replace(
    ':device',
    device.toLowerCase(),
  )}?X-Admin-Session=${localStorage.getItem('sessionToken')}&eventId=${
    param.eventId
  }&combineKey=${param.combineKey}`;
  return url;
};

// 上传素材上下架
export const fetchMaterialDisable = (param: MaterialDisableParam): Promise<ApiSuccessEnum> => {
  const config = matterApiOption.materialDisable;
  config.option.data = param;
  return request(config.url, config.option);
}
