import {
  LogicSpecInfo,
  LoginSpecParam,
  SecondarySpecInfo,
  SpecInfo,
  UserGroup,
  UserGroupDetail,
  UserGroupListParam,
  UserGroupParam,
} from './interface';
// 创建用户分群

import { tagApiOption } from '@/services/api/tag';
import request, { Data } from '@/utils/request';
import { StatusEnum } from '../common.interface';

// userGroupCreation
export const fetchCreateUserGroup = (
  param: UserGroupParam,
): Promise<string> => {
  const config = tagApiOption.userGroupCreation;
  config.option.data = {
    ...param,
    logicInfo: JSON.stringify(param.logicInfo),
  };
  config.option.requestType = 'form';
  return request(config.url, config.option);
};
// 编辑用户分群
// userGroupUpdating
export const fetchUpdateUserGroup = (
  param: UserGroupParam,
): Promise<string> => {
  const config = tagApiOption.userGroupUpdating;
  config.option.data = {
    ...param,
    logicInfo: JSON.stringify(param.logicInfo),
  };
  config.option.requestType = 'form';
  return request(config.url, config.option);
};
// 获取用户分群列表
// userGroupList
export const fetchUserGroupList = (
  param?: UserGroupListParam,
): Promise<Data<UserGroup>> => {
  const config = tagApiOption.userGroupList;
  if (param) config.option.params = param;
  return request(config.url, config.option);
};
// 快速启用/禁用用户分群
// switchStatus
export const fetchSwitchUserGroup = (
  id: number,
  status: StatusEnum,
): Promise<void> => {
  const config = tagApiOption.switchStatus;
  config.option.params = { tagId: id, enable: status };
  return request(config.url, config.option);
};
// 获取用户分群详情
// userGroupDetail
export const fetchUserGroupDetail = (id: number): Promise<UserGroupDetail> => {
  const config = tagApiOption.userGroupDetail;
  config.option.params = { tagId: id };
  return request(config.url, config.option);
};
// 逻辑条件数据
// logicConditionList
export const fetchLogicCondition = (
  param: LoginSpecParam,
): Promise<LogicSpecInfo[]> => {
  const config = tagApiOption.logicConditionList;
  config.option.params = { ...param };
  return request(config.url, config.option);
};
// 一级属性数据
// firstLevelCondition
export const fetchFirstLevelCondition = (): Promise<SpecInfo[]> => {
  const config = tagApiOption.firstLevelCondition;
  return request(config.url, config.option);
};
// 二级属性数据
// secondLevelCondition
export const fetchSecondLevelCondition = (
  id: number,
): Promise<SecondarySpecInfo[]> => {
  const config = tagApiOption.secondLevelCondition;
  config.option.params = { id };
  return request(config.url, config.option);
};
// 列表页面获取预估人数
// estimatePeopleCountByTagId
export const fetchEstimatePeopleCountByTagId = (
  id: number,
): Promise<number> => {
  const config = tagApiOption.estimatePeopleCountByTagId;
  config.option.params = { tagId: id };
  config.option.timeout = 30000;
  return request(config.url, config.option);
};
