import { CascaderOption, SelectOption } from '@/models/common.interface';
import { Effect, Reducer } from '@umijs/max';
import { fetchFirstLevelCondition } from './fetch';
import { SecondarySpecInfo, SpecInfo } from './interface';

export interface TagState {
  dimensionList: SpecInfo[];
  dimensionOptions: SelectOption[];
  dimensionValueList: SecondarySpecInfo[];
  dimensionValueOptionsByDimensionId: Partial<{
    [key: number]: CascaderOption[];
  }>;
}

export const initTagState: TagState = {
  dimensionList: [],
  dimensionOptions: [],
  dimensionValueList: [],
  dimensionValueOptionsByDimensionId: {},
};

export interface TagModel {
  namespace: 'tag';
  state: TagState;
  effects: {
    // requestTagList: Effect;
    requestDimensionAndValueList: Effect;
  };
  reducers: {
    requestDimensionAndValueListSuccess: Reducer<
      TagState,
      { type: 'requestDimensionAndValueListSuccess'; payload: SpecInfo[] }
    >;
  };
}

const tagModel: TagModel = {
  namespace: 'tag',
  state: initTagState,
  effects: {
    *requestDimensionAndValueList(action, { call, put }) {
      const dimensionList: SpecInfo[] = yield call(fetchFirstLevelCondition);
      yield put({
        type: 'requestDimensionAndValueListSuccess',
        payload: dimensionList,
      });
    },
  },
  reducers: {
    requestDimensionAndValueListSuccess(
      state = initTagState,
      { payload },
    ): TagState {
      let dimensionValueList: SecondarySpecInfo[] = [];
      const dimensionValueOptionsByDimensionId: Partial<{
        [key: number]: CascaderOption[];
      }> = {};
      const dimensionOptions: SelectOption[] = [];
      payload.forEach((dimension) => {
        dimensionOptions.push({
          label: dimension.spec,
          value: dimension.id,
          disabled: true,
        });
        dimensionValueList = [...dimensionValueList, ...dimension.children];
        dimensionValueOptionsByDimensionId[dimension.id] =
          dimension.children.map<CascaderOption>((item) => ({
            label: item.detailName,
            value: item.id,
            maxValue: item.maxValue,
            minValue: item.minValue,
            indexId: item.indexId,
            isLeaf: !item.children.length,
            children: item.children.map((_item) => ({
              label: _item.detailName,
              value: _item.id,
              indexId: _item.indexId,
              isLeaf: !_item.children.length,
            })),
          }));
      });
      return {
        ...state,
        dimensionList: payload,
        dimensionOptions,
        dimensionValueList,
        dimensionValueOptionsByDimensionId,
      };
    },
  },
};

export default tagModel;
