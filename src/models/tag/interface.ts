import { StatusEnum } from '@/models/common.interface';
import { PaginatorParam } from '@/utils/request';

export enum LogicOperator {
  AND = 'and',
  OR = 'or',
}

// 列表参数数据
export interface UserGroupListParam extends PaginatorParam {
  tagId?: number;
  name?: string;
  // -1全部，0-禁用,1-启用
  enable?: number;
}

// 列表数据
export interface UserGroup {
  id: number;
  name: string;
  description: string;
  estimate: number;
  enable: number;
  createTime: number;
  updateTime: number;
}

export type EstimateCountCalculationParam = Pick<UserGroupParam, 'logicInfo'>;

export type UserGroupDetailLogicInfoAttr = LogicInfoAttrParam;

export interface UserGroupDetailLogicInfo {
  attrList: UserGroupDetailLogicInfoAttr[];
}

export type UserGroupDetail = Required<Omit<UserGroupParam, 'logicInfo'>> & {
  logicInfo: UserGroupDetailLogicInfo[];
};

// 创建/编辑提交参数
export interface UserGroupParam {
  tagId?: number;
  tagName: string;
  logicInfo: LogicInfoAttrParam[][];
  operator: LogicOperator;
}

export interface LogicInfoParam {
  attrList: LogicInfoAttrParam[];
}

export interface LogicInfoAttrParam {
  typeId: number;
  detailId: number;
  value: number[];
  logicId: number;
  // description: string;
  operator: LogicOperator;
}

// 属性
export interface SpecInfo {
  id: number;
  spec: string;
  enable: StatusEnum;
  type: number;
  children: SecondarySpecInfo[];
}

// 二级属性
export interface SecondarySpecInfo {
  id: number;
  detailName: string;
  indexId: number;
  logicType: string;
  enable: StatusEnum;
  maxValue: number;
  minValue: number;
  unit: string;
  children: SecondarySpecInfo[];
}

export enum LogicSpecTypeEnum {
  NONE = 0,
  SELECTOR = 1,
  DATEPICKER = 2,
  PETBREED = 3,
}

// 逻辑条件获取参数
export interface LoginSpecParam {
  type: LogicSpecTypeEnum;
  typeName?: string;
}

// 逻辑条件
export interface LogicSpecInfo extends SpecInfo {
  logicSpec: string;
  type: 1;
  // inputType: number;
}
