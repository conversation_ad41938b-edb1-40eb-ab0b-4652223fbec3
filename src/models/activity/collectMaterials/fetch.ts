
  import {
    CollectMaterialsListParam,
    CollectMaterials,
    CollectMaterialsParam,
    CollectMaterialsDetail,
  } from './interface';  
  import { collectMaterialsApiOption } from '@/services/api/collectMaterials';
  import request from '@/utils/request';
    
  // 获取CollectMaterials列表
  export const fetchCollectMaterialsList = (param?: CollectMaterialsListParam): Promise<CollectMaterials[]> => {
    const config = collectMaterialsApiOption.collectMaterialsList;
    param && (config.option.params = param);
    return request(config.url, config.option);
  };

  // 创建CollectMaterials
  export const fetchCollectMaterialsCreation = (param: CollectMaterialsParam): Promise<void> => {
    const config = collectMaterialsApiOption.collectMaterialsCreation;
    config.option.data = param
    return request(config.url, config.option);
  };

  // 更新CollectMaterials
  export const fetchCollectMaterialsUpdate = (param: CollectMaterialsParam): Promise<void> => {
    const config = collectMaterialsApiOption.collectMaterialsUpdate;
    config.option.data = param
    return request(config.url, config.option);
  };

  // 根据id获取CollectMaterials
  export const fetchCollectMaterialsDetail = (id: number): Promise<CollectMaterialsDetail> => {
    const config = collectMaterialsApiOption.collectMaterialsDetail;
    config.option.params = {id};
    return request(config.url, config.option);
  };

  // 删除CollectMaterials
  export const fetchCollectMaterialsDeletion = (id: number): Promise<void> => {
    const config = collectMaterialsApiOption.collectMaterialsDeletion;
    config.option.params = {id};
    return request<never>(config.url, config.option);
  };
