import { virtualDeviceApiOption } from '@/services/api/virtualDevice';
import request from '@/utils/request';
import {
  VirtualDevice,
  VirtualDeviceCategory,
  VirtualDeviceListParam,
  VirtualDeviceShelfStateParam,
} from './interface';

// 获取VirtualDevice列表
export const fetchVirtualDeviceList = (
  param: VirtualDeviceListParam,
): Promise<VirtualDevice[]> => {
  const config = virtualDeviceApiOption.virtualDeviceList;
  config.option.params = param;
  return request(config.url, config.option);
};

// 创建VirtualDevice
export const fetchVirtualDeviceShelfState = (
  param: VirtualDeviceShelfStateParam,
): Promise<void> => {
  const config = virtualDeviceApiOption.virtualDeviceShelfState;
  config.option.data = param;
  return request(config.url, config.option);
};

// 更新VirtualDevice
export const fetchVirtualDeviceCategoryList = (): Promise<{
  devices: VirtualDeviceCategory[];
}> => {
  const config = virtualDeviceApiOption.virtualDeviceCategoryList;
  return request(config.url, config.option);
};
