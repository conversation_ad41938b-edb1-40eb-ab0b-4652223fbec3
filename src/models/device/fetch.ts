import { ApiSuccessEnum } from '@/models/common.interface';
import { deviceApiOption } from '@/services/api/device';
import request, { Data } from '@/utils/request';
import { RequestOption } from '@mantas/request';
import {
  Device,
  DeviceLinkParam,
  DeviceListParam,
  DeviceLog,
  DeviceLogWithFile,
  DeviceLogsParam,
  DeviceModule,
  DeviceModuleListParam,
  DeviceModuleParam,
  DeviceSn,
  DeviceSnListParam,
  DeviceSnParam,
  Firmware,
  FirmwareBetaDeviceListParam,
  FirmwareBetaDeviceParam,
  FirmwareDetail,
  FirmwareListParam,
  FirmwareParam,
  FirmwareVersion,
  FirmwareVersionDetail,
  FirmwareVersionListParam,
  FirmwareVersionModule,
  FirmwareVersionParam,
  LinkHistory,
  LinkHistoryParam,
  LogLevelParam,
  MiniVersionParam,
  SandTrayUnlockParam,
  SendCommandParam,
  SoundFirmware,
  SoundFirmwareListParam,
  SoundFirmwareParam,
  SoundMd5,
} from './interface';

// 获取Device列表
export const fetchDeviceList = (
  param: DeviceListParam,
  type: string,
): Promise<Data<Device>> => {
  // const isCenter = global.centerDeviceList.includes(type);
  // const config = isCenter ? deviceApiOption.centerDeviceList : deviceApiOption.deviceList;
  const config = deviceApiOption.deviceList;
  config.option.restApi = { device: type };
  config.option.data = param;
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 解绑设备
export const fetchLink = (
  param: DeviceLinkParam,
  type: string,
): Promise<void> => {
  // const isCenter = global.centerDeviceList.includes(type);
  // const config = isCenter ? deviceApiOption.centerBindDevice : deviceApiOption.link;
  const config = deviceApiOption.link;
  config.option.restApi = { device: type };
  config.option.data = { ...param };
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 用宠物Id绑定设备
export const fetchLinkByPetId = (
  id: number,
  petId: string,
  type: string,
): Promise<void> => {
  // const isCenter = global.centerDeviceList.includes(type);
  // const config = isCenter ? deviceApiOption.centerBindDevice : deviceApiOption.link;
  const config = deviceApiOption.link;
  config.option.restApi = { device: type };
  config.option.data = { id, petId };
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 解绑设备
export const fetchUnlink = (id: number, type: string): Promise<void> => {
  // const isCenter = global.centerDeviceList.includes(type);
  // const config = isCenter ? deviceApiOption.centerUnbindDevice : deviceApiOption.unlink;
  const config = deviceApiOption.unlink;
  config.option.restApi = { device: type };
  config.option.data = { id };
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 获取设备绑定历史记录
export const fetchLinkHistory = (
  param: LinkHistoryParam,
  type: string,
): Promise<Data<LinkHistory>> => {
  const config: RequestOption = deviceApiOption.linkHistory;
  // 2023/6/17号 接口已经统一为link_histroy;
  // if (type.toLocaleLowerCase() === 'cozy') {
  //   config = deviceApiOption.cozyLinkHistory;
  // } else {
  //   // config = global.centerDeviceList.includes(type.toLocaleLowerCase())
  //   //   ? deviceApiOption.centerLinkHistory
  //   //   : deviceApiOption.linkHistory;
  //   config = deviceApiOption.linkHistory;
  // }
  config.option.data = param;
  config.option.restApi = { device: type };
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 获取设备序列号列表
export const fetchDeviceSnList = (
  param: DeviceSnListParam,
  type: string,
): Promise<Data<DeviceSn>> => {
  // const isCenter = global.centerDeviceList.includes(type);
  // const config = isCenter ? deviceApiOption.centerDeviceSnList : deviceApiOption.snList;
  const config = deviceApiOption.snList;
  config.option.restApi = { device: type };
  config.option.data = param;
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 保存设备序列号
export const fetchDeviceSnSaving = (
  param: DeviceSnParam,
  type: string,
): Promise<void> => {
  // const isCenter = global.centerDeviceList.includes(type);
  // const config = isCenter ? deviceApiOption.centerDeviceSnCreation : deviceApiOption.deviceSnSaving;
  const config = deviceApiOption.deviceSnSaving;
  config.option.restApi = { device: type };
  config.option.data = param;
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 删除设备序列号
export const fetchDeviceSnDeletion = (sn: string, type: string) => {
  // const isCenter = global.centerDeviceList.includes(type);
  // const config = isCenter ? deviceApiOption.centerDeleteDeviceSn : deviceApiOption.deviceSnDeletion;
  const config = deviceApiOption.deviceSnDeletion;
  config.option.restApi = { device: type };
  config.option.data = { sn };
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 设备固件列表(蓝牙)
export const fetchFirmwareList = (
  param: FirmwareListParam,
  type: string,
): Promise<Data<Firmware>> => {
  // const isCenter = global.centerDeviceList.includes(type);
  const config = deviceApiOption.firmwareList;
  config.option.restApi = { device: type };
  config.option.data = param;
  config.option.requestType = 'form';
  return request(config.url, config.option);
};
// 设备固件列表(wifi)
export const fetchFirmwareVersionList = (
  param: FirmwareVersionListParam,
  type: string,
): Promise<Data<FirmwareVersion>> => {
  // const isCenter = global.centerDeviceList.includes(type);
  const config = deviceApiOption.firmwareList;
  config.option.restApi = { device: type };
  config.option.data = param;
  config.option.requestType = 'form';
  return request(config.url, config.option);
};
// 设备腾讯固件列表(wifi)
export const fetchFirmwareVersionTxList = (
  param: FirmwareVersionListParam,
  type: string,
): Promise<Data<FirmwareVersion>> => {
  // const isCenter = global.centerDeviceList.includes(type);
  const config = deviceApiOption.firmwareTxList;
  config.option.restApi = { device: type };
  config.option.data = param;
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 设备固件详情(蓝牙)
export const fetchFirmwareDetail = (
  id: string,
  type: string,
): Promise<FirmwareDetail> => {
  // const isCenter = global.centerDeviceList.includes(type);
  const config = deviceApiOption.firmwareDetail;
  config.option.restApi = { device: type };
  config.option.params = { id };
  return request(config.url, config.option);
};
// 设备固件版本详情(wifi)
export const fetchFirmwareVersionDetail = (
  id: string,
  type: string,
): Promise<FirmwareVersionDetail> => {
  // const isCenter = global.centerDeviceList.includes(type);
  const config = deviceApiOption.firmwareDetail;
  config.option.restApi = { device: type };
  config.option.params = { id };
  return request(config.url, config.option);
};
// 设备腾讯固件版本详情(wifi)
export const fetchFirmwareVersionTxDetail = (
  id: string,
  type: string,
): Promise<FirmwareVersionDetail> => {
  // const isCenter = global.centerDeviceList.includes(type);
  const config = deviceApiOption.firmwareTxDetail;
  config.option.restApi = { device: type };
  config.option.params = { id };
  return request(config.url, config.option);
};

/**
 * @description 固件保存(蓝牙)
 * @param param {FirmwareParam}为蓝牙设备固件参数 {FirmwareVersionParam}为Wifi设备固件参数
 * @param type
 * @returns
 */
export const fetchFirmwareSaving = (
  param: FirmwareParam,
  type: string,
): Promise<ApiSuccessEnum> => {
  // const isCenter = global.centerDeviceList.includes(type);
  console.log(param);
  const config = deviceApiOption.firmwareSaving;
  config.option.restApi = { device: type };
  config.option.data = param;
  config.option.requestType = 'form';
  return request(config.url, config.option);
};
/**
 * @description 固件保存(wifi)
 * @param param {FirmwareParam}为蓝牙设备固件参数 {FirmwareVersionParam}为Wifi设备固件参数
 * @param type
 * @returns
 */
export const fetchFirmwareVersionSaving = (
  param: FirmwareVersionParam,
  type: string,
): Promise<ApiSuccessEnum> => {
  // const isCenter = global.centerDeviceList.includes(type);
  console.log(param);
  const config = deviceApiOption.firmwareSaving;
  config.option.restApi = { device: type };
  config.option.data = param;
  config.option.requestType = 'form';
  return request(config.url, config.option);
};
/**
 * @description 腾讯固件保存(wifi)
 * @param param {FirmwareParam}为蓝牙设备固件参数 {FirmwareVersionParam}为Wifi设备固件参数
 * @param type
 * @returns
 */
export const fetchFirmwareVersionTxSaving = (
  param: FirmwareVersionParam,
  type: string,
): Promise<ApiSuccessEnum> => {
  // const isCenter = global.centerDeviceList.includes(type);
  console.log(param);
  const config = deviceApiOption.firmwareTxSaving;
  config.option.restApi = { device: type };
  config.option.data = param;
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 固件内测设备列表
export const fetchFirmwareBetaDeviceList = async (
  param: FirmwareBetaDeviceListParam,
  type: string,
): Promise<Data<number>> => {
  // const isCenter = global.centerDeviceList.includes(type);
  const config = deviceApiOption.firmwareBetaDeviceList;
  config.option.restApi = { device: type };
  config.option.data = param;
  config.option.requestType = 'form';
  return request(config.url, config.option);
};
// 腾讯固件内测设备列表
export const fetchFirmwareBetaDeviceTxList = async (
  param: FirmwareBetaDeviceListParam,
  type: string,
): Promise<Data<number>> => {
  // const isCenter = global.centerDeviceList.includes(type);
  const config = deviceApiOption.firmwareBetaDeviceTxList;
  config.option.restApi = { device: type };
  config.option.data = param;
  config.option.requestType = 'form';
  return request(config.url, config.option);
};
// 固件添加内测设备
export const fetchFirmwareBetaDeviceAdding = (
  param: FirmwareBetaDeviceParam,
  type: string,
): Promise<ApiSuccessEnum> => {
  // const isCenter = global.centerDeviceList.includes(type);
  const config = deviceApiOption.firmwareBetaDeviceAdding;
  config.option.restApi = { device: type };
  config.option.data = param;
  config.option.requestType = 'form';
  return request(config.url, config.option);
};
// 腾讯固件添加内测设备
export const fetchFirmwareBetaDeviceTxAdding = (
  param: FirmwareBetaDeviceParam,
  type: string,
): Promise<ApiSuccessEnum> => {
  // const isCenter = global.centerDeviceList.includes(type);
  const config = deviceApiOption.firmwareBetaDeviceTxAdding;
  config.option.restApi = { device: type };
  config.option.data = param;
  config.option.requestType = 'form';
  return request(config.url, config.option);
};
// 固件删除内测设备
export const fetchFirmwareBetaDeviceDeletion = (
  firmwareId: string,
  deviceId: number,
  type: string,
): Promise<ApiSuccessEnum> => {
  // const isCenter = global.centerDeviceList.includes(type);
  const config = deviceApiOption.firmwareBetaDeviceDeletion;
  config.option.restApi = { device: type };
  config.option.params = { firmwareId, deviceId };
  return request(config.url, config.option);
};
// 腾讯固件删除内测设备
export const fetchFirmwareBetaDeviceTxDeletion = (
  firmwareId: string,
  deviceId: number,
  type: string,
): Promise<ApiSuccessEnum> => {
  // const isCenter = global.centerDeviceList.includes(type);
  const config = deviceApiOption.firmwareBetaDeviceTxDeletion;
  config.option.restApi = { device: type };
  config.option.params = { firmwareId, deviceId };
  return request(config.url, config.option);
};
// 固件通知升级
export const fetchFirmwareNotifyingUpgrade = (
  firmwareId: string,
  deviceId: number,
  type: string,
): Promise<ApiSuccessEnum> => {
  // const isCenter = global.centerDeviceList.includes(type);
  const config = deviceApiOption.firmwareNotifyingUpgrade;
  config.option.restApi = { device: type };
  config.option.data = { firmwareId, deviceId };
  config.option.requestType = 'form';
  return request(config.url, config.option);
};
// 固件通知升级
export const fetchFirmwareNotifyingTxUpgrade = (
  firmwareId: string,
  deviceId: number,
  type: string,
): Promise<ApiSuccessEnum> => {
  // const isCenter = global.centerDeviceList.includes(type);
  const config = deviceApiOption.firmwareNotifyingTxUpgrade;
  config.option.restApi = { device: type };
  config.option.data = { firmwareId, deviceId };
  config.option.requestType = 'form';
  return request(config.url, config.option);
};
// 固件删除
export const fetchFirmwareDeletion = (
  id: string,
  type: string,
): Promise<ApiSuccessEnum> => {
  // const isCenter = global.centerDeviceList.includes(type);
  const config = deviceApiOption.firmwareDeletion;
  config.option.restApi = { device: type };
  config.option.params = { id };
  return request(config.url, config.option);
};
// 固件删除
export const fetchFirmwareTxDeletion = (
  id: string,
  type: string,
): Promise<ApiSuccessEnum> => {
  // const isCenter = global.centerDeviceList.includes(type);
  const config = deviceApiOption.firmwareTxDeletion;
  config.option.restApi = { device: type };
  config.option.params = { id };
  return request(config.url, config.option);
};

// 获取固件详情中的固件模块选择列表
export const fetchFirmwareDetailsGroupByModule = (
  hardware: string,
  type: string,
): Promise<FirmwareVersionModule[]> => {
  const config = deviceApiOption.firmwareDetailsGroupByModule;
  config.option.restApi = { device: type };
  config.option.params = { hardware };
  return request(config.url, config.option);
};

// 设置log等级
export const fetchSetLogLevel = (
  param: LogLevelParam,
  type: string,
): Promise<void> => {
  const config = deviceApiOption.setLogLevel;
  // const config = global.centerDeviceList.includes(type.toLowerCase())
  //   ? deviceApiOption.centerSetLogLevel
  //   : deviceApiOption.setLogLevel;
  config.option.restApi = { device: type };
  config.option.data = param;
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 重启设备
export const fetchRestartDevice = (id: number, type: string): Promise<void> => {
  const config = deviceApiOption.restartDevice;
  // const config = global.centerDeviceList.includes(type.toLocaleLowerCase())
  //   ? deviceApiOption.centerRestartDevice
  //   : deviceApiOption.restartDevice;
  config.option.restApi = { device: type };
  config.option.data = { id };
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 获取SN列表数据
export const fetchCenterDeviceSnList = (
  param: DeviceSnListParam,
  type: string,
): Promise<Data<DeviceSn>> => {
  const config = deviceApiOption.centerDeviceSnList;
  config.option.restApi = { device: type };
  config.option.data = param;
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 删除SN数据
export const fetchCenterDeleteDeviceSn = (
  sn: string,
  type: string,
): Promise<void> => {
  const config = deviceApiOption.centerDeleteDeviceSn;
  config.option.restApi = { device: type };
  config.option.data = { sn };
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 创建设备SN号
export const fetchCenterDeviceSnCreation = (
  param: DeviceSnParam,
  type: string,
): Promise<void> => {
  const config = deviceApiOption.centerDeviceSnCreation;
  config.option.restApi = { device: type };
  config.option.data = param;
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 获取设备模块列表数据
export const fetchDeviceModuleList = (
  param: DeviceModuleListParam,
  type: string,
): Promise<DeviceModule[]> => {
  const config = deviceApiOption.deviceModuleList;
  // const config = global.centerDeviceList.includes(type)
  //   ? deviceApiOption.centerDeviceModuleList
  //   : deviceApiOption.deviceModuleList;
  config.option.restApi = { device: type };
  config.option.data = param;
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 修改/创建设备模块
export const fetchDeviceModuleEdition = (
  param: DeviceModuleParam,
  type: string,
): Promise<DeviceModule> => {
  const config = deviceApiOption.deviceModuleEdition;
  // const config = global.centerDeviceList.includes(type)
  //   ? deviceApiOption.centerDeviceModuleEdition
  //   : deviceApiOption.deviceModuleEdition;
  config.option.restApi = { device: type };
  config.option.data = param;
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 删除设备模块
export const fetchDeviceModuleDeletion = (
  moduleId: string,
  hardware: number,
  type: string,
): Promise<DeviceModule> => {
  const config = deviceApiOption.deviceModuleDeletion;
  // const config = global.centerDeviceList.includes(type.toLocaleLowerCase())
  //   ? deviceApiOption.centerDeviceModuleDeletion
  //   : deviceApiOption.deviceModuleDeletion;
  config.option.restApi = { device: type };
  config.option.data = { id: moduleId, hardware };
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 获取固件大版本列表
export const fetchFirmwareHardwareList = (type: string): Promise<number[]> => {
  const config = deviceApiOption.firmwareHardwareList;
  config.option.restApi = { device: type };
  return request(config.url, config.option);
};
// 获取腾讯固件大版本列表
export const fetchFirmwareHardwareTxList = (
  type: string,
): Promise<number[]> => {
  const config = deviceApiOption.firmwareHardwareTxList;
  config.option.restApi = { device: type };
  return request(config.url, config.option);
};

// 日志相关接口
export const fetchDeviceLogs = (
  param: DeviceLogsParam,
  type: string,
): Promise<Data<DeviceLog>> => {
  const config = deviceApiOption.deviceLogs;
  // const config = global.centerDeviceList.includes(type)
  //   ? deviceApiOption.centerDeviceLogs
  //   : deviceApiOption.deviceLogs;
  config.option.restApi = { device: type };
  config.option.data = { ...param };
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 日志相关接口(带下载文件)
export const fetchDeviceLogsWithFile = (
  param: DeviceLogsParam,
  type: string,
): Promise<Data<DeviceLogWithFile>> => {
  const config = deviceApiOption.deviceLogsWithFile;
  config.option.restApi = { device: type };
  config.option.data = { ...param };
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 设置最小APP版本
export const fetchMiniVersionSave = (
  param: MiniVersionParam,
  type: string,
): Promise<ApiSuccessEnum> => {
  const config = deviceApiOption.miniVersionSave;
  config.option.data = { ...param };
  config.option.restApi = { device: type };
  return request(config.url, config.option);
};
// 设置最小APP版本
export const fetchMiniVersionTxSave = (
  param: MiniVersionParam,
  type: string,
): Promise<ApiSuccessEnum> => {
  const config = deviceApiOption.miniVersionTxSave;
  config.option.data = { ...param };
  config.option.restApi = { device: type };
  return request(config.url, config.option);
};

// 获取最小APP版本
export const fetchMiniVersion = (
  hardware: string,
  type: string,
): Promise<string> => {
  const config = deviceApiOption.miniVersion;
  config.option.data = { hardware };
  config.option.restApi = { device: type };
  return request(config.url, config.option);
};
// 获取腾讯最小APP版本
export const fetchMiniVersionTx = (
  hardware: string,
  type: string,
): Promise<string> => {
  const config = deviceApiOption.miniVersionTx;
  config.option.data = { hardware };
  config.option.restApi = { device: type };
  return request(config.url, config.option);
};

// 音频固件包
export const fetchSoundFirmWareList = (
  param: SoundFirmwareListParam,
  type: string,
): Promise<Data<SoundFirmware>> => {
  const config = deviceApiOption.soundFirmWareList;
  config.option.restApi = { device: type };
  config.option.data = param;
  return request(config.url, config.option);
};

export const fetchSoundFirmWareDetail = (
  id: number,
  type: string,
): Promise<SoundFirmware> => {
  const config = deviceApiOption.soundFirmWareDetail;
  config.option.restApi = { device: type };
  config.option.data = { id };
  return request(config.url, config.option);
};

// 创建/更新
export const fetchSoundFirmWareSaving = (
  param: SoundFirmwareParam,
  type: string,
): Promise<ApiSuccessEnum> => {
  const config = deviceApiOption.soundFirmWareSaving;
  config.option.restApi = { device: type };
  config.option.data = param;
  return request(config.url, config.option);
};

export const fetchSoundFirmWareDeletion = (
  id: number,
  type: string,
): Promise<ApiSuccessEnum> => {
  const config = deviceApiOption.soundFirmWareDeletion;
  config.option.restApi = { device: type };
  config.option.data = { id };
  return request(config.url, config.option);
};

// 获取固件包MD5
export const fetchFirmwareSoundMd5 = (
  soundFileUrl: string,
): Promise<SoundMd5> => {
  const config = deviceApiOption.soundFirmwareMd5;
  config.url = `${soundFileUrl}${config.url}`;
  return request(config.url, config.option);
};

// 切换直播方案
export const fetchSwtichLiveType = (
  deviceType: string,
  deviceId: number,
): Promise<ApiSuccessEnum> => {
  const config = deviceApiOption.switchLiveType;
  config.option.data = { deviceId };
  config.option.restApi = { device: deviceType };
  return request(config.url, config.option);
};

// 下发指令
export const fetchSendCommand = (
  deviceType: string,
  param: SendCommandParam,
): Promise<{ result: boolean }> => {
  const config = deviceApiOption.sendCommand;
  config.option.data = param;
  config.option.restApi = { device: deviceType };
  return request(config.url, config.option);
};

// 猫砂盘解锁
export const fetchSandTrayUnlock = (
  param: SandTrayUnlockParam,
  deviceType: string,
): Promise<ApiSuccessEnum> => {
  const config = deviceApiOption.sandTrayUnlock;
  config.option.restApi = { device: deviceType };
  config.option.data = param;
  config.option.requestType = 'form';
  return request(config.url, config.option);
};
