import { LocaleObject, StatusEnum } from '@/models/common.interface';
import { PaginatorParam } from '@/utils/request';
import { WifiPimStatusEnum } from '../common.enum';

export enum OTAStatusEnum {
  READY = 1,
  UPDATING = 2,
  UPDATED = 3,
  UPDATED_FAILURE = 4,
  OFFLINE = 5,
}

export enum SearchTypeEnum {
  ID = 1,
  MAC = 2,
  OWNER = 3,
  Sn = 4,
}

export enum WifiSearchTypeEnum {
  MAC = 'mac',
  SN = 'sn',
  ID = 'id',
}

export enum BluetoothModeEnum {
  NONE = 0,
  NORMAL = 1,
  SMART = 2,
  RESPONSE = 3,
}

export enum OpenEnum {
  BETA = 0,
  PUBLIC = 1,
  GRAY = 2,
}

export enum ForceUpgradeEnum {
  NOT_FORCE = 0,
  FORCE = 1,
}

// 直播方案枚举
export enum P2pTypeEnum {
  TENCENT_LIVE = 1,
  AGORA_LIVE = 2,
}

/**
 * *********************
 * 设备列表相关
 */
export enum ServiceStatusEnum {
  NOT_AVAILABLE = 0,
  ON_SERVICE = 1,
  OUT_DATE = 2,
}

export interface File {
  url: string;
  size: number;
}

export interface DeviceListParam extends PaginatorParam {
  type?: SearchTypeEnum | WifiSearchTypeEnum;
  s?: string;
}

export interface Device {
  id: number;
  mac: string;
  sn: string;
  familyId: number;
  secret: string;
  createdAt: number;
  name?: string;
  hardware: number;
  firmware: string;
  timezone: number;
  signupAt: number;
  locale: string;
  region: number;
  shareOpen: number;
  btMac?: string;
  settings: {
    manualLock: number;
    rev: number;
    dryTime: number;
    targetTemp: number;
    unit: number;
  };
  state: {
    pim: WifiPimStatusEnum;
    ota: OTAStatusEnum;
    wifi: { bssid: string; ssid: string; rsq: number };
    // 垃圾袋盒SN
    packageSn?: string;
    // 水晶猫砂盘SN
    sandTraySn?: string;
  };
  specialLitterAd: {
    adDetailUrl: string;
    adSwitch: number;
    adLinkUrl: string;
    label: string;
    labelUrl: string;
    labelName: string;
  };
  status: StatusEnum;
  power?: StatusEnum;
  ledState?: StatusEnum;
  anionState?: StatusEnum;
  temp?: number;
  remainTime?: number;
  registerTime: number;
  tencentTuple: number;
  firmwareDetails: Array<{ module: string; version: string }>;
  relation?: {
    userId: string;
    petIds: number[];
  };
  owner?: { userId: string; petId: string };
  mode?: BluetoothModeEnum;
  // 只存云存设备特有数据
  cloudProduct: {
    id: number;
    workIndate: number;
  };
  // 云存套餐相关设备使用
  serviceStatus?: ServiceStatusEnum;
  p2pType?: P2pTypeEnum;
}

// 绑定参数
export interface DeviceLinkParam {
  id: number;
  groupId: number;
  userId?: number;
  petId?: number;
}

/**
 * *********************
 */

/**
 * *********************
 * 设备绑定历史相关
 */
export interface LinkHistoryParam extends PaginatorParam {
  id: number;
}

export interface LinkHistory {
  createdAt: string;
  deviceId: number;
  type: string;
  userId: string;
}
/**
 * *********************
 */
/**
 * *********************
 * 设备SN相关
 */
// 设备SN列表数据
export interface DeviceSn {
  chipId: string;
  createdAt: string;
  mac: string;
  sn: string;
}
// 设备SN列表接口参数
export interface DeviceSnListParam extends PaginatorParam {
  type?: SearchTypeEnum | WifiSearchTypeEnum;
  s?: string;
}

// 创建SN号的提交参数
export interface DeviceSnParam {
  sn: string;
  mac: string;
  chipId: string;
}
/**
 * *********************
 */
/**
 * *********************
 * 设备固件相关(蓝牙)
 */
// 固件参数
export interface FirmwareListParam extends PaginatorParam {
  _t: string;
  hardware: number;
  version?: string;
}
export interface Firmware {
  file: { size: number; url: string };
  hardware: number;
  id: string;
  open: OpenEnum;
  version: string;
  note: LocaleObject;
  // 灰度时使用的字段
  androidVersion?: string;
  iosVersion?: string;
}

// 固件返回参数
export type FirmwareDetail = Firmware;

// 固件版本提交参数(蓝牙)
export interface FirmwareParam {
  id?: string;
  file: string;
  hardware: number;
  version: string;
  extra: '{ "imageType": "A" }';
  open: OpenEnum;
  note: string;
  // 灰度时使用的字段
  androidVersion?: string;
  iosVersion?: string;
}
/**
 * *********************
 */

/**
 * *********************
 * 设备固件版本相关(wifi)
 */
// 固件参数
export interface FirmwareVersionListParam extends PaginatorParam {
  _t: string;
  hardware?: number;
  version?: string;
}
export interface FirmwareVersion {
  file: { size: number; url: string };
  hardware: number;
  id: string;
  open: OpenEnum;
  version: string;
  // wifi固件版本
  releaseNotes?: LocaleObject;
  appMinVersion?: string;
  details: FirmwareVersionListDetail[];
  forceUpgrade: ForceUpgradeEnum;
  remark?: string;
  // 灰度时使用的字段
  androidVersion?: string;
  iosVersion?: string;
  harmonyVersion?: string;
  // 目前只有t5,t6,t7提供
  createdAt?: number;
  creator?: string;
}

export interface FirmwareVersionListDetail {
  id: number;
  module: string;
  version: string;
  file: { url: string; size: number };
  note: string;
}
export interface FirmwareVersionModule {
  items: FirmwareVersionListDetail[];
  module: string;
}

export type FirmwareVersionDetail = FirmwareVersion;

export type FirmwareVersionDetailParam = Omit<
  FirmwareVersionListDetail,
  'note'
>;

// 固件版本提交参数(Wifi)
export interface FirmwareVersionParam
  extends Omit<FirmwareParam, 'extra' | 'note'> {
  forceUpgrade: number;
  appMinVersion: string;
  // LocaleObject
  releaseNotes: string;
  // FirmwareVersionDetailParam
  details: string;
  remark?: string;
  // 灰度时使用的字段
  androidVersion?: string;
  iosVersion?: string;
  harmonyVersion?: string;
}
/**
 * *********************
 */

export interface FirmwareBetaDevice {
  deviceId: number;
}

export interface FirmwareBetaDeviceListParam extends PaginatorParam {
  firmwareId: string;
  deviceId: number | undefined;
}

export interface FirmwareBetaDeviceParam {
  deviceIds: string;
  firmwareId: string;
}

/**
 * *********************
 */
/**
 * *********************
 * 设备列表相关
 */
// 设备模块列表接口参数
export type DeviceModuleListParam = PaginatorParam & {
  hardware: number;
};
/**
 * *********************
 */

// 设置log等级的参数
export interface LogLevelParam {
  id: number;
  level: string;
}

// 设备模块列表数据
export interface DeviceModule {
  id: string;
  logLevel: number;
  minVersion: number;
  priority: number;
  upgradeTime: number;
}

// 设备创建/修改提交参数
export interface DeviceModuleParam {
  id: string;
  // 排序
  priority: number;
  // 最小版本
  minVersion: number;
  // Log等级
  logLevel: number;
  // 升级时间(秒)
  upgradeTime: number;
  hardware?: number;
}

/**
 * *********************
 * 设备固件版本相关
 */
/**
 * *********************
 */

/**
 * *********************
 * 设备固件模块包相关
 */
/**
 * *********************
 */
/**
 * *********************
 * 日志相关
 */
export interface DeviceLog {
  createAt: string;
  src: string;
  log: string;
}
export interface DeviceLogsParam extends PaginatorParam {
  sn?: string;
}

export interface DeviceLogWithFile {
  deviceId: string;
  createdAt: string;
  file: File;
}

export interface DeviceLogWithFileListParam extends PaginatorParam {
  deviceId?: string;
}
/**
 * *********************
 */

/**
 * *********************
 * 设置最小APP版本
 */
export interface MiniVersionParam {
  hardware: string;
  version: string;
}
/**
 * *********************
 */

/**
 * *********************
 * 音频固件包
 */
// 音频固件包列表参数
export type SoundFirmwareListParam = PaginatorParam;

// 音频固件包列表数据
export interface SoundFirmware {
  digest: string;
  gmtCreate: string;
  id: number;
  note: string;
  size: number;
  url: string;
  version: string;
}

export interface SoundFirmwareParam {
  id?: number;
  url: string;
  size: number;
  digest: string;
  version: string;
  note: string;
}
/**
 * *********************
 */

export interface SoundMd5 {
  md5: string;
}
/**
 * *********************
 */

/**
 * *********************
 * 下发指令相关
 */
// 下发指令参数
export type SendCommandParam = {
  command: string;
  deviceId: number;
};

/**
 * *********************
 */

/**
 * *********************
 * 猫砂盘解锁相关
 */
// 猫砂盘区域枚举
export enum SandTrayRegionEnum {
  DOMESTIC = 0, // 国内
  OVERSEAS = 1, // 海外
}

// 猫砂盘解锁参数
export interface SandTrayUnlockParam {
  sn: string;
  secret: string;
}

/**
 * *********************
 */
