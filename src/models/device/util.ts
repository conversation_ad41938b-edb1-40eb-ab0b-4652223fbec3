import { BooleanEnum } from '@/models/common.enum';
import { initPaginatorParam } from '@/utils/request';
import {
  BluetoothModeEnum,
  DeviceListParam,
  DeviceLogsParam,
  DeviceLogWithFileListParam,
  DeviceModuleListParam,
  DeviceSnListParam,
  FirmwareBetaDeviceListParam,
  FirmwareListParam,
  ForceUpgradeEnum,
  LinkHistoryParam,
  OpenEnum,
  OTAStatusEnum,
  P2pTypeEnum,
  SandTrayRegionEnum,
  SearchTypeEnum,
  ServiceStatusEnum,
  SoundFirmwareListParam,
} from './interface';

export const initDeviceListParam: DeviceListParam = {
  limit: 10,
  offset: 0,
  type: SearchTypeEnum.ID,
  s: '',
};

export const initDeviceSnListParam: DeviceSnListParam = {
  ...initPaginatorParam,
  // type: SearchTypeEnum.Sn,
  // s: '',
};

export const initWifiDeviceSnListParam: DeviceSnListParam = {
  ...initPaginatorParam,
  // type: WifiSearchTypeEnum.SN,
  // s: '',
};

export const initHistoryListParam: LinkHistoryParam = {
  limit: 10,
  offset: 0,
  id: 0,
};

export const serviceStatusNameObj: { [key in ServiceStatusEnum]: string } = {
  [ServiceStatusEnum.NOT_AVAILABLE]: '未开通',
  [ServiceStatusEnum.ON_SERVICE]: '服务中',
  [ServiceStatusEnum.OUT_DATE]: '已过期',
};

const oTAStatus: { [key in keyof typeof OTAStatusEnum]: string } = {
  READY: '准备升级',
  UPDATING: '升级中',
  UPDATED: '升级完成',
  UPDATED_FAILURE: '升级失败',
  OFFLINE: '设备离线未开始升级',
};

export const getOTAStatusInfo = (ota: OTAStatusEnum) => {
  const info = oTAStatus[ota] || '未知';
  return info;
};

export const bluetoothModeNames: { [key in BluetoothModeEnum]: string } = {
  [BluetoothModeEnum.NONE]: '--',
  [BluetoothModeEnum.NORMAL]: '标准模式',
  [BluetoothModeEnum.SMART]: '简写模式',
  [BluetoothModeEnum.RESPONSE]: '电池模式',
};

export const openEnumName: { [key in OpenEnum]: string } = {
  [OpenEnum.PUBLIC]: '公开',
  [OpenEnum.GRAY]: '灰度',
  [OpenEnum.BETA]: '内测',
};
export const forceUpgradeEnumName: { [key in ForceUpgradeEnum]: string } = {
  [ForceUpgradeEnum.FORCE]: '自动',
  [ForceUpgradeEnum.NOT_FORCE]: '手动',
};

export const p2pTypeNameObj: { [key in P2pTypeEnum]: string } = {
  [P2pTypeEnum.TENCENT_LIVE]: '腾讯',
  [P2pTypeEnum.AGORA_LIVE]: '声网',
};

// 解锁状态展示规则
export const lockedNameMap: { [key in BooleanEnum]: string } = {
  [BooleanEnum.FALSE]: '已解锁',
  [BooleanEnum.TRUE]: '未解锁',
};

// 猫砂盘区域展示规则
export const sandTrayRegionNameMap: { [key in SandTrayRegionEnum]: string } = {
  [SandTrayRegionEnum.DOMESTIC]: '国内',
  [SandTrayRegionEnum.OVERSEAS]: '海外',
};

/**
 * 固件相关(蓝牙)
 */
export const initFirmwareListParam: FirmwareListParam = {
  ...initPaginatorParam,
  hardware: 0,
  version: undefined,
  _t: '',
};

export const initFirmwareBetaDeviceListParam: FirmwareBetaDeviceListParam = {
  ...initPaginatorParam,
  firmwareId: '',
  deviceId: undefined,
};
/**
 * **********
 */
/**
 * 固件版本相关(wifi)
 */
export const initFirmwareVersionListParam: FirmwareListParam = {
  ...initPaginatorParam,
  hardware: 0,
  version: undefined,
  _t: '',
};
/**
 * **********
 */

/**
 * 模块相关
 */
export const initDeviceModuleListParam: DeviceModuleListParam = {
  ...initPaginatorParam,
  limit: 100000,
};

/**
 * **********
 */

/**
 * 日志相关
 */
export const initDeviceLogsParam: DeviceLogsParam = {
  ...initPaginatorParam,
};

export const initDeviceLogWithFileListParam: DeviceLogWithFileListParam = {
  ...initPaginatorParam,
};

/**
 * **********
 */
export const initSoundFirmwareListParam: SoundFirmwareListParam = {
  ...initPaginatorParam,
};
