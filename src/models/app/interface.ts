import { PaginatorParam } from '@/utils/request';
import { DevicePlatformEnum } from '../common.enum';

export interface QiniuUploadResult {
  imageInfo: string;
  name: string;
  size: number;
  url: string;
}

export interface QiniuUploadToken {
  key: string;
  token: string;
}

// 列表参数数据
export interface Locale {
  key: string;
  name: string;
}

export interface LocalityParam {
  parent: string;
}

export interface Locality {
  code: string;
  name: string;
}

/**
 * ====================================
 * @description APP审核相关数据结构
 */
export enum AppVersionStateEnum {
  WAIT_VERIFY = 1,
  VERIFYING = 2,
  PUBLISHED = 3,
}

export interface AppVersionVerificationListParam extends PaginatorParam {
  version?: string;
  platform?: DevicePlatformEnum;
}

export interface AppVersionVerification {
  id: number;
  platform: DevicePlatformEnum;
  state: AppVersionStateEnum;
  version: string;
}

export interface AppVersionVerificationParam {
  id?: number;
  version: string;
  platform: DevicePlatformEnum;
}

/**
 * ====================================
 */
