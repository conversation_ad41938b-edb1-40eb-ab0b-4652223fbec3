import { initPaginatorParam } from '@/utils/request';
import {
  AppVersionStateEnum,
  AppVersionVerification,
  AppVersionVerificationListParam,
} from './interface';
import { DevicePlatformEnum } from '../common.enum';

export const initAppVersionVerificationListParam: AppVersionVerificationListParam = {
  ...initPaginatorParam,
};

export const appVersionStateName: { [key in AppVersionStateEnum]: string } = {
  [AppVersionStateEnum.WAIT_VERIFY]: '待审核',
  [AppVersionStateEnum.VERIFYING]: '审核中',
  [AppVersionStateEnum.PUBLISHED]: '已发布',
};

export const initAppVersionVerification: AppVersionVerification = {
  id: 0,
  platform: DevicePlatformEnum.iOS,
  state: AppVersionStateEnum.WAIT_VERIFY,
  version: '',
};
