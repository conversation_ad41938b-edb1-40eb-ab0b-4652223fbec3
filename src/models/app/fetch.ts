import { appApiOption } from '@/services/api/app';
import request, { Data } from '@/utils/request';
import { RequestMethod, RequestOption, TokenPlaceEnum } from '@mantas/request';
import { ApiSuccessEnum } from '../common.interface';
import {
  AppVersionStateEnum,
  AppVersionVerification,
  AppVersionVerificationListParam,
  AppVersionVerificationParam,
  Locale,
  Locality,
  LocalityParam,
  QiniuUploadToken,
} from './interface';

// 获取App列表
export const fetchQiniuToken = (
  option: RequestOption,
): Promise<QiniuUploadToken> => {
  const config = option;
  config.option.data = {
    namespace: 'post',
    ...option.option.data,
  };
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 获取国际化列表
export const fetchLocaleList = (): Promise<Locale[]> => {
  const config = appApiOption.localeList;
  return request(config.url, config.option);
};

// 获取国际化列表
export const fetchLocalities = (param: LocalityParam): Promise<Locality[]> => {
  const config = appApiOption.localities;
  config.option.data = param;
  return request(config.url, config.option);
};

/**
 * @description 获取APP审核版本列表
 */
export const fetchAppVersionVerificationList = (
  param: AppVersionVerificationListParam,
): Promise<Data<AppVersionVerification>> => {
  const config = appApiOption.appVersionDataList;
  config.option.params = param;
  return request(config.url, config.option);
};

/**
 * @description 创建/更新APP审核版本
 */
export const fetchAppVersionVerificationSaving = (
  param: AppVersionVerificationParam,
) => {
  const config = appApiOption.appVersionDataSaving;
  config.option.data = param;
  return request(config.url, config.option);
};

/**
 * @description 删除APP审核版本
 */
export const fetchAppVersionVerificationDeletion = (
  id: number,
): Promise<ApiSuccessEnum> => {
  const config = appApiOption.appVersionDataDeletion;
  config.option.data = { id };
  return request(config.url, config.option);
};

/**
 * @description APP版本 提审
 */
export const fetchAppVersionDataVerifying = (
  id: number,
  state: AppVersionStateEnum,
): Promise<ApiSuccessEnum> => {
  const config = appApiOption.appVersionDataVerifying;
  config.option.data = { id, state };
  return request(config.url, config.option);
};

/**
 * @description APP版本 发布
 */
export const fetchAppVersionDataPublishing = (
  id: number,
  state: AppVersionStateEnum,
): Promise<ApiSuccessEnum> => {
  const config = appApiOption.appVersionDataPublishing;
  config.option.data = { id, state };
  return request(config.url, config.option);
};

/**
 * @description 获取七牛oss的md5值内容
 */
export const fetchQiniuMd5 = (url: string): Promise<{ md5: string }> => {
  const config: RequestOption = {
    url,
    option: {
      headers: { 'Content-Type': 'application/json' },
      method: RequestMethod.Get,
      tokenPlaces: [TokenPlaceEnum.NONE],
    },
  };
  return request(config.url, config.option);
};
