import { SelectOption } from '@/models/common.interface';
import { ConnectState } from '@/models/connect';
import { Effect, Reducer } from '@umijs/max';
import { fetchLocaleList, fetchLocalities } from './fetch';
import { Locale, Locality } from './interface';

export interface AppState {
  localeList: Locale[];
  localeOptions: SelectOption[];
  localityMap: Record<string, Locality[]>;
}

export const initAppState: AppState = {
  localeList: [],
  localeOptions: [],
  localityMap: {},
};

export interface AppModel {
  namespace: 'app';
  state: AppState;
  effects: {
    requestLocaleList: Effect;
    requestLocalities: Effect;
  };
  reducers: {
    requestLocaleListSuccess: Reducer<
      AppState,
      { type: 'requestLocaleListSuccess'; payload: Locale[] }
    >;
    requestLocalitiesSuccess: Reducer<
      AppState,
      { type: 'requestLocalitiesSuccess'; payload?: Record<string, Locality[]> }
    >;
  };
}

const appModel: AppModel = {
  namespace: 'app',
  state: initAppState,
  effects: {
    *requestLocaleList(action, { call, put, select }) {
      let localeList: Locale[] = yield select(
        ({ app }: ConnectState) => app.localeList,
      );
      if (!localeList.length) {
        localeList = yield call(fetchLocaleList);
      }
      yield put({
        type: 'requestLocaleListSuccess',
        payload: localeList,
      });
    },
    *requestLocalities({ payload }, { call, put, select }) {
      let localityMap: Record<string, string> = yield select(
        ({ app }: ConnectState) => app.localityMap,
      );
      if (localityMap[payload.parent] && localityMap[payload.parent].length) {
        return;
      }
      const result: Locality[] = yield call(fetchLocalities, payload);
      yield put({
        type: 'requestLocalitiesSuccess',
        payload: { [payload.parent as string]: result },
      });
    },
  },
  reducers: {
    requestLocaleListSuccess(state = initAppState, { payload }): AppState {
      return {
        ...state,
        localeList: [...payload],
        localeOptions: payload.map((item) => ({
          value: item.key,
          label: item.name,
        })),
      };
    },

    requestLocalitiesSuccess(state = initAppState, { payload }): AppState {
      return {
        ...state,
        localityMap: {
          ...state.localityMap,
          ...payload,
        },
      };
    },
  },
};

export default appModel;
