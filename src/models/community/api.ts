import { defaultOptions } from '@/services/api/util';
import { RequestMethod, RequestOption } from '@mantas/request';

export const communityApiOption: Record<
  | 'appBannerList'
  | 'appBannerSave'
  | 'appBannerDetail'
  | 'appBannerEnable'
  | 'appBannerDeletion',
  RequestOption
> = {
  // 获取Community列表
  appBannerList: {
    url: '/adm/community/appbanners',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 创建/更新Community
  appBannerSave: {
    url: '/adm/community/updateAppBanner',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 根据Community的id获取详情数据
  appBannerDetail: {
    url: '/adm/community/findAppBanner',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 启用/禁用AppBanner
  appBannerEnable: {
    url: '/adm/community/enableAppBanner',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 删除AppBanner
  appBannerDeletion: {
    url: '/adm/community/deleteAppBanner',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
};
