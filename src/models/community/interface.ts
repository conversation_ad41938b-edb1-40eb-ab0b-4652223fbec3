import { PaginatorParam } from '@/utils/request';
import { StatusEnum } from '../common.interface';

export enum AppBannerTypeEnum {
  // 全部
  ALL = 0,
  // 已启用
  ENABLED = 1,
  // 未启用
  UNUSED = 2,
  // 已失效
  DISABLED = 3,
}

// 列表参数数据
export interface AppBannerListParam extends PaginatorParam {
  type: AppBannerTypeEnum;
}

// 列表数据
export interface AppBanner {
  id: number;
  countdown: number;
  enable: StatusEnum;
  link: { url: string };
  regions: string[];
  title: string;
  updateAt: number;
  url: string;
  startTime: number;
  tagId: string;
  tagName: string;
  endTime: number;
  testUser?: string;
}

// 启用禁用AppBanner参数
export interface AppBannerEnableParam {
  id: number;
  // doc: {enable: 0|1} 0为禁用，1为启用
  doc: string;
}

// 创建/编辑提交参数
export interface AppBannerParam {
  id?: number;
  title: string;
  url: string;
  enable: number;
  testUser?: string;
  // regions: ["CN_440204","CN_140203"] 国家_地区编码
  regions: string;
  // tagId: 1,2,3,4,5
  tagId?: string;
  // link: {"url": "xxxxx"}
  link: string;
  startTime: number;
  endTime: number;
}
