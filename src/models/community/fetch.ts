import request, { Data } from '@/utils/request';
import { ApiSuccessEnum } from '../common.interface';
import {
  AppBannerListParam,
  AppBanner,
  AppBannerEnableParam,
  AppBannerParam,
} from './interface';
import { communityApiOption } from './api';

// 获取AppBanner列表
export const fetchAppBannerList = (param: AppBannerListParam): Promise<Data<AppBanner>> => {
  const config = communityApiOption.appBannerList;
  config.option.params = param
  return request(config.url, config.option);
};

// 创建AppBanner
export const fetchAppBannerSave = (param: AppBannerParam): Promise<ApiSuccessEnum> => {
  const config = communityApiOption.appBannerSave;
  config.option.data = param
  return request(config.url, config.option);
};

// 根据id获取AppBanner
export const fetchAppBannerDetail = (id: number): Promise<AppBanner> => {
  const config = communityApiOption.appBannerDetail;
  config.option.data = {id};
  return request(config.url, config.option);
};

// 启用/禁用AppBanner
export const fetchAppBannerEnable = (param: AppBannerEnableParam): Promise<ApiSuccessEnum> => {
  const config = communityApiOption.appBannerEnable;
  config.option.data = {param};
  return request(config.url, config.option);
};

// 删除AppBanner
export const fetchAppBannerDeletion = (id: number): Promise<ApiSuccessEnum> => {
  const config = communityApiOption.appBannerDeletion;
  config.option.data = {id};
  return request<never>(config.url, config.option);
};