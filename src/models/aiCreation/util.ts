import { initPaginatorParam } from '@/utils/request';
import {
  AiCreationApplyListParam,
  AiCreationApplyStatusEnum,
  AiCreationListParam,
} from './interface';

export const aiCreationApplyStatusNameMap: {
  [key in AiCreationApplyStatusEnum]: string;
} = {
  [AiCreationApplyStatusEnum.UNCHECKED]: '未审核',
  [AiCreationApplyStatusEnum.APPROVAL]: '审核通过',
  [AiCreationApplyStatusEnum.REJECT]: '审核失败',
};

export const initAiCreationListParam: AiCreationListParam = {
  ...initPaginatorParam,
};

export const initAiCreationApplyListParam: AiCreationApplyListParam = {
  ...initPaginatorParam,
};

// export const initAiCreationParam: AiCreationParam = {};
