import { defaultOptions } from '@/services/api/util';
import { RequestMethod, RequestOption } from '@mantas/request';

export const aiCreationApiOption: Record<
  | 'aiCreationList'
  | 'aiCreationCreation'
  | 'aiCreationUpdate'
  | 'aiCreationDetail'
  | 'aiCreationDeletion'
  | 'aiCreeationStatusUpdate'
  // 任务审核相关
  | 'aiCreationNameQuery'
  | 'aiCreationApplyList'
  | 'aiCreationApplyApprove'
  | 'aiCreationApplyCount',
  RequestOption
> = {
  // 获取AiCreation列表
  aiCreationList: {
    url: '/adm/AICreation/list',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 创建AiCreation
  aiCreationCreation: {
    url: '/adm/AICreation/create',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
      // headers: {
      //   'Content-Type': 'application/json',
      // },
      // requestType: 'json',
    },
  },
  // 编辑AiCreation
  aiCreationUpdate: {
    url: '/adm/AICreation/edit',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
      // headers: {
      //   'Content-Type': 'application/json',
      // },
      // requestType: 'json',
    },
  },
  // 根据AiCreation的id获取详情数据
  aiCreationDetail: {
    url: '/adm/AICreation/detail',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 删除AiCreation
  aiCreationDeletion: {
    url: '/adm/AICreation/delete',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 上下架AiCreation
  aiCreeationStatusUpdate: {
    url: '/adm/AICreation/disable',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 模糊搜索AiCreation名称
  aiCreationNameQuery: {
    url: '/adm/AICreation/name/query',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 审核列表
  aiCreationApplyList: {
    url: '/adm/AICreation/check/list',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 审核
  aiCreationApplyApprove: {
    url: '/adm/AICreation/check/disable',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 未审核数量
  aiCreationApplyCount: {
    url: '/adm/AICreation/check/status/tab',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
};
