import request, { Data } from '@/utils/request';
import { ApiSuccessEnum, StatusEnum } from '../common.interface';
import { aiCreationApiOption } from './api';
import {
  AiCreation,
  AiCreationApplyApproveParam,
  AiCreationApplyCountParam,
  AiCreationApplyCountResult,
  AiCreationApplyInfo,
  AiCreationApplyListParam,
  AiCreationDetail,
  AiCreationListParam,
  AiCreationParam,
  AiCreationTaskInfo,
} from './interface';

// 获取AiCreation列表
export const fetchAiCreationList = (
  param?: AiCreationListParam,
): Promise<Data<AiCreation>> => {
  const config = aiCreationApiOption.aiCreationList;
  if (param) {
    config.option.params = param;
  }
  return request(config.url, config.option);
};

// 创建AiCreation
export const fetchAiCreationCreation = (
  param: AiCreationParam,
): Promise<ApiSuccessEnum> => {
  const config = aiCreationApiOption.aiCreationCreation;
  config.option.data = param;
  return request(config.url, config.option);
};

// 编辑AiCreation
export const fetchAiCreationUpdate = (
  param: AiCreationParam,
): Promise<ApiSuccessEnum> => {
  const config = aiCreationApiOption.aiCreationUpdate;
  config.option.data = param;
  return request(config.url, config.option);
};

// 根据id获取AiCreation
export const fetchAiCreationDetail = (
  id: number,
): Promise<AiCreationDetail> => {
  const config = aiCreationApiOption.aiCreationDetail;
  config.option.params = { id };
  return request(config.url, config.option);
};

// 删除AiCreation
export const fetchAiCreationDeletion = (
  id: number,
): Promise<ApiSuccessEnum> => {
  const config = aiCreationApiOption.aiCreationDeletion;
  config.option.params = { id };
  return request<never>(config.url, config.option);
};

// 上下架AiCreation
export const fetchAiCreeationStatusUpdate = (
  id: number,
  status: StatusEnum,
): Promise<ApiSuccessEnum> => {
  const config = aiCreationApiOption.aiCreeationStatusUpdate;
  config.option.data = { id, status };
  return request<never>(config.url, config.option);
};

// 模糊搜索活动名称
export const fetchAiCreationNameQuery = (
  name?: string,
): Promise<AiCreationTaskInfo[]> => {
  const config = aiCreationApiOption.aiCreationNameQuery;
  config.option.params = { name };
  return request<never>(config.url, config.option);
};

// 审核列表
export const fetchAiCreationApplyList = (
  param: AiCreationApplyListParam,
): Promise<Data<AiCreationApplyInfo>> => {
  const config = aiCreationApiOption.aiCreationApplyList;
  config.option.data = param;
  return request<never>(config.url, config.option);
};

// 审核
export const fetchAiCreationApplyApprove = (
  param: AiCreationApplyApproveParam,
): Promise<ApiSuccessEnum> => {
  const config = aiCreationApiOption.aiCreationApplyApprove;
  config.option.data = param;
  return request<never>(config.url, config.option);
};

// 审核
export const fetchAiCreationApplyCount = (
  param: AiCreationApplyCountParam,
): Promise<AiCreationApplyCountResult> => {
  const config = aiCreationApiOption.aiCreationApplyCount;
  config.option.data = param;
  return request<never>(config.url, config.option);
};
