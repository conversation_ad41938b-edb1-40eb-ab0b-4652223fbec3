import { PaginatorParam } from '@/utils/request';
import { MediaTypeEnum } from '../common.enum';
import { DeviceTypeEnum, StatusEnum } from '../common.interface';

export enum AwardDurationUnitEnum {
  DAY = 'd',
  MONTH = 'm',
  YEAR = 'y',
}

export enum TaskPropertyEnum {
  VIDEO = 1,
  IMAGE = 2,
  VIDEO_IMAGE = 3,
}

export enum TaskExampleTypeEnum {
  VIDEO = 1,
  IMAGE = 2,
}

// 列表参数数据
export interface AiCreationListParam extends PaginatorParam {
  deviceType?: DeviceTypeEnum;
  name?: string;
  activityStart?: number;
  activityEnd?: number;
  createStart?: number;
  createEnd?: number;
}

/**
 * ======================================================================
 * 列表数据 start
 */
export interface AiCreation {
  id: number;
  title: string;
  deviceType: DeviceTypeEnum;
  deviceName: string;
  taskCount: number;
  participateCount: number;
  receiveCount: number;
  startTime: number;
  endTime: number;
  createTime: number;
  status: StatusEnum;
}
/**
 * 列表数据 end
 * ======================================================================
 */

/**
 * ======================================================================
 * 创建/编辑提交参数 start
 */
export interface AiCreationTaskOptionTagParam {
  name: string;
}
export interface AiCreationTaskOptionParam {
  title: string;
  tagList: AiCreationTaskOptionTagParam[];
}
export interface AiCreationTaskParam {
  name: string;
  title: string;
  desc: string;
  property: TaskPropertyEnum;
  example: string;
  exampleType: TaskExampleTypeEnum;
  option: AiCreationTaskOptionParam[];
}
export interface AiCreationParam {
  id?: number;
  planId?: number;
  startTime: number;
  endTime: number;
  deviceType: DeviceTypeEnum;
  title: string;
  description: string;
  rule: string;
  skuId: number;
  planName: string;
  duration: number;
  unit: AwardDurationUnitEnum;
  // taskList: AiCreationTaskParam[];
  taskList: string;
}
/**
 * 创建/编辑提交参数 end
 * ======================================================================
 */

/**
 * ======================================================================
 * 详情数据 start
 */
export interface AiCreationDetail {
  startTime: number;
  endTime: number;
  deviceType: DeviceTypeEnum;
  title: string;
  description: string;
  rule: string;
  id: number;
  skuId: number;
  planId: number;
  planName: string;
  duration: number;
  unit: AwardDurationUnitEnum;
  taskList: {
    taskId: number;
    name: string;
    title: string;
    desc: string;
    property: TaskPropertyEnum;
    example: string;
    exampleType: TaskExampleTypeEnum;
    option: {
      title: string;
      tagList: { name: string }[];
    }[];
  }[];
}
/**
 * 详情数据 end
 * ======================================================================
 */

export interface AiCreationTaskInfo {
  id: number;
  title: string;
  taskList: { id: number; name: string }[];
}

/**
 * ======================================================================
 * AI共创审核列表 start
 */
export enum AiCreationApplyStatusEnum {
  UNCHECKED = 0,
  APPROVAL = 1,
  REJECT = 2,
}

// AI共创审核列表参数
export interface AiCreationApplyListParam extends PaginatorParam {
  deviceType?: DeviceTypeEnum;
  id?: number;
  taskId?: number;
  userId?: number;
  deviceId?: number;
  status?: AiCreationApplyStatusEnum;
}

export interface AiCreationApplyInfo {
  deviceType: DeviceTypeEnum;
  deviceName: string;
  id: number;
  actId: number;
  actName: string;
  taskId: number;
  taskName: string;
  userId: number;
  deviceId: number;
  status: AiCreationApplyStatusEnum;
  reason?: string;
  remark?: string;
  material: {
    url: string;
    urlType: MediaTypeEnum;
    tagName: string;
    petId: number;
    petName: string;
    petUrl: string;
    options: { tagName: string; title: string }[];
  };
}
/**
 * AI共创审核列表 end
 * ======================================================================
 */

/**
 * ======================================================================
 * AI共创审核相关 start
 */
export enum TaskApproveStatusEnum {
  APPROVED = 1,
  REJECT = 2,
}

export interface AiCreationApplyApproveParam {
  checkId: number;
  disable: TaskApproveStatusEnum;
  reason?: string;
  remark?: string;
}

export interface AiCreationApplyCountParam {
  deviceType?: DeviceTypeEnum;
  id?: number;
  taskId?: number;
  userId?: number;
  deviceId?: number;
}

export interface AiCreationApplyCountResult {
  unCheckCount: number;
}
/**
 * AI共创审核相关 end
 * ======================================================================
 */
