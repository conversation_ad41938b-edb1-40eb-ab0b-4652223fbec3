import { BaseQueryFilterProps } from '@ant-design/pro-components';
import { forIn } from 'lodash';
import {
  DeviceCodeEnum,
  DevicePlatformEnum,
  WifiPimStatusEnum,
} from './common.enum';
import { LocaleObject, PMData, StatusEnum, TreeNode } from './common.interface';

export const DEFAULT_PAGE_SIZE = 20;

export const getStatusMap = (): Map<
  'ALL' | StatusEnum,
  { text: string; status: string }
> => {
  const _statusMap = new Map<
    'ALL' | StatusEnum,
    { text: string; status: string }
  >();
  _statusMap.set(StatusEnum.ENABLE, { text: '启用', status: 'Success' });
  _statusMap.set(StatusEnum.DISABLE, { text: '禁用', status: 'Error' });
  return _statusMap;
};

// 组装form表达的提交参数数据 用于表格搜索form
type CopyPartialInterface<T> = Partial<Pick<T, keyof T>>;
export const transferFormDataToParam = <FormData, Param>(
  formData: FormData,
): CopyPartialInterface<Param> => {
  const param: CopyPartialInterface<Param> = {};
  forIn(formData, (value, key) => {
    if (value !== undefined) {
      (param as any)[key] = value;
    }
  });
  return param;
};

// 用户主人的跳转功能
export const postMessageFunction = (pmData: PMData) => {
  if (window.parent) {
    let redirectUrl = pmData.content.redirectUrl || '';
    const searchStr = new URLSearchParams(pmData.content.param);
    if (searchStr.toString()) {
      if (redirectUrl.includes('?')) {
        redirectUrl = `${redirectUrl}&${searchStr.toString()}`;
      } else {
        redirectUrl = `${redirectUrl}?${searchStr.toString()}`;
      }
    }
    window.parent.postMessage({
      type: pmData.type,
      content: {
        redirectUrl,
      },
    });
  }
};

// 获取国际化的值
export const getL10nValue = (localeObject: {
  [key: string]: string;
}): string => {
  const language = window.navigator.language
    .replace('-', '_')
    .toLocaleLowerCase();
  for (const locale in localeObject) {
    if (language === locale.toLowerCase()) {
      return localeObject[locale];
    }
  }

  for (const locale in localeObject) {
    if (localeObject[locale]) {
      return localeObject[locale];
    }
  }
  return '';
};

// 扁平化TreeNode树形结构,可传入回调函数自定义操作
export const flatTreeNode = (
  tree: TreeNode[],
  cb: (node: TreeNode) => void,
) => {
  if (!tree || !tree.length) return;
  tree.forEach((node) => {
    cb(node);
    flatTreeNode(node.children || [], cb);
  });
};

export const spanConfig: BaseQueryFilterProps['span'] = {
  xs: 24,
  sm: 24,
  md: 12,
  lg: 12,
  xl: 8,
  xxl: 8,
};

export const devicePlatformName: { [key in DevicePlatformEnum]: string } = {
  [DevicePlatformEnum.iOS]: 'iOS',
  [DevicePlatformEnum.Android]: 'Android',
};

export const wifiPimStatusNames: { [key in WifiPimStatusEnum]: string } = {
  [WifiPimStatusEnum.OFFLINE]: '离线',
  [WifiPimStatusEnum.ONLINE]: '在线模式',
  [WifiPimStatusEnum.BATTERY]: '电池模式',
};

// 设备代号名称映射
export const deviceCodeNameMap: { [key in DeviceCodeEnum]: string } = {
  [DeviceCodeEnum.BB]: '国内版',
  [DeviceCodeEnum.BA]: '国外版',
};

export const encodeLocaleObject = (obj: LocaleObject) => {
  const newObj: LocaleObject = {};
  for (const key in obj) {
    if (obj[key]) {
      const value = obj[key].replace(/\n/g, '\\n');
      newObj[key] = encodeURIComponent(obj[key]);
      console.log(newObj[key], value);
    }
  }
  return newObj;
};
