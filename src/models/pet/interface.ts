import { PaginatorParam } from '@/utils/request';

// 列表参数数据
export interface PetListParam extends PaginatorParam {
  // 该id为petId，也用于userId，但是传值结果为 owner-xxxxxx
  id?: string;
}

// 列表数据
export interface Pet {
  id: string;
  name: string;
  owner: { id: string };
  omsDiscernPic: {
    faceInfo: { faceOrigin: string; faceCut: string; faceDiscern: string }[];
    body: string[];
    bodyVideo: string;
  };
}
