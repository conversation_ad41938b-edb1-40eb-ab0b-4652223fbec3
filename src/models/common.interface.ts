import { IconType } from 'antd/es/notification/interface';
import { DefaultOptionType } from 'antd/lib/cascader';
import React from 'react';

export type EditorType = 'create' | 'update' | undefined;

export enum StatusEnum {
  ENABLE = 1,
  DISABLE = 0,
}

export interface ResponseLogicError {
  code: number;
  msg: string;
}

export interface Key {
  key: string;
}

export type KeyValue = string | number;
export type ValueType = string | number;

export interface Node {
  checkable?: boolean;
  disabled?: boolean;
  disableCheckbox?: boolean;
  icon?: IconType;
  isLeaf?: boolean;
  title?: React.ReactNode;
  selectable?: boolean;
  switcherIcon?: IconType;
  className?: string;
  style?: React.CSSProperties;
}

// antd没有暴露Data node数据结构
export interface DataNode extends Node {
  key: string | number;
  children?: DataNode[];
  parent?: DataNode;
}

// antd没有暴露Tree node数据结构
export interface TreeNode extends Node {
  key?: string | number;
  value: string | number;
  label?: React.ReactNode;
  children?: TreeNode[];
  parent?: TreeNode;
  /** Customize data info */
  [prop: string]: any;
}

export interface SelectOption<T = ValueType> {
  label: string;
  value: T;
  disabled?: boolean;
  children?: SelectOption[];
}

export type CascaderOption = DefaultOptionType;

export enum ApiSuccessEnum {
  success = 'success',
}

export interface PMData {
  type: PMDataType;
  content: PMDataContent;
}

export type PMDataType = 'redirect';

export interface PMDataContent {
  redirectUrl?: string;
  param?: Record<string, string>;
}

export interface BreadcrumbInfo {
  path: string;
  breadcrumbName: string;
}

// 页面url参数抽象
export interface UrlParam {
  [key: string]: string | undefined;
}

// 设备枚举
export enum DeviceTypeEnum {
  Fit = 1,
  Mate = 2,
  Go = 3,
  Feeder = 4,
  Cozy = 5,
  FeederMini = 6,
  T3 = 7,
  K2 = 8,
  D3 = 9,
  Aq = 10,
  D4 = 11,
  P3 = 12,
  H2 = 13,
  W5 = 14,
  T4 = 15,
  K3 = 16,
  Aqr = 17,
  R2 = 18,
  AqH1 = 19,
  D4s = 20,
  Hg = 22,
  Ctw3 = 24,
  D4sh = 25,
  D4H = 26,
  T6 = 27,
  T5 = 21,
  T7 = 28,
}

export type DeviceTypeValue = `${DeviceTypeEnum}`;
export type DeviceTypeKey = keyof typeof DeviceTypeEnum;

export type LocaleObject = { [key: string]: string };
