import { NewPaginatorParam } from '@/utils/request';
import { StatusEnum } from '../common.interface';

// 列表参数
export type AttireListParam = NewPaginatorParam;

// 列表数据
export interface Attire {
  id: number;
  name: string;
  subscribe: boolean;
  thumbnail: string;
  leftLivePic: string;
  rightLivePic: string;
  backgroundPic: string;
  createdAt: number;
  piority: number;
  status: StatusEnum;
}

export interface AttirePriorityParam {
  id?: number;
  priority: number;
}

// 创建/编辑提交参数
export interface AttireParam {
  id?: number;
  // localeName: LocaleObject;
  localeName: string;
  userIds: string;
  priority: number;
  subscribe: boolean;
  thumbnail: string;
  leftLivePic: string;
  rightLivePic: string;
  backgroundPic: string;
  binFile: string;
  binEncrypt: string;
  binFile2: string;
  binEncrypt2: string;
}

export interface AttireImportParam {
  file: File;
}

// 详情数据
export interface AttireDetail {
  id: number;
  localeName: string;
  userIds: string;
  priority: number;
  subscribe: boolean;
  thumbnail: string;
  leftLivePic: string;
  rightLivePic: string;
  backgroundPic: string;
  binFile: string;
  binEncrypt: string;
  binFile2: string;
  binEncrypt2: string;
}
