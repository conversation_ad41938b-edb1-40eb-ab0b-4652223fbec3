import { attireApiOption } from '@/services/api/attire';
import request, { Data } from '@/utils/request';
import { ApiSuccessEnum, StatusEnum } from '../common.interface';
import {
  Attire,
  AttireDetail,
  AttireImportParam,
  AttireListParam,
  AttireParam,
  AttirePriorityParam,
} from './interface';

// 获取装扮列表
export const fetchAttireList = (
  param: AttireListParam,
): Promise<Data<Attire>> => {
  const config = attireApiOption.attireList;
  config.option.params = param;
  return request(config.url, config.option);
};

// 创建装扮
export const fetchAttireCreation = (
  param: AttireParam,
): Promise<ApiSuccessEnum> => {
  const config = attireApiOption.attireCreation;
  config.option.data = param;
  return request(config.url, config.option);
};

// 更新装扮
export const fetchAttireUpdate = (
  param: AttireParam,
): Promise<ApiSuccessEnum> => {
  const config = attireApiOption.attireUpdate;
  config.option.data = param;
  return request(config.url, config.option);
};

// 根据装扮的id获取详情数据
export const fetchAttireDetail = (id: number): Promise<AttireDetail> => {
  const config = attireApiOption.attireDetail;
  config.option.data = { id };
  return request(config.url, config.option);
};

// 装扮校验优先级
export const fetchAttirePriorityJudgement = (
  param: AttirePriorityParam,
): Promise<boolean> => {
  const config = attireApiOption.attirePriorityJudgement;
  config.option.data = param;
  return request(config.url, config.option);
};

// 装扮导出
export const attireExcelExport = (): string => {
  const config = attireApiOption.attireExcelExport;
  // return request(config.url, config.option);
  return config.url;
};

// 装扮导入
export const fetchAttireExcelImport = async (
  param: AttireImportParam,
): Promise<ApiSuccessEnum> => {
  const config = attireApiOption.attireExcelImport;
  const formData = new FormData();
  formData.append('file', param.file);
  config.option.data = formData;
  return request(config.url, config.option);
};

export const fetchAttireStatusUpdate = (
  id: number,
  status: StatusEnum,
): Promise<ApiSuccessEnum> => {
  const config = attireApiOption.attireStatusUpdate;
  config.option.data = {id, status};
  return request(config.url, config.option);
};

export const fetchAttireDelete = (id: number): Promise<ApiSuccessEnum> => {
  const config = attireApiOption.attireDelete;
  config.option.data = {id, enable: 0};
  return request(config.url, config.option);
};
