import { PaginatorParam } from '@/utils/request';

export enum LockMaterialSearchTypeEnum {
  CODE = 'code',
  NAME = 'name',
  PROJECT = 'project',
}

// 列表参数数据
export interface LockMaterialListParam extends PaginatorParam {
  type: LockMaterialSearchTypeEnum | undefined;
  content: string | undefined;
}

export interface LockMaterial {
  id: number;
  createdAt: string;
  code: string;
  name: string;
  specification: string;
  project: string;
  deviceType: string;
  secret: string;
  updatedAt: string;
}

export type LockMaterialDetail = LockMaterial;

export interface LockMaterialParam {
  id?: number;
  code: string;
  name: string;
  specification: string;
  project: string;
}

export interface LockedDeviceListParam {
  deviceTypeId?: number;
  sn?: string;
}

export interface LockDeviceParam {
  deviceTypeId: number;
  sn: string;
}

export interface LockedDeviceInfo {
  deviceId: number;
  deviceType: string;
  sn: string;
  disableReason: string;
}

export interface DeviceTypeInfo {
  id: number;
  name: string;
}
