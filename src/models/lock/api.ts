import { RequestMethod, RequestOption } from '@mantas/request';
import { defaultOptions } from '../../services/api/util';

export const lockApiOption: Record<
  | 'lockMaterialList'
  | 'lockMaterialDetail'
  | 'lockMaterialSaving'
  | 'lockMaterialDeletion'
  | 'lockedDeviceList'
  | 'lockDevice'
  | 'deviceTypeList',
  RequestOption
> = {
  // 获取LockMaterial列表
  lockMaterialList: {
    url: '/adm/lock/materials',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 根据LockMaterial的id获取详情数据
  lockMaterialDetail: {
    url: '/adm/lock/material',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 保存LockMaterial
  lockMaterialSaving: {
    url: '/adm/lock/material_save',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 删除LockMaterial
  lockMaterialDeletion: {
    url: '/adm/lock/material_remove',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 锁定设备查询列表
  lockedDeviceList: {
    url: '/adm/lock/disable_query',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 锁定设备
  lockDevice: {
    url: '/adm/lock/device_disable',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 设备类型列表
  deviceTypeList: {
    url: '/adm/lock/deviceTypes',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
};
