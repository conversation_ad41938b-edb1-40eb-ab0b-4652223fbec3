import { ApiSuccessEnum } from '@/models/common.interface';
import request, { Data } from '@/utils/request';

import { lockApiOption } from '@/models/lock/api';
import {
  DeviceTypeInfo,
  LockDeviceParam,
  LockMaterial,
  LockMaterialDetail,
  LockMaterialListParam,
  LockMaterialParam,
  LockedDeviceInfo,
  LockedDeviceListParam,
} from './interface';

// 获取LockMaterial列表
export const fetchLockMaterialList = (
  param: LockMaterialListParam,
): Promise<Data<LockMaterial>> => {
  const config = lockApiOption.lockMaterialList;
  config.option.params = param;
  return request(config.url, config.option);
};

// 根据LockMaterial的id获取详情数据
export const fetchLockMaterialDetail = (
  id: number,
): Promise<LockMaterialDetail> => {
  const config = lockApiOption.lockMaterialDetail;
  config.option.params = { id };
  return request(config.url, config.option);
};

// 保存LockMaterial
export const fetchLockMaterialSaving = (
  param: LockMaterialParam,
): Promise<ApiSuccessEnum> => {
  const config = lockApiOption.lockMaterialSaving;
  config.option.data = param;
  config.option.requestType = 'form';
  return request(config.url, config.option);
};

// 删除LockMaterial
export const fetchLockMaterialDeletion = (
  id: number,
): Promise<ApiSuccessEnum> => {
  const config = lockApiOption.lockMaterialDeletion;
  config.option.params = { id };
  return request(config.url, config.option);
};

// 锁定设备查询列表
export const fetchLockedDeviceList = (
  param: LockedDeviceListParam,
): Promise<LockedDeviceInfo> => {
  const config = lockApiOption.lockedDeviceList;
  config.option.params = param;
  return request(config.url, config.option);
};

// 锁定设备
export const fetchLockDevice = (
  param: LockDeviceParam,
): Promise<ApiSuccessEnum> => {
  const config = lockApiOption.lockDevice;
  config.option.data = param;
  return request(config.url, config.option);
};

// 设备类型列表
export const fetchDeviceTypeList = (): Promise<DeviceTypeInfo[]> => {
  const config = lockApiOption.deviceTypeList;
  return request(config.url, config.option);
};
