import { getCurrentPrefix } from '@/utils/currentPrefix';
import { SizeEnum, UploadTokenTypeEnum } from './interface';

export const getByteSize = (byte: number, type: SizeEnum): number => {
  let result = 0;
  switch (type) {
    case SizeEnum.MB:
      result = byte * 1024 * 1024;
      break;
    case SizeEnum.KB:
      result = byte * 1024;
      break;
    case SizeEnum.B:
      result = byte;
      break;
    default:
      result = byte;
      break;
  }
  return result;
};

export const uploadTypeUrl: { [key in UploadTokenTypeEnum]: string } = {
  [UploadTokenTypeEnum.FILE]: `${getCurrentPrefix()}/adm/app/upload_file_token`,
  [UploadTokenTypeEnum.IMAGE]: `${getCurrentPrefix()}/adm/app/upload_image_token`,
  [UploadTokenTypeEnum.VIDEO]: `${getCurrentPrefix()}/adm/app/upload_video_token`,
  [UploadTokenTypeEnum.FIRMWARE]: `${getCurrentPrefix()}/adm/app/upload_firmware_token`,
  [UploadTokenTypeEnum.CUSTOMER]: '',
};
