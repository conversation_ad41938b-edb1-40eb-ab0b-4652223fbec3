import { LoginUserInfo } from '@/models/login/interface';
import { history } from '@umijs/max';

export interface LoginService {
  login: (user: LoginUserInfo) => void;
  logout: () => void;
}

export const loginService: LoginService = {
  /**
   * 登入
   */
  login(user: LoginUserInfo) {
    localStorage.setItem('sessionToken', user.token);
    localStorage.setItem('user', JSON.stringify(user));
  },

  /**
   * 登出
   */
  logout() {
    localStorage.removeItem('sessionToken');
    localStorage.removeItem('user');
    // const { query, pathname, search } = history.location;
    history.replace('/login');
  },
};
