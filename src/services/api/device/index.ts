import { RequestMethod, RequestOption } from '@mantas/request';
import { defaultOptions } from './../util';

export const deviceApiOption: Record<
  | 'deviceList'
  | 'link'
  | 'unlink'
  | 'cozyLinkHistory'
  | 'linkHistory'
  | 'snList'
  | 'deviceSnDeletion'
  | 'deviceSnSaving'
  // 固件相关
  | 'firmwareList'
  | 'firmwareDetail'
  | 'firmwareSaving'
  | 'firmwareBetaDeviceList'
  | 'firmwareBetaDeviceAdding'
  | 'firmwareBetaDeviceDeletion'
  | 'firmwareNotifyingUpgrade'
  | 'firmwareDeletion'
  | 'firmwareDetailsGroupByModule'
  | 'centerDeviceList'
  | 'centerLinkHistory'
  | 'setLogLevel'
  | 'centerSetLogLevel'
  | 'restartDevice'
  | 'centerRestartDevice'
  | 'centerBindDevice'
  | 'centerUnbindDevice'
  | 'centerDeviceSnList'
  | 'centerDeleteDeviceSn'
  | 'centerDeviceSnCreation'
  | 'deviceModuleList'
  | 'centerDeviceModuleList'
  | 'deviceModuleEdition'
  | 'centerDeviceModuleEdition'
  | 'deviceModuleDeletion'
  | 'centerDeviceModuleDeletion'
  // 固件大版本
  | 'firmwareHardwareList'
  // 腾讯固件大版本列表
  | 'firmwareHardwareTxList'
  // 腾讯固件相关
  | 'firmwareTxList'
  | 'firmwareTxDetail'
  | 'firmwareTxDeletion'
  | 'firmwareTxSaving'
  | 'firmwareBetaDeviceTxList'
  | 'firmwareBetaDeviceTxAdding'
  | 'firmwareBetaDeviceTxDeletion'
  | 'firmwareNotifyingTxUpgrade'
  // 设置腾讯固件最小APP版本
  | 'miniVersionTxSave'
  // 获取腾讯固件最小APP版本
  | 'miniVersionTx'
  // 日志相关
  | 'deviceLogs'
  | 'centerDeviceLogs'
  | 'deviceLogsWithFile'
  // 设置最小APP版本
  | 'miniVersionSave'
  // 获取最小APP版本
  | 'miniVersion'
  // 音频固件包
  | 'soundFirmWareList'
  | 'soundFirmWareDetail'
  | 'soundFirmWareSaving'
  | 'soundFirmWareDeletion'
  | 'soundFirmwareMd5'
  // 切换直播方案
  | 'switchLiveType'
  // 下发指令
  | 'sendCommand'
  // 猫砂盘解锁
  | 'sandTrayUnlock',
  RequestOption
> = {
  // 获取Device列表
  deviceList: {
    url: '/adm/:device/devices',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 绑定
  link: {
    url: '/adm/:device/link',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 解绑
  unlink: {
    url: '/adm/:device/unlink',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  linkHistory: {
    url: '/adm/:device/link_history',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  cozyLinkHistory: {
    url: '/adm/cozy/linkhistory',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 序列号列表
  snList: {
    url: '/adm/:device/sn_list',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 删除序列号
  deviceSnDeletion: {
    url: '/adm/:device/sn_remove',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 保存序列号
  deviceSnSaving: {
    url: '/adm/:device/sn_save',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 设备固件列表
  firmwareList: {
    url: '/adm/:device/firmwares',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 设备固件详情
  firmwareDetail: {
    url: '/adm/:device/firmware',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 固件保存
  firmwareSaving: {
    url: '/adm/:device/firmware_save',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 固件内测设备列表
  firmwareBetaDeviceList: {
    url: '/adm/:device/firmware_betadevices',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 固件添加内测设备
  firmwareBetaDeviceAdding: {
    url: '/adm/:device/firmware_add_betadevice',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 固件删除内测设备
  firmwareBetaDeviceDeletion: {
    url: '/adm/:device/firmware_remove_betadevice',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 固件通知升级
  firmwareNotifyingUpgrade: {
    url: '/adm/:device/firmware_notify',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 固件删除
  firmwareDeletion: {
    url: '/adm/:device/firmware_remove',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 腾讯固件删除
  firmwareTxDeletion: {
    url: '/adm/:device/firmware_remove_tx',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 获取固件详情中的固件模块选择列表
  firmwareDetailsGroupByModule: {
    url: '/adm/:device/firmware_details_groupby_module',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 设备列表
  centerDeviceList: {
    url: '/adm/center/:device/devices',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 绑定历史
  centerLinkHistory: {
    url: '/adm/center/:device/linkhistory',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 设置log等级
  setLogLevel: {
    url: '/adm/:device/log_level',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  centerSetLogLevel: {
    url: '/adm/center/:device/log_level',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 重启设备
  restartDevice: {
    url: '/adm/:device/reboot',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  centerRestartDevice: {
    url: '/adm/center/:device/reboot',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 解绑设备
  centerBindDevice: {
    url: '/adm/center/:device/link',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 绑定设备
  centerUnbindDevice: {
    url: '/adm/center/:device/unlink',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 设备sn列表
  centerDeviceSnList: {
    url: '/adm/center/:device/sn_list',
    option: { ...defaultOptions, method: RequestMethod.Post },
  },
  // 删除设备SN号
  centerDeleteDeviceSn: {
    url: '/adm/center/:device/sn_remove',
    option: { ...defaultOptions, method: RequestMethod.Post },
  },
  // 创建SN号
  centerDeviceSnCreation: {
    url: '/adm/center/:device/sn_save',
    option: { ...defaultOptions, method: RequestMethod.Post },
  },

  // 获取设备模块列表数据
  deviceModuleList: {
    url: '/adm/:device/modules',
    option: { ...defaultOptions, method: RequestMethod.Post },
  },
  centerDeviceModuleList: {
    url: '/adm/center/:device/modules',
    option: { ...defaultOptions, method: RequestMethod.Post },
  },
  deviceModuleEdition: {
    url: '/adm/:device/module_save',
    option: { ...defaultOptions, method: RequestMethod.Post },
  },
  centerDeviceModuleEdition: {
    url: '/adm/center/:device/module_save',
    option: { ...defaultOptions, method: RequestMethod.Post },
  },
  deviceModuleDeletion: {
    url: '/adm/:device/module_remove',
    option: { ...defaultOptions, method: RequestMethod.Post },
  },
  centerDeviceModuleDeletion: {
    url: '/adm/center/:device/module_remove',
    option: { ...defaultOptions, method: RequestMethod.Post },
  },
  // 固件大版本列表
  firmwareHardwareList: {
    url: '/adm/:device/hardwares',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 腾讯固件大版本列表
  firmwareHardwareTxList: {
    url: '/adm/:device/hardwaresTx',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 腾讯设备固件列表
  firmwareTxList: {
    url: '/adm/:device/firmwaresTx',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 腾讯设备固件详情
  firmwareTxDetail: {
    url: '/adm/:device/firmwareTx',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 腾讯固件保存
  firmwareTxSaving: {
    url: '/adm/:device/firmware_save_tx',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 腾讯固件内测设备列表
  firmwareBetaDeviceTxList: {
    url: '/adm/:device/firmware_betadevices_tx',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 固件添加内测设备
  firmwareBetaDeviceTxAdding: {
    url: '/adm/:device/firmware_add_betadevice_tx',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 腾讯固件删除内测设备
  firmwareBetaDeviceTxDeletion: {
    url: '/adm/:device/firmware_remove_betadevice_tx',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 腾讯固件通知升级
  firmwareNotifyingTxUpgrade: {
    url: '/adm/:device/firmware_notify_tx',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 设置腾讯固件最小APP版本
  miniVersionTxSave: {
    url: '/adm/:device/mini_version_save_tx',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 获取腾讯固件最小APP版本
  miniVersionTx: {
    url: '/adm/:device/mini_version_get_tx',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 日志相关
  deviceLogs: {
    url: '/adm/:device/logs',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  centerDeviceLogs: {
    url: '/adm/center/:device/logs',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 新版本日志接口，带文件下载
  deviceLogsWithFile: {
    url: '/adm/:device/deviceLogs',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 设置最小APP版本
  miniVersionSave: {
    url: '/adm/:device/mini_version_save',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 获取最小APP版本
  miniVersion: {
    url: '/adm/:device/mini_version_get',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 音频固件包
  soundFirmWareList: {
    url: '/adm/:device/sound_firmwares',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  soundFirmWareDetail: {
    url: '/adm/:device/sound_firmware',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  soundFirmWareSaving: {
    url: '/adm/:device/sound_firmware_save',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  soundFirmWareDeletion: {
    url: '/adm/:device/sound_firmware_delete',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  soundFirmwareMd5: {
    url: '?hash/md5',
    option: {
      method: RequestMethod.Get,
    },
  },
  switchLiveType: {
    url: '/adm/:device/updateLicenseSwitchMark',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  sendCommand: {
    url: '/adm/:device/sendCommand',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  sandTrayUnlock: {
    url: '/adm/:device/sandTray/lock',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
};
