import { RequestMethod, RequestOption } from '@mantas/request';
import { defaultOptions } from '../util';

export const tagApiOption: Record<
  | 'userGroupCreation'
  | 'userGroupUpdating'
  | 'userGroupList'
  | 'switchStatus'
  | 'userGroupDetail'
  | 'logicConditionList'
  | 'firstLevelCondition'
  | 'secondLevelCondition'
  | 'estimatePeopleCount'
  | 'estimatePeopleCountByTagId'
  | 'userGroupCreationEn'
  | 'userGroupUpdatingEn'
  | 'userGroupListEn'
  | 'switchStatusEn'
  | 'userGroupDetailEn'
  | 'logicConditionListEn'
  | 'firstLevelConditionEn'
  | 'secondLevelConditionEn'
  | 'estimatePeopleCountEn'
  | 'estimatePeopleCountByTagIdEn',
  RequestOption
> = {
  // 创建用户分群
  userGroupCreation: {
    url: '/tag/user/create',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 编辑用户分群
  userGroupUpdating: {
    url: '/tag/user/edit',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 获取用户分群列表
  userGroupList: {
    url: '/tag/user/list',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 快速启用/禁用用户分群
  switchStatus: {
    url: '/tag/user/enable',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 获取用户分群详情
  userGroupDetail: {
    url: '/tag/user/info',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 逻辑条件数据
  logicConditionList: {
    url: '/tag/user/logicList',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 一级属性数据
  firstLevelCondition: {
    url: '/tag/user/attrType',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 二级属性数据
  secondLevelCondition: {
    url: '/tag/user/attrDetail',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 编辑页面获取预估人数
  estimatePeopleCount: {
    url: '/tag/user/estimate',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 列表页面获取预估人数
  estimatePeopleCountByTagId: {
    url: '/tag/user/calculate',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 创建用户分群
  userGroupCreationEn: {
    url: '/tag/user/create',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 编辑用户分群
  userGroupUpdatingEn: {
    url: '/tag/user/edit',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 获取用户分群列表
  userGroupListEn: {
    url: '/tag/user/list',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 快速启用/禁用用户分群
  switchStatusEn: {
    url: '/tag/user/enable',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 获取用户分群详情
  userGroupDetailEn: {
    url: '/tag/user/info',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 逻辑条件数据
  logicConditionListEn: {
    url: '/tag/user/logicList',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 一级属性数据
  firstLevelConditionEn: {
    url: '/tag/user/attrType',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 二级属性数据
  secondLevelConditionEn: {
    url: '/tag/user/attrDetail',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 编辑页面获取预估人数
  estimatePeopleCountEn: {
    url: '/tag/user/estimate',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 列表页面获取预估人数
  estimatePeopleCountByTagIdEn: {
    url: '/tag/user/calculate',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
};
