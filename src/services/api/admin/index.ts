import { RequestMethod, RequestOption } from '@mantas/request';
import { defaultOptions } from '../util';

export const adminApiOption: Record<
  'adminGroupList' | 'adminGroup' | 'permissionList' | 'adminGroupSaving' | 'adminGroupDeleting',
  RequestOption
> = {
  // 获取管理组列表
  adminGroupList: {
    url: '/adm/admin/groups',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  permissionList: {
    url: '/adm/admin/permissions',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  adminGroup: {
    url: '/adm/admin/group',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  adminGroupSaving: {
    url: '/adm/admin/save_group',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  adminGroupDeleting: {
    url: '/adm/admin/remove_group',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
};
