
  import { RequestMethod, RequestOption } from '@mantas/request';

  export const collectMaterialsApiOption: Record<
    | 'collectMaterialsList'
    | 'collectMaterialsCreation'
    | 'collectMaterialsUpdate'
    | 'collectMaterialsDetail'
    | 'collectMaterialsDeletion',
    RequestOption
  > = {
    // 获取CollectMaterials列表
    collectMaterialsList: {
      url: '/collectMaterials/list',
      option: {
        method: RequestMethod.Get,
      },
    },
    // 创建CollectMaterials
    collectMaterialsCreation: {
      url: '/collectMaterials',
      option: {
        method: RequestMethod.Post,
      },
    },
    // 更新CollectMaterials
    collectMaterialsUpdate: {
      url: '/collectMaterials',
      option: {
        method: RequestMethod.Put,
      },
    },
    // 根据CollectMaterials的id获取详情数据
    collectMaterialsDetail: {
      url: '/collectMaterials',
      option: {
        method: RequestMethod.Get,
      },
    },
    // 删除CollectMaterials
    collectMaterialsDeletion: {
      url: '/collectMaterials',
      option: {
        method: RequestMethod.Delete,
      },
    },
  };
