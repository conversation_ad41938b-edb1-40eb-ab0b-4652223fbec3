import { RequestMethod, RequestOption } from '@mantas/request';
import { defaultOptions } from '../util';

export const virtualDeviceApiOption: Record<
  'virtualDeviceList' | 'virtualDeviceShelfState' | 'virtualDeviceCategoryList',
  RequestOption
> = {
  // 获取VirtualDevice列表
  virtualDeviceList: {
    url: '/adm/virtualDevice/virtualDeviceList',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  virtualDeviceShelfState: {
    url: '/adm/virtualDevice/updateShelfState',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  virtualDeviceCategoryList: {
    url: '/adm/virtualDevice/virtualDeviceType',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
};
