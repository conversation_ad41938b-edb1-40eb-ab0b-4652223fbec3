import { RequestMethod, RequestOption } from '@mantas/request';
import { defaultOptions } from '../util';

export const attireApiOption: Record<
  | 'attireList'
  | 'attireCreation'
  | 'attireUpdate'
  | 'attireDetail'
  | 'attirePriorityJudgement'
  | 'attireExcelExport'
  | 'attireExcelImport'
  | 'attireStatusUpdate'
  | 'attireDelete',
  RequestOption
> = {
  // 获取装扮列表
  attireList: {
    url: '/adm/attire/list',
    option: {
      ...defaultOptions,
      method: RequestMethod.Get,
    },
  },
  // 创建装扮
  attireCreation: {
    url: '/adm/attire/created',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 更新装扮
  attireUpdate: {
    url: '/adm/attire/edit',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 根据装扮的id获取详情数据
  attireDetail: {
    url: '/adm/attire/detail',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 装扮校验优先级
  attirePriorityJudgement: {
    url: '/adm/attire/judge_priority',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  attireExcelExport: {
    url: '/adm/attire/excel_export',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  attireExcelImport: {
    url: '/adm/attire/excel_import',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
      // 上传必须设置headers为空
      headers: {},
    },
  },
  attireStatusUpdate: {
    url: '/adm/attire/disabled',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  attireDelete: {
    url: '/adm/attire/delete',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
};
