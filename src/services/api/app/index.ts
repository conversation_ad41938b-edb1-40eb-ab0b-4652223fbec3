import { RequestMethod, RequestOption } from '@mantas/request';
import { defaultOptions } from '../util';

export const appApiOption: Record<
  | 'localeList'
  | 'localities'
  | 'uploadToken'
  | 'appVersionDataList'
  | 'appVersionDataSaving'
  | 'appVersionDataDeletion'
  | 'appVersionDataVerifying'
  | 'appVersionDataPublishing',
  RequestOption
> = {
  // 获取App列表
  localeList: {
    url: '/adm/app/locales',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  localities: {
    url: '/adm/app/localities',
    option: {
     ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  uploadToken: {
    url: '/adm/app/upload_image_token',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  appVersionDataList: {
    url: '/adm/app/getAppVersionData',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  appVersionDataSaving: {
    url: '/adm/app/saveAppVersion',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  appVersionDataDeletion: {
    url: '/adm/app/deleteAppVersion',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  appVersionDataVerifying: {
    url: '/adm/app/examineAppVersion',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  appVersionDataPublishing: {
    url: '/adm/app/publishAppVersion',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
};
