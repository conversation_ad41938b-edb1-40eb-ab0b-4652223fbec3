import { RequestMethod, RequestOption } from '@mantas/request';
import { defaultOptions } from '../util';

// 打包相关接口层
export const packageApiOption: Record<
  | 'packageBatchList'
  | 'packageBatchCreate'
  | 'packageSnList'
  | 'packageExport'
  | 'packageUnlock'
  | 'packageGarbageList'
  | 'packageResetBagBox'
  | 'packageOperationDetails',
  RequestOption
> = {
  // 获取Package列表
  packageBatchList: {
    url: '/adm/:device/package/list',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 创建Package
  packageBatchCreate: {
    url: '/adm/:device/package/create',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 更新Package
  packageSnList: {
    url: '/adm/:device/package/batch/list',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 导出垃圾袋
  packageExport: {
    url: '/adm/:device/package/excel/list',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  packageUnlock: {
    url: '/adm/:device/package/lock',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  packageGarbageList: {
    url: '/adm/:device/package/garbageList',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  packageResetBagBox: {
    url: '/adm/:device/package/resetBagBox',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  packageOperationDetails: {
    url: '/adm/:device/package/operationDetails',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
};
