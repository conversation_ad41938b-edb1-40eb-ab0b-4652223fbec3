import { RequestMethod, RequestOption } from '@mantas/request';
import { defaultOptions } from '../util';

export const familyApiOption: Record<
  | 'familyGroupList'
  | 'familyGroupListByUserId'
  | 'familyPetList'
  | 'familyDeviceList'
  | 'familyUserList',
  RequestOption
> = {
  // 获取Family列表
  familyGroupList: {
    url: '/adm/family/getFamilyGroupList',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 根据用户Id获取家庭组列表
  familyGroupListByUserId: {
    url: '/adm/family/getFamilyListByUser',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 根据家庭组id获取宠物列表
  familyPetList: {
    url: '/adm/family/getPetListByGroupId',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 根据家庭组id获取设备列表
  familyDeviceList: {
    url: '/adm/family/getDeviceListByGroupId',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 根据家庭组id获取用户列表
  familyUserList: {
    url: '/adm/family/getUserListByGroupId',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
};
