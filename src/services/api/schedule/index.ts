import { defaultOptions } from './../util';
import { RequestMethod, RequestOption } from '@mantas/request';

export const scheduleApiOption: Record<
  'scheduleTypeList' | 'scheduleTypeDetail' | 'scheduleTypeSaving' | 'scheduleTypeEnable',
  RequestOption
> = {
  // 获取ScheduleType列表
  scheduleTypeList: {
    url: '/adm/schedule/types',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 根据ScheduleType的id获取详情数据
  scheduleTypeDetail: {
    url: '/adm/schedule/type',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 保存ScheduleType
  scheduleTypeSaving: {
    url: '/adm/schedule/type_save',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
  // 启用/禁用ScheduleType
  scheduleTypeEnable: {
    url: '/adm/schedule/type_enable',
    option: {
      ...defaultOptions,
      method: RequestMethod.Post,
    },
  },
};
