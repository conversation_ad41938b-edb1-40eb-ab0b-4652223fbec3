import { postMessageFunction } from '@/models/common.util';
import React from 'react';

interface Props {
  userId: string;
}

const UserIdLink: React.FC<Props> = ({ userId }: Props) => {
  if (!userId) return <>-</>;

  return (
    <a
      onClick={() =>
        postMessageFunction({
          type: 'redirect',
          content: { redirectUrl: `/user/users?username=${userId}` },
        })
      }
    >
      {userId}
    </a>
  );
};

export default UserIdLink;
