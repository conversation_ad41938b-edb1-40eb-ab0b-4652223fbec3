import { Input, InputProps, Select, SelectProps } from 'antd';
import { useEffect, useState } from 'react';
import { SelectInputValue } from './interface';
import { initialSelectInputValue } from './util';

interface Props {
  inputProps: Omit<InputProps, 'value'>;
  selectProps: Omit<SelectProps, 'value'>;
  value?: SelectInputValue;
  onChange?: (value: SelectInputValue) => void;
}

const SelectInputGroup = ({
  inputProps,
  selectProps,
  value = initialSelectInputValue(selectProps.defaultValue),
  onChange,
}: Props) => {
  const [inputValue, setInputValue] = useState('');
  const [selectValue, setSelecValue] = useState<string | number>(
    selectProps.defaultValue,
  );

  const triggerChange = (value: SelectInputValue) => {
    onChange?.(value);
  };

  useEffect(() => {
    if (!value) {
      triggerChange(initialSelectInputValue(selectProps.defaultValue));
      return;
    }
    setInputValue(value?.input || '');
    setSelecValue(value.select || selectProps.defaultValue);
  }, [value]);

  return (
    <Input.Group compact style={{ display: 'flex', width: '100%' }}>
      <Select
        placeholder="请选择..."
        {...selectProps}
        value={selectValue}
        onChange={(val) => triggerChange({ ...value, select: val })}
      ></Select>
      <Input
        placeholder="请输入..."
        {...inputProps}
        value={inputValue}
        onChange={(e) => triggerChange({ ...value, input: e.target.value })}
      />
    </Input.Group>
  );
};

export default SelectInputGroup;
