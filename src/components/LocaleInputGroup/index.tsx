import { Form, Input } from 'antd';
import { useEffect, useState } from 'react';
import styles from './index.less';
import { LocaleInputGroupValue, LocaleInputList } from './interface';

export interface Props {
  id?: string;
  value?: LocaleInputGroupValue;
  onChange?: (value: LocaleInputGroupValue) => void;
  localeInputList?: LocaleInputList[];
  'aria-invalid'?: boolean;
  disabled?: boolean;
}

const LocaleInputGroup: React.FC<Props> = ({
  id,
  value = {},
  onChange,
  localeInputList = [{ locale: 'zh_CN' }, { locale: 'en_US' }],
  'aria-invalid': ariaInvalid = false,
  disabled = false,
}: Props) => {
  const [inputValue, setInputValue] = useState<LocaleInputGroupValue>({});

  const triggerChange = (_value: LocaleInputGroupValue) => {
    onChange?.(_value);
    setInputValue(_value);
  };

  const onInputChanged = <T = HTMLInputElement,>(
    ev: React.ChangeEvent<T>,
    locale: string,
  ) => {
    const _inputValue = { ...inputValue };
    if (
      ev.target instanceof HTMLInputElement ||
      ev.target instanceof HTMLTextAreaElement
    ) {
      _inputValue[locale] = ev.target.value;
    }
    triggerChange(_inputValue);
  };

  // useEffect(() => {
  //   console.log(id, ariaInvalid, rest);
  // }, [id, ariaInvalid]);

  useEffect(() => {
    if (value && JSON.stringify(value) === JSON.stringify(inputValue)) {
      return;
    }

    if (!value || JSON.stringify(value) === '{}') {
      const _inputValue: LocaleInputGroupValue = {};
      localeInputList.forEach(({ locale }) => {
        _inputValue[locale] = '';
      });
    }
    setInputValue(value);
  }, [value, localeInputList]);

  if (!id) {
    return <></>;
  }

  return (
    <div className={styles.container}>
      {localeInputList.map(
        ({ locale, type, inputProps, textareaProps, required }, index) => {
          // const name = [...id.split('_'), locale];
          return (
            <div className={styles.locale} key={String(index)}>
              {type === 'textarea' ? (
                <Form.Item
                  label={locale}
                  className={styles.formItem}
                  required={required}
                  validateStatus={
                    ariaInvalid && !inputValue?.[locale] && required
                      ? 'error'
                      : 'success'
                  }
                >
                  <Input.TextArea
                    {...textareaProps}
                    className={styles.textarea}
                    value={inputValue?.[locale]}
                    disabled={disabled}
                    onChange={(ev) =>
                      onInputChanged<HTMLTextAreaElement>(ev, locale)
                    }
                  />
                </Form.Item>
              ) : null}
              {type !== 'textarea' ? (
                <Form.Item
                  label={locale}
                  className={styles.formItem}
                  required={required}
                  validateStatus={
                    ariaInvalid && !inputValue?.[locale] && required
                      ? 'error'
                      : 'success'
                  }
                >
                  <Input
                    {...inputProps}
                    className={styles.textarea}
                    value={inputValue?.[locale]}
                    disabled={disabled}
                    onChange={(ev) => onInputChanged(ev, locale)}
                  />
                </Form.Item>
              ) : null}
            </div>
          );
        },
      )}
    </div>
  );
};

export default LocaleInputGroup;
