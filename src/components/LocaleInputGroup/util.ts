import { LocaleInputGroupValue, LocaleInputList } from './interface';

export const validateLocaleInputGroup = (
  value: LocaleInputGroupValue,
  localInputList: LocaleInputList[],
): Promise<Error | void> => {
  const requiredFields = localInputList.filter((item) => item.required);
  const errorInfoList: string[] = [];
  requiredFields.forEach((field) => {
    const placeholder =
      field.inputProps?.placeholder || field.textareaProps?.placeholder;
    if (!value[field.locale]) {
      errorInfoList.push(
        placeholder?.replace('请输入', `请输入${field.locale}`) ||
          `请配置${field.locale}`,
      );
    }
  });
  if (errorInfoList.length === 0) {
    return Promise.resolve();
  }

  return Promise.reject(new Error(errorInfoList[0]));
};
