import { InputProps } from 'antd';
import { TextAreaProps } from 'antd/es/input';

export type LocaleInputGroupValue = Record<string, string>;

export interface LocaleInputList {
  inputProps?: InputProps;
  textareaProps?: TextAreaProps;
  locale: string;
  required?: boolean;
  type?:
    | 'button'
    | 'checkbox'
    | 'color'
    | 'date'
    | 'datetime-local'
    | 'email'
    | 'file'
    | 'hidden'
    | 'image'
    | 'month'
    | 'number'
    | 'password'
    | 'radio'
    | 'range'
    | 'reset'
    | 'search'
    | 'submit'
    | 'tel'
    | 'text'
    | 'time'
    | 'url'
    | 'week'
    | string;
}
