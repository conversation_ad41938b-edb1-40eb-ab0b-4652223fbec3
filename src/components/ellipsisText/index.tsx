import { Tooltip, Typography } from 'antd';
import React from 'react';
import styles from './index.less';

interface Props {
  tooltip?: boolean;
  copyable?: boolean;
  width?: number | string;
  className?: string;
  tooltipShow?: boolean;
  message?: string;
  maxLength?: number;
  children: React.ReactNode;
}

const EllipsisText: React.FC<Props> = ({
  copyable = false,
  width,
  className = '',
  tooltip = false,
  tooltipShow = false,
  message = '',
  maxLength,
  children,
}: Props) => {
  if (!children && !message) return null;

  const textDom = (
    <Typography.Text
      ellipsis
      style={{ width }}
      copyable={copyable}
      className={`${className}`}
    >
      {children || message.slice(0, maxLength)}
    </Typography.Text>
  );

  if (!tooltip) return textDom;

  if (tooltip)
    return (
      <Tooltip
        overlayClassName={!tooltipShow && styles.noArrow}
        arrowPointAtCenter={false}
        title={children || message}
      >
        {textDom}
      </Tooltip>
    );

  return null;
};

export default EllipsisText;
