import { postMessageFunction } from '@/models/common.util';
import React, { useEffect, useState } from 'react';

interface Props {
  linkUrl: string;
  params: { [key: string]: string | number };
  text?: string | React.ReactNode;
  defaultText?: string;
}

const RedirectLink: React.FC<Props> = ({ linkUrl, params, text = '', defaultText = '' }: Props) => {
  const [url, setUrl] = useState<string>('');

  useEffect(() => {
    const urlParam = new URLSearchParams(params as Record<string, string>);
    setUrl(`${linkUrl}?${urlParam.toString()}`);
  }, [linkUrl, params]);

  if (!text) return <>{defaultText || '-'}</>;

  return (
    <a
      style={{ display: 'block', paddingLeft: 0, paddingRight: 0 }}
      onClick={() =>
        postMessageFunction({
          type: 'redirect',
          content: { redirectUrl: url },
        })
      }
    >
      {text}
    </a>
  );
};

export default RedirectLink;
