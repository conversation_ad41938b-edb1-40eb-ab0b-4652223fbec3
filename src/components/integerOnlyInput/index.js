import React from 'react';
import { Input } from 'antd';
import { NumberHelper } from '@/utils/helper';
import { isNumber } from '@/utils/lang';

export default (props) => {
  const {
    disabled,
    placeholder = '请输入数字',
    addonBefore,
    addonAfter,
    min = 0,
    max = 99999999,
    value,
    style,
    onNumberChange,
  } = props;

  const isLessThanMin = (val) => val < min;
  const isGreatThanMax = (val) => val > max;

  const handleInputChange = (e) => {
    const inputValue = e.target.value;
    if (inputValue !== '' && isNumber(inputValue)) {
      let result = Number(inputValue);
      result = NumberHelper.intOnly(result);
      if (isLessThanMin(result)) {
        result = min;
      }
      if (isGreatThanMax(result)) {
        result = max;
      }
      onNumberChange && onNumberChange(result);
    } else {
      onNumberChange && onNumberChange('');
    }
  };

  return (
    <Input
      value={value}
      style={style}
      disabled={disabled}
      placeholder={placeholder}
      addonBefore={addonBefore}
      addonAfter={addonAfter}
      onChange={handleInputChange}
    />
  );
};
