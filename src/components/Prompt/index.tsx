import { SelectOption } from '@/models/common.interface';
import { Col, ColProps, Input, InputNumber, Modal, Radio, RadioChangeEvent, Row } from 'antd';
import React, {
  ChangeEvent,
  HTMLInputTypeAttribute,
  useCallback,
  useEffect,
  useState,
} from 'react';

// 为了方便与业务系统进行解耦，放入组件系统
type ValueType = string | number;

interface PromptProps {
  visible: boolean;
  defaultValue?: ValueType | ValueType[];
  title: React.ReactNode;
  label: React.ReactNode;
  valueType?: HTMLInputTypeAttribute;
  options?: SelectOption[];
  labelCol?: ColProps;
  wrapperCol?: ColProps;
  placeholder?: string;
  onOk: (v: ValueType | ValueType[] | null | undefined) => void;
  onCancel: () => void;
}

const Prompt: React.FC<PromptProps> = ({
  visible = false,
  title,
  label,
  valueType = 'text',
  options = [],
  defaultValue,
  labelCol = { span: 24 },
  wrapperCol = { span: 24 },
  placeholder = '请输入',
  onOk,
  onCancel,
}: PromptProps) => {
  const [val, setVal] = useState<ValueType | ValueType[] | null | undefined>();

  useEffect(() => {
    setVal(defaultValue);
  }, [defaultValue]);

  const renderForm = useCallback(() => {
    switch (valueType) {
      case 'number':
        return (
          <InputNumber<ValueType>
            defaultValue={defaultValue as ValueType}
            onChange={(value) => setVal(value)}
          />
        );
      case 'radio':
        return (
          <Radio.Group onChange={({ target: { value } }: RadioChangeEvent) => setVal(value)}>
            {options.map((item) => (
              <Radio value={item.value}>{item.label}</Radio>
            ))}
          </Radio.Group>
        );
      default:
        return (
          <Input
            type={valueType}
            value={val as string}
            placeholder={placeholder}
            onChange={({ target: { value } }: ChangeEvent<HTMLInputElement>) => {
              setVal(value);
            }}
          />
        );
    }
  }, [valueType, val]);

  return (
    <Modal open={visible} title={title} destroyOnClose onOk={() => onOk(val)} onCancel={onCancel}>
      <Row>
        <Col {...labelCol} style={{ marginBottom: '8px' }}>
          {label}
        </Col>
        <Col {...wrapperCol}>{renderForm()}</Col>
      </Row>
    </Modal>
  );
};

export default Prompt;
