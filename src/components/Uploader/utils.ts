import { SizeEnum } from './interface';

export const getByteSize = (byte: number, type: SizeEnum): number => {
  let result = 0;
  switch (type) {
    case SizeEnum.MB:
      result = byte * 1024 * 1024;
      break;
    case SizeEnum.KB:
      result = byte * 1024;
      break;
    case SizeEnum.B:
      result = byte;
      break;
    default:
      result = byte;
      break;
  }
  return result;
};
