import { QiniuUploadResult } from '@/models/app/interface';
import { SizeEnum, UploadTokenTypeEnum } from '@/services/qiniuOss/interface';
import { qiniuOssService } from '@/services/qiniuOss/qiniuOss.service';
import { getByteSize } from '@/services/qiniuOss/utils';
import imageAsync from '@/utils/imageAsync';
import readFileAsync from '@/utils/readFileAsync';
import videoAsync from '@/utils/videoAsync';
import { PlusOutlined, UploadOutlined } from '@ant-design/icons';
import { Button, Modal, Upload, UploadProps, message } from 'antd';
import { RcFile, UploadChangeParam } from 'antd/lib/upload';
import { UploadFile } from 'antd/lib/upload/interface';
import { isFunction } from 'lodash';
import React, { useEffect, useState } from 'react';
import styles from './index.less';

export interface UploaderProps {
  disabled?: boolean;
  accept?: string;
  type?: UploadTokenTypeEnum;
  customerUrl?: string;
  uploadData?: { namespace: string };
  fileList?: Array<UploadFile<QiniuUploadResult>>;
  sizeList?: number[][];
  // Mb单位
  maxSize?: number;
  maxWidth?: number;
  maxHeight?: number;
  maxCount?: number;
  uploadButtonText?: string;
  onChange?: (info: UploadChangeParam) => void;
  onDrop?: (event: React.DragEvent<HTMLDivElement>) => void;
  onDownload?: (file: UploadFile<QiniuUploadResult>) => void;
  onPreview?: (file: UploadFile<QiniuUploadResult>) => void;
  onRemove?: (file: UploadFile<QiniuUploadResult>) => void;
  // 不使用form.item的形式时必传，否则上传结果无法被onChange事件所同步
  onUploadSuccess?: (body: QiniuUploadResult) => void;
  customRequest?: UploadProps['customRequest'];
}

const Uploader = ({
  disabled = false,
  accept,
  type = UploadTokenTypeEnum.IMAGE,
  customerUrl = '',
  uploadData = { namespace: 'post' },
  fileList,
  sizeList = [],
  maxSize = 30,
  maxWidth = 0,
  maxHeight = 0,
  maxCount = 1,
  uploadButtonText = '上传',
  onChange,
  onDrop,
  onDownload,
  onPreview,
  onRemove,
  onUploadSuccess,
  customRequest,
}: UploaderProps) => {
  const [limitSize, setLimitSize] = useState(getByteSize(maxSize, SizeEnum.MB));
  useEffect(() => {
    setLimitSize(getByteSize(maxSize, SizeEnum.MB));
  }, [maxSize]);

  const isSizeValid = async (file: RcFile, sizeList: number[][] = []) => {
    if (!sizeList || !sizeList.length || !file) return true;

    const fileContent = await readFileAsync(file);

    if (!fileContent) return false;

    console.log(fileContent.result, file.type.includes('video'));

    let tempMedia: HTMLVideoElement | HTMLImageElement | null = null;
    let _width = 0;
    let _height = 0;

    if (file.type.includes('video')) {
      tempMedia = await videoAsync(fileContent.result as string);
      if (!tempMedia) return false;
      const { videoWidth, videoHeight } = tempMedia;
      _width = videoWidth;
      _height = videoHeight;
    } else {
      tempMedia = await imageAsync(fileContent.result as string);
      if (!tempMedia) return false;
      const { width, height } = tempMedia;
      _width = width;
      _height = height;
    }

    if (!tempMedia) return false;

    console.log(_width, _height);
    const result = sizeList.some((sizeInfo) => {
      const [validWidth, validHeight] = sizeInfo;
      return validWidth % _width === 0 && validHeight % _height === 0;
    });

    console.log(result);

    return result;

    // console.log(file.url);
    // if (!file.url) return;
    // const templImage = new Image();
    // templImage.src = file.url;
    // templImage.onload = () => {
    //   const { width, height } = templImage;
    //   console.log(width, height);
    // };
    // return true;
  };

  const UploadButton = () => (
    <div className={styles.uploadButton}>
      <PlusOutlined className={styles.uploadButtonIcon} />
      <div className={styles.uploadButtonText}>
        {uploadButtonText || '上传'}
      </div>
    </div>
  );

  const beforeUploadFile = async (file: RcFile) => {
    if (file.size > limitSize) {
      message.warning(`文件大小已经超过了${maxSize}Mb，请重新上传`);
      return false;
    }

    const valid = await isSizeValid(file, sizeList);
    if (!valid) {
      message.warning(`文件尺寸不符合要求，请重新上传`);
    }

    return valid || Upload.LIST_IGNORE;
    // return false;
  };

  const onUploadChange = (
    info: UploadChangeParam<UploadFile<QiniuUploadResult>>,
  ) => {
    const index = info.fileList.findIndex(
      (_file) => _file.uid === info.file.uid,
    );

    // console.log('onUploadChange', info);
    if (info.file.status === 'error' && !info.file.response) {
      info.fileList.splice(index, 1);
    } else if (info.file.status === 'done' && info.file.response) {
      info.file.url = (info.file.response as QiniuUploadResult).url || '';
      info.fileList[index].url = info.file.url;
    }
    if (isFunction(onChange)) onChange(info);
  };

  const onUploadDrop = (event: React.DragEvent<HTMLDivElement>) => {
    if (isFunction(onDrop)) onDrop(event);
  };

  const onUploadDownload = (file: UploadFile<QiniuUploadResult>) => {
    if (isFunction(onDownload)) onDownload(file);
  };

  const onUploadPreview = (file: UploadFile<QiniuUploadResult>) => {
    console.log('onUploadPreview', file);
    const url = file.url || file.thumbUrl || file.response?.url;
    let node = <img style={{ width: '100%' }} src={url} />;
    if (file.type?.startsWith('video/')) {
      node = <video autoPlay style={{ width: '100%' }} controls src={url} />;
    }
    Modal.info({
      title: file.name,
      content: node,
    });
    if (isFunction(onPreview)) onPreview(file);
  };

  const onUploadRemove = (file: UploadFile<QiniuUploadResult>) => {
    if (isFunction(onRemove)) onRemove(file);
  };

  return (
    <Upload
      accept={accept}
      listType={
        type !== UploadTokenTypeEnum.FIRMWARE &&
        type !== UploadTokenTypeEnum.FILE
          ? 'picture-card'
          : 'text'
      }
      disabled={disabled}
      maxCount={maxCount}
      // action="/api/common/upload"
      name="uploadFiles"
      fileList={fileList}
      beforeUpload={beforeUploadFile}
      onChange={onUploadChange}
      onDrop={onUploadDrop}
      onDownload={onUploadDownload}
      onPreview={onUploadPreview}
      onRemove={onUploadRemove}
      customRequest={async (options) => {
        if (customRequest) {
          customRequest(options);
          return;
        }
        const { file, onProgress, onError, onSuccess } = options;
        qiniuOssService.run({
          maxWidth,
          maxHeight,
          file,
          onProgress,
          onError,
          onSuccess,
          type,
          customerUrl,
          uploadData,
          onUploadSuccess,
        });
      }}
    >
      {type !== UploadTokenTypeEnum.FIRMWARE &&
        type !== UploadTokenTypeEnum.FILE && <UploadButton />}
      {type === UploadTokenTypeEnum.FIRMWARE ||
        (type === UploadTokenTypeEnum.FILE && (
          <Button icon={<UploadOutlined />}>
            {uploadButtonText || '上传固件包'}
          </Button>
        ))}
    </Upload>
  );
};

export default Uploader;
