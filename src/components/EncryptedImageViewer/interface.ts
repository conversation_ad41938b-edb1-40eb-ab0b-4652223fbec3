import { ImageProps } from 'antd';

/**
 * 加密图片项接口
 */
export interface EncryptedImageItem {
  /**
   * 加密图片URL
   */
  url: string;
  /**
   * AES密钥
   */
  aesKey: string;
  /**
   * 初始化向量，可选
   */
  iv?: string;
}

export interface EncryptedImageViewerProps {
  /**
   * 单个加密图片或多个加密图片数组
   */
  images?: EncryptedImageItem[] | EncryptedImageItem;
  /**
   * 缩略图宽度，默认150
   */
  width?: number;
  /**
   * 缩略图高度，默认150
   */
  height?: number;
  /**
   * 预览配置，继承自Ant Design的Image.PreviewGroup的属性
   */
  previewProps?: Omit<ImageProps['preview'], 'src'>;
  /**
   * 自定义className
   */
  className?: string;
  /**
   * 自定义style
   */
  style?: React.CSSProperties;
}
