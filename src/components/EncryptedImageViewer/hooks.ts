import { createDecryptedImageUrl } from '@/utils/crypto';
import { useEffect, useState } from 'react';
import { EncryptedImageItem } from './interface';
import { DEFAULT_IV } from './util';

/**
 * 解密图片Hook的返回值接口
 */
interface UseDecryptImagesResult {
  /**
   * 解密后的图片URL数组
   */
  decryptedUrls: string[];
  /**
   * 加载状态
   */
  loading: boolean;
  /**
   * 错误信息
   */
  error: string | null;
}

/**
 * 将单个图片项或图片项数组统一转换为数组
 */
const normalizeImageItems = (
  images?: EncryptedImageItem | EncryptedImageItem[],
): EncryptedImageItem[] => {
  if (!images) return [];
  return Array.isArray(images) ? images : [images];
};

/**
 * 图片解密自定义Hook
 * @param images 加密图片项或图片项数组
 * @param defaultIv 默认初始化向量
 * @returns 解密结果
 */
export const useDecryptImages = (
  images?: EncryptedImageItem | EncryptedImageItem[],
): UseDecryptImagesResult => {
  const [decryptedUrls, setDecryptedUrls] = useState<string[]>([]);
  const [blobUrls, setBlobUrls] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // 追踪组件是否已卸载
    let isMounted = true;
    // 用于存储和清理Blob URL
    const urls: string[] = [];

    const decryptImages = async () => {
      try {
        setLoading(true);
        setError(null);

        const imageItems = normalizeImageItems(images);

        if (imageItems.length === 0) {
          setDecryptedUrls([]);
          setLoading(false);
          return;
        }

        const decryptedUrlsPromises = imageItems.map(async (item) => {
          try {
            const { url, aesKey, iv = DEFAULT_IV } = item;
            const result = await createDecryptedImageUrl(url, aesKey, iv);
            if (result && result.url) {
              if (result.url.startsWith('blob:')) {
                urls.push(result.url); // 存储Blob URL以便稍后清理
              }
              return result.url;
            }
            return '';
          } catch (err) {
            console.error('图片解密失败:', err);
            return ''; // 单个图片解密失败时返回空字符串
          }
        });

        const results = await Promise.all(decryptedUrlsPromises);

        // 检查组件是否仍然挂载
        if (isMounted) {
          setBlobUrls(urls);
          setDecryptedUrls(results);
          setLoading(false);
        }
      } catch (err) {
        console.error('解密图片过程中出错:', err);
        if (isMounted) {
          setError(err instanceof Error ? err.message : '解密图片失败');
          setLoading(false);
        }
      }
    };

    decryptImages();

    // 清理函数
    return () => {
      isMounted = false;
      // 释放所有创建的Blob URL
      blobUrls.forEach((url) => {
        if (url.startsWith('blob:')) {
          URL.revokeObjectURL(url);
        }
      });
    };
  }, [images]);

  return {
    decryptedUrls,
    loading,
    error,
  };
};
