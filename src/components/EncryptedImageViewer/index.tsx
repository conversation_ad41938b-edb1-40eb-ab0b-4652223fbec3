import { Alert, Image, Spin } from 'antd';
import React, { useMemo } from 'react';
import { useDecryptImages } from './hooks';
import './index.less';
import { EncryptedImageItem, EncryptedImageViewerProps } from './interface';

/**
 * 加密图片查看器组件
 * 支持单张和多张加密图片的展示和预览
 */
export const EncryptedImageViewer: React.FC<EncryptedImageViewerProps> = ({
  images,
  width = 150,
  height = 150,
  className = '',
  style = {},
}) => {
  // 兼容旧的API，将src转换为images格式
  const normalizedImages = useMemo<EncryptedImageItem[]>(() => {
    // 优先使用images
    if (images) {
      return Array.isArray(images) ? images : [images];
    }

    return [];
  }, [images]);

  // 使用自定义Hook处理图片解密
  const { decryptedUrls, loading, error } = useDecryptImages(normalizedImages);

  // 如果没有图片
  if (normalizedImages.length === 0) {
    return (
      <Alert
        message="无图片"
        description="未提供图片URL"
        type="warning"
        showIcon
      />
    );
  }

  // 加载中状态
  if (loading) {
    return <Spin tip="图片解密中..." />;
  }

  // 解密错误状态
  if (error) {
    return (
      <Alert message="图片解密失败" description={error} type="error" showIcon />
    );
  }

  // 过滤掉空URL
  const validUrls = decryptedUrls.filter((url) => !!url);

  if (validUrls.length === 0) {
    return (
      <Alert
        message="图片解密失败"
        // description="所有图片解密均失败"
        type="error"
        showIcon
      />
    );
  }

  // 如果有多张图片，使用Image.PreviewGroup
  return (
    <div className={`encrypted-image-viewer ${className || ''}`} style={style}>
      {/* 使用Image.PreviewGroup提供幻灯片预览功能 */}
      <Image.PreviewGroup>
        {validUrls.map((url, index) => (
          <Image
            key={`preview-${index}`}
            src={url}
            width={width}
            height={height}
          />
        ))}
      </Image.PreviewGroup>
    </div>
  );
};

export default EncryptedImageViewer;
