import { createDecryptedImageUrl } from '@/utils/crypto';
import { Alert, Image, Spin, message } from 'antd';
import React, { useEffect, useState } from 'react';

interface DecryptedImageProps {
  /**
   * 加密的图片URL
   */
  src: string;
  /**
   * AES密钥
   */
  aesKey: string;
  /**
   * 初始化向量
   */
  iv: string;
  /**
   * 图片宽度
   */
  width?: number | string;
  /**
   * 图片高度
   */
  height?: number | string;
  /**
   * 图片样式
   */
  style?: React.CSSProperties;
  /**
   * 类名
   */
  className?: string;
  /**
   * 加载失败时的替代文本
   */
  alt?: string;
}

const DecryptedImage: React.FC<DecryptedImageProps> = ({
  src,
  aesKey,
  iv,
  width,
  height,
  style,
  className,
  alt = '图片',
}) => {
  const [imageUrl, setImageUrl] = useState<string>('');
  const [blobInfo, setBlobInfo] = useState<{ url: string; blob: Blob }>({
    url: '',
    blob: new Blob(),
  });
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string>('');

  useEffect(() => {
    if (!blobInfo.blob) return;
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blobInfo.blob);
    link.download = 'test_image.jpg'; // 或 .png
    link.click();
    URL.revokeObjectURL(link.href);
  }, [blobInfo]);

  useEffect(() => {
    let isMounted = true;
    let blobUrl = '';

    const decryptImage = async () => {
      try {
        if (!src) {
          throw new Error('图片URL不能为空');
        }

        if (!aesKey) {
          throw new Error('AES密钥不能为空');
        }

        if (!iv) {
          throw new Error('初始化向量(IV)不能为空');
        }

        setLoading(true);
        setError(false);
        setErrorMessage('');

        console.log('开始解密图片:', src);
        // 解密图片并创建URL
        const _blobInfo = await createDecryptedImageUrl(src, aesKey, iv);
        setBlobInfo(_blobInfo);
        blobUrl = _blobInfo.url;

        if (isMounted) {
          console.log('解密成功，设置图片URL:', blobUrl);
          setImageUrl(blobUrl);
          setLoading(false);
        }
      } catch (err) {
        console.error('图片解密失败:', err);
        if (isMounted) {
          setError(true);
          setLoading(false);
          const errorMsg = err instanceof Error ? err.message : '未知错误';
          setErrorMessage(errorMsg);
          message.error(`图片解密失败: ${errorMsg}`);
          // 释放Blob URL
          if (blobUrl) {
            URL.revokeObjectURL(blobUrl);
          }
        }
      }
    };

    if (src && aesKey && iv) {
      decryptImage();
    } else {
      setError(true);
      setErrorMessage('缺少必要参数：图片URL、AES密钥或初始化向量');
      setLoading(false);
    }

    // 清理函数
    return () => {
      isMounted = false;
      // 释放创建的Blob URL
      if (imageUrl) {
        console.log('组件卸载，释放Blob URL');
        URL.revokeObjectURL(imageUrl);
      }
    };
  }, [src, aesKey, iv]);

  if (loading) {
    return <Spin tip="图片解密中..." />;
  }

  if (error) {
    return (
      <Alert
        message="图片解密失败"
        description={errorMessage || '请检查图片URL、密钥和初始化向量是否正确'}
        type="error"
        showIcon
      />
    );
  }

  return (
    <Image
      src={imageUrl}
      width={width}
      height={height}
      style={style}
      className={className}
      alt={alt}
      onError={() => {
        console.error('图片加载失败');
        setError(true);
        setErrorMessage('图片加载失败，可能解密结果不是有效的图片格式');
      }}
      preview={{ src: imageUrl }}
    />
  );
};

export default DecryptedImage;
