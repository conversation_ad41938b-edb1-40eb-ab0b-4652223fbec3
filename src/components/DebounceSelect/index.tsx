import { SelectOption } from '@/models/common.interface';
import { Select, SelectProps, Spin } from 'antd';
import { debounce } from 'lodash';
import { useMemo, useRef, useState } from 'react';

export interface DebounceSelectProps<ValueType = SelectOption>
  extends Omit<SelectProps<ValueType | ValueType[]>, 'options' | 'children'> {
  fetchOptions: (search: string) => Promise<ValueType[]>;
  debounceTimeout?: number;
}

const DebounceSelect = <ValueType extends SelectOption>({
  fetchOptions,
  debounceTimeout = 800,
  ...props
}: DebounceSelectProps<ValueType>) => {
  const [fetching, setFetching] = useState(false);
  const [options, setOptions] = useState<SelectOption[]>([]);
  const fetchRef = useRef(0);

  const debounceFetcher = useMemo(() => {
    const loadOptions = async (value: string) => {
      fetchRef.current += 1;
      const fetchId = fetchRef.current;
      setOptions([]);
      setFetching(true);

      if (fetchId !== fetchRef.current) {
        // for fetch callback order
        return;
      }

      const optionList: ValueType[] = await fetchOptions(value);

      setOptions(optionList);
      setFetching(false);
    };

    return debounce(loadOptions, debounceTimeout);
  }, [fetchOptions, debounceTimeout]);

  return (
    <Select
      labelInValue
      filterOption={false}
      onSearch={debounceFetcher}
      notFoundContent={fetching ? <Spin size="small" /> : null}
      {...props}
      options={options}
    />
  );
};

export default DebounceSelect;
