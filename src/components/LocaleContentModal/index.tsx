import { SelectOption } from '@/models/common.interface';
import { Form, Modal, Select } from 'antd';
import React, { useEffect } from 'react';
import { LocaleContentForm } from './interface';

interface Props<T extends LocaleContentForm> {
  visible: boolean;
  onConfirm?: (formData: T) => void;
  onCancel: () => void;
  detail?: T;
  localeOptionList: SelectOption[];
  initialFormValues: T;
  children: React.ReactNode;
  title?: string;
}

function LocaleContentModal<T extends LocaleContentForm>({
  localeOptionList,
  visible,
  onCancel,
  detail,
  onConfirm,
  initialFormValues,
  children,
  title = '编辑本地化内容',
}: Props<T>) {
  const [form] = Form.useForm();
  const layout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 20 },
  };

  useEffect(() => {
    if (!detail) return;
    form.setFieldsValue(detail);
  }, [detail]);

  const onOk = async () => {
    try {
      const fd = await form.validateFields();
      if (onConfirm) onConfirm(fd);
      form.resetFields();
    } catch (error: any) {
      const firstError = error.errorFields[0];
      form.scrollToField(firstError.name);
    }
  };

  const onCancelled = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      width={800}
      title={title}
      open={visible}
      onOk={onOk}
      onCancel={onCancelled}
    >
      <Form {...layout} form={form} initialValues={initialFormValues}>
        <Form.Item
          name="language"
          label="语言"
          rules={[{ required: true, message: '请选择语言' }]}
        >
          <Select
            showSearch
            options={localeOptionList}
            filterOption={(input, option) =>
              (option?.value as unknown as string)
                .toLowerCase()
                .includes(input.toLowerCase()) ||
              (option?.label as unknown as string)
                .toLowerCase()
                .includes(input.toLowerCase())
            }
          />
        </Form.Item>
        {children}
      </Form>
    </Modal>
  );
}

export default LocaleContentModal;
