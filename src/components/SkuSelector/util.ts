import { ProductSku } from '@/models/product/interface';
import { serviceTimeUnitNameObj } from '@/models/product/util';
import { ProColumns } from '@ant-design/pro-components';
import dayjs from 'dayjs';

// 选择关联SKU和展示关联SKU所用到的column定义
export const associateSkuTableColumns: Array<ProColumns<ProductSku>> = [
  {
    title: 'Plan ID',
    dataIndex: 'id',
    width: 100,
  },
  {
    title: 'Plan名称',
    dataIndex: 'name',
    width: 130,
  },
  {
    title: '适用设备',
    dataIndex: 'deviceTypes',
    width: 120,
    search: false,
    render: (_, record) =>
      (record.deviceTypes || []).map((deviceType) => deviceType),
  },
  {
    title: 'Plan别名',
    dataIndex: 'aliasName',
    width: 130,
  },
  // {
  //   title: 'Plan能力',
  //   dataIndex: 'capacities',
  //   valueType: 'select',
  //   width: 150,
  //   search: false,
  // },
  {
    title: '服务时长',
    dataIndex: 'serviceTime',
    search: false,
    width: 100,
    render: (_, row) =>
      `${row.serviceTime}${serviceTimeUnitNameObj[row.serviceTimeUnit]}`,
  },
  {
    title: '关联Plan',
    dataIndex: 'relationSkuId',
    search: false,
    width: 100,
    render: (_, row) => row.relationSkuId || '--',
  },
  {
    title: '自动续费',
    dataIndex: 'isRenew',
    search: false,
    width: 100,
    render: (_, row) => (row.price.isReNew ? '是' : '否'),
  },
  {
    title: '价格(元)',
    dataIndex: 'price',
    search: false,
    width: 100,
    render: (_, row) => row.price.price,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    search: false,
    width: 180,
    render: (_, row) => dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss'),
  },
];
