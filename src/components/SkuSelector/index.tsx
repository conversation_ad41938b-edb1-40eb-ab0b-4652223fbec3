import { spanConfig } from '@/models/common.util';
import { fetchAssociableProductSkuList } from '@/models/product/fetch';
import {
  ProductSku,
  RelationProductSkuListParam,
} from '@/models/product/interface';
import { Pagination, initPagination } from '@/utils/request';
import { BaseQueryFilterProps, ProTable } from '@ant-design/pro-components';
import { Button, Modal, Row, message } from 'antd';
import React, { useEffect, useState } from 'react';
import { associateSkuTableColumns } from './util';

interface Props {
  open: boolean;
  param: RelationProductSkuListParam;
  onOk: (sku: ProductSku) => void;
  onCancel: () => void;
}

const SkuSelector: React.FC<Props> = ({
  param,
  open,
  onOk,
  onCancel,
}: Props) => {
  const [dataList, setDataList] = useState<ProductSku[]>([]);
  const [listParam, setListParam] =
    useState<RelationProductSkuListParam>(param);
  const [paginator, setPaginator] = useState<Pagination>(initPagination);
  const [selectedSkuIds, setSelectedSkuIds] = useState<number[]>([]);
  const [selectedSku, setSelectedSku] = useState<ProductSku>();

  const requestAssociateProductSkuList = async (
    _param: RelationProductSkuListParam,
  ) => {
    const { items, ...rest } = await fetchAssociableProductSkuList(_param);
    setDataList(items);
    setPaginator(rest);
  };

  const onCancelled = () => {
    setSelectedSkuIds([]);
    onCancel();
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          const formData = form?.getFieldsValue() || {};
          const { id, name, aliasName } = formData;
          const _param: RelationProductSkuListParam = {
            ...param,
            payload: {
              ...param.payload,
              skuId: id,
              skuName: name,
              skuAlias: aliasName,
            },
          };
          setListParam({
            ..._param,
          });
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          form?.resetFields();
          setListParam({ ...param });
        }}
      >
        重置
      </Button>,
    ];
  };

  const onPaginationChanged = (page: number, pageSize: number) => {
    // 设置分页参数
    let index = page;
    // 如果分页大小不等于列表参数的限制，则设置为1
    if (pageSize !== listParam.limit) {
      index = 1;
    }
    // 设置列表参数
    setListParam({
      ...listParam,
      limit: pageSize,
      offset: (index - 1) * pageSize,
    });
  };

  const onConfirm = () => {
    // 如果没有选择SKU
    if (!selectedSku) {
      // 警告提示
      message.warning('请先选择需要关联的Plan');
      // 终止函数
      return;
    }
    // 调用onOk函数
    onOk(selectedSku);
    // 调用onCancelled函数
    onCancelled();
  };

  const onSelectChanged = (ev: Array<number | string>) => {
    setSelectedSkuIds(ev as number[]);
  };

  useEffect(() => {
    // 请求关联产品SKU列表
    requestAssociateProductSkuList(listParam);
  }, [listParam]);

  useEffect(() => {
    // 根据选中的SKU设置选中的SKU
    const sku = dataList.find((item) => item.id === selectedSkuIds[0]);
    setSelectedSku(sku);
  }, [selectedSkuIds, dataList]);

  const footer = (
    <Row justify="center">
      <Button type="primary" onClick={onConfirm}>
        提交
      </Button>
      <Button onClick={onCancelled}>取消</Button>
    </Row>
  );

  return (
    <Modal
      destroyOnClose
      width={1000}
      open={open}
      footer={footer}
      onCancel={() => onCancel()}
    >
      <ProTable<ProductSku>
        dataSource={dataList}
        columns={associateSkuTableColumns}
        defaultSize="small"
        rowKey="id"
        rowSelection={{ type: 'radio', onChange: onSelectChanged }}
        search={{
          defaultCollapsed: false,
          labelWidth: 100,
          span: spanConfig,
          optionRender: searchOptionRender,
        }}
        scroll={{
          x: associateSkuTableColumns
            .filter((col) => col.dataIndex === 'action')
            .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
        }}
        pagination={{
          pageSize: paginator.limit,
          total: paginator.total,
          current: Math.trunc(paginator.offset / paginator.limit) + 1,
          showQuickJumper: true,
          onChange: onPaginationChanged,
        }}
      />
    </Modal>
  );
};

export default SkuSelector;
