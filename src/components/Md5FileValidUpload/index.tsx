import { fetchQiniuMd5 } from '@/models/app/fetch';
import { QiniuUploadResult } from '@/models/app/interface';
import { UploadTokenTypeEnum } from '@/services/qiniuOss/interface';
import { uuid } from '@/utils/uuid';
import { PlusOutlined } from '@ant-design/icons';
import { Button, Input, InputRef, UploadFile, message } from 'antd';
import React, { ChangeEvent, useEffect, useRef, useState } from 'react';
import Uploader from '../Uploader';
import styles from './index.less';
import { Md5FileResult } from './interface';

interface Props {
  value?: Md5FileResult;
  onChange?: (value: Md5FileResult) => void;
}

const Md5FileValidUpload: React.FC<Props> = ({ value, onChange }: Props) => {
  const inputFileRef = useRef<InputRef>(null);
  const [files, setFiles] = useState<UploadFile[]>([]);

  const onValueChanged = (value: Md5FileResult) => {
    onChange?.(value);
  };

  const uploadMd5File = () => {
    if (!inputFileRef || !inputFileRef.current || !inputFileRef.current.input)
      return;

    inputFileRef.current.input.click();
  };

  const onFileInputChnaged = (ev: ChangeEvent<HTMLInputElement>) => {
    if (!ev.target.files || !ev.target.files.length) return;
    const reader = new FileReader();
    reader.readAsText(ev.target.files[0], 'utf8'); // input.files[0]为第一个文件
    reader.onload = () => {
      if (!reader || !reader.result) return;
      const md5 = (reader.result as string).trim();
      if (value?.md5 === md5) return;
      // 如果Md5和以前的值不一致，则需要重置曾经上传的文件
      onValueChanged({ md5, file: [] });
    };
  };

  const requestUploadFileMd5Info = async (fileUrl: string) => {
    const result = await fetchQiniuMd5(
      `${fileUrl.replace('http:', location.protocol)}?hash/md5`,
    );
    return result;
  };

  const onUploadSuccess = async (response: QiniuUploadResult) => {
    const md5Info = await requestUploadFileMd5Info(response.url);
    if (value?.md5 === md5Info.md5) {
      onValueChanged({
        ...value,
        file: [
          {
            uid: uuid(),
            name: response.name,
            url: response.url,
          },
        ],
      });
    } else {
      message.warning('上传的文件与md5校验文件内容不一致，请重新上传');
    }
  };

  useEffect(() => {
    setFiles(value?.file || []);
  }, [value]);

  return (
    <div>
      <div className={styles.md5UploadButtonWrapper}>
        <Input
          type="file"
          ref={inputFileRef}
          style={{ display: 'none' }}
          onChange={onFileInputChnaged}
        />
        <Button icon={<PlusOutlined />} onClick={uploadMd5File}>
          上传MD5文件
        </Button>
      </div>
      <Input
        placeholder="请输入 MD5 或者上传 MD5 文件"
        value={value?.md5 || ''}
        style={{ marginBottom: 8 }}
        onChange={(ev) => {
          onValueChanged({ md5: ev.target.value, file: [] });
        }}
      />
      {value?.md5 ? (
        <Uploader
          type={UploadTokenTypeEnum.FILE}
          fileList={files}
          onUploadSuccess={onUploadSuccess}
        />
      ) : null}
    </div>
  );
};

export default Md5FileValidUpload;
