import Hls from 'hls.js';
import { useEffect, useRef, useState } from 'react';
import ReactPlayer from 'react-player';

// 需要手动将Hls引入到window上，否则ReactPlayer默认会去加载海外的hls.js依赖的cdn地址，不翻墙就会报错
window.Hls = Hls;

export enum VideoType {
  COMMON = 'common',
  HLS = 'hls',
}

interface Props {
  width?: number;
  height?: number;
  src: string;
  videoType?: VideoType;
  onError?: (error: any) => void;
}

const VideoPlayer = ({
  src,
  width = 800,
  height = 600,
  videoType = VideoType.COMMON,
  onError,
}: Props) => {
  const playerRef = useRef(null);
  const [playing, setPlaying] = useState(true); // 控制是否播放

  useEffect(() => {
    console.log('src changed', src);
  }, [src]);

  //   return <div id="my-video" style={{ width, height }}></div>;
  return (
    <ReactPlayer
      ref={playerRef}
      style={{ width, height }}
      config={{
        file: {
          forceVideo: videoType === VideoType.COMMON,
          forceHLS: videoType === VideoType.HLS,
        },
      }}
      playing={playing}
      url={src}
      controls
      onPlay={() => {
        setPlaying(true);
      }}
      onPause={() => {
        setPlaying(false);
      }}
      onError={(error) => {
        onError?.(error);
      }}
    />
  );
};

export default VideoPlayer;
