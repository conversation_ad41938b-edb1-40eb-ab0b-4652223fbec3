import EllipsisText from '@/components/ellipsisText';
import { StatusEnum } from '@/models/common.interface';
import {
  DEFAULT_PAGE_SIZE,
  getStatusMap,
  spanConfig,
  transferFormDataToParam,
} from '@/models/common.util';
import { fetchSwitchUserGroup, fetchUserGroupList } from '@/models/tag/fetch';
import { UserGroup, UserGroupListParam } from '@/models/tag/interface';
import { initUserGroupListParam } from '@/models/tag/util';
import {
  initPagination,
  initPaginatorParam,
  Pagination,
} from '@/utils/request';
import { PlusOutlined } from '@ant-design/icons';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProTable,
} from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { Button, message, Space, Switch } from 'antd';
import dayjs from 'dayjs';
import { parse } from 'qs';
import React, { useEffect, useMemo, useState } from 'react';
import EstimateCalc from '../EstimateCalc';

const List: React.FC = () => {
  const [dataList, setDataList] = useState<UserGroup[]>([]);
  const [listParam, setListParam] = useState<UserGroupListParam>(
    initUserGroupListParam,
  );
  const [pagination, setPagination] = useState<Pagination>(initPagination);
  // const [selectedRows, setSelectedRows] = useState<UserGroup[]>([]);
  const queryInfo = parse(location.href.split('?')[1]);

  // 处理页面链接中的参数逻辑
  const resolveQueryInfo = () => {
    const action = queryInfo.action as string;
    if (action === 'create') {
      history.replace(`/user/group/edit/0`);
    }
  };

  // 获取用户分组列表数据
  const requestUserGroupList = async (
    param: UserGroupListParam = initUserGroupListParam,
  ) => {
    const { items, ...rest } = await fetchUserGroupList(param);
    setDataList(items || []);
    setPagination(rest);
  };

  // 切换分群状态
  const requestSwitchStatus = async (id: number, status: StatusEnum) => {
    try {
      await fetchSwitchUserGroup(id, status);
      requestUserGroupList(listParam);
      message.success(status === StatusEnum.ENABLE ? '启用成功' : '禁用成功');
    } catch (e) {
      console.log(e);
    }
  };

  // 编辑用户分群
  const editUserGroup = (id: number) => {
    history.push(`/user/group/edit/${id}`);
    // history.push(`/user/group/edit/${id}`);
  };

  // 批量选择事件 处理
  // const onSelectedRowChanged = (selectedRowKeys: React.Key[], srs: UserGroup[]) => {
  //   setSelectedRows(srs);
  // };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          const _param = transferFormDataToParam<any, UserGroupListParam>(
            form?.getFieldsValue(),
          );
          if (!_param.name) _param.name = initUserGroupListParam.name;
          if (_param.enable === undefined)
            _param.enable = initUserGroupListParam.enable;
          setListParam({ ..._param, ...initPaginatorParam });
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          form?.resetFields();
          setListParam(initUserGroupListParam);
        }}
      >
        重置
      </Button>,
    ];
  };

  const onPaginatorChanged = (page: number, pageSize: number) => {
    const currentIndex = listParam.limit !== pageSize ? 1 : page;
    const param: UserGroupListParam = {
      ...listParam,
      limit: pageSize,
      offset: pageSize * (currentIndex - 1),
    };
    setListParam(param);
  };

  const columns: Array<ProColumns<UserGroup>> = useMemo(
    () => [
      {
        title: 'ID',
        dataIndex: 'id',
        width: 50,
        search: {
          transform: (value) => ({ tagId: value }),
        },
        formItemProps: {
          name: 'tagId',
        },
        // renderFormItem: (_, { defaultRender, ...rest }) => (
        //   <Input {...rest} type="number" allowClear />
        // ),
      },
      {
        title: '分群名称',
        dataIndex: 'name',
        width: 180,
        render: (text) => (
          <EllipsisText tooltip width={180} tooltipShow>
            {text}
          </EllipsisText>
        ),
      },
      // {
      //   title: '分群定义',
      //   dataIndex: 'description',
      //   width: 250,
      //   search: false,
      // },
      {
        title: '预估人数',
        dataIndex: 'estimate',
        search: false,
        width: 150,
        render: (_, row) => (
          <EstimateCalc
            isReload={false}
            rowId={row.id}
            estimate={row.estimate}
          />
        ),
      },
      {
        title: '状态',
        dataIndex: 'enabled',
        valueEnum: getStatusMap(),
        width: 150,
        formItemProps: {
          name: 'enable',
        },
        render: (text, row) => (
          <Switch
            checkedChildren="开启"
            unCheckedChildren="关闭"
            checked={!!row.enable}
            onChange={() => requestSwitchStatus(row.id, Number(!row.enable))}
          />
        ),
      },
      {
        title: '创建时间',
        dataIndex: 'createTime',
        search: false,
        width: 200,
        render: (text, row) =>
          dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss'),
      },
      {
        title: '更新时间',
        dataIndex: 'updateTime',
        search: false,
        width: 200,
        render: (text, row) =>
          dayjs(row.updateTime).format('YYYY-MM-DD HH:mm:ss'),
      },
      {
        title: '操作',
        dataIndex: 'action',
        search: false,
        width: 140,
        render: (_, row) => (
          <Space split="|" size={0}>
            <Button type="link" onClick={() => editUserGroup(row.id)}>
              编辑
            </Button>
          </Space>
        ),
      },
    ],
    [dataList],
  );

  useEffect(() => {
    resolveQueryInfo();
  }, []);

  useEffect(() => {
    requestUserGroupList(listParam);
  }, [listParam]);

  return (
    <ProTable<UserGroup>
      dataSource={dataList}
      columns={columns}
      defaultSize="small"
      rowKey="id"
      search={{
        defaultCollapsed: false,
        span: spanConfig,
        optionRender: searchOptionRender,
      }}
      pagination={{
        pageSize: pagination.limit,
        total: pagination.total,
        defaultPageSize: DEFAULT_PAGE_SIZE,
        showSizeChanger: true,
        showQuickJumper: true,
        onChange: onPaginatorChanged,
      }}
      scroll={{
        x: columns
          .filter((col) => col.dataIndex === 'action')
          .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
      }}
      toolbar={{
        actions: [
          <Button
            key="button"
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => editUserGroup(0)}
          >
            新增
          </Button>,
        ],
      }}
    />
  );
};

export default List;
