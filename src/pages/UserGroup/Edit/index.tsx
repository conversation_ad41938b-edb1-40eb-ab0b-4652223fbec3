import {
  CascaderOption,
  SelectOption,
  ValueType,
} from '@/models/common.interface';
import { ConnectState } from '@/models/connect';
import {
  fetchCreateUserGroup,
  fetchLogicCondition,
  fetchUpdateUserGroup,
  fetchUserGroupDetail,
} from '@/models/tag/fetch';
import {
  LogicOperator,
  LogicSpecInfo,
  LogicSpecTypeEnum,
  UserGroupDetail,
} from '@/models/tag/interface';
import { MinusCircleOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { history, useDispatch, useParams, useSelector } from '@umijs/max';
import {
  Alert,
  Button,
  Cascader,
  Col,
  DatePicker,
  Form,
  Input,
  InputNumber,
  message,
  Row,
  Select,
  Space,
  Spin,
  Tag,
} from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { cloneDeep } from 'lodash';
import React, { useEffect, useMemo, useState } from 'react';
import {
  ConditionValueFormTypeEnum,
  DimensionTypeForm,
  LogicSpecOption,
  OneRowConditionDataGroup,
  UserGroupForm,
} from '../interface';
import {
  getNumberOptions,
  getRowDataConditions,
  initialDimensionValueForm,
  initialUserGroupForm,
  initialUserGroupFormData,
  initOneConditionData,
  initOneRowConditionDataGroup,
  transferDetailToFormData,
  transferFormDataToParam,
} from '../util';
import styles from './index.less';

const Edit: React.FC = () => {
  const initTimeLogicOptions: SelectOption[] = [{ label: '至今', value: -1 }];
  const param = useParams<{ id: string }>();
  // const [calculating, setCalculating] = useState(false);
  const [loading, setLoading] = useState(false);
  const dispatch = useDispatch();
  const dimensionOptions: SelectOption[] = useSelector(
    ({ tag }: ConnectState) => tag.dimensionOptions,
  );
  const dimensionValueOptionsByDimensionId: Partial<{
    [key: number]: CascaderOption[];
  }> = useSelector(
    ({ tag }: ConnectState) => tag.dimensionValueOptionsByDimensionId,
  );
  const [detail, setDetail] = useState<UserGroupDetail>();
  const [oneRowConditionDataGroupList, setOneRowConditionDataGroupList] =
    useState<OneRowConditionDataGroup[]>([]);
  // 逻辑/时间条件选项
  const [allLogicSpecList, setAllLogicSpecList] = useState<LogicSpecInfo[]>([]);
  const [logicSpecOption, setLogicSpecOption] = useState<LogicSpecOption>({
    0: [],
    1: [],
    2: [],
    3: [],
  });
  // const [invalid, setInvalid] = useState(true);
  const [form] = Form.useForm<UserGroupForm>();
  const dimensionTypeList = Form.useWatch('dimensionTypeList', form);
  const layout = {
    labelCol: { span: 2 },
    wrapperCol: { span: 22 },
  };
  const conditionLogicOptions: SelectOption[] = [
    {
      label: 'AND',
      value: LogicOperator.AND,
    },
    {
      label: 'OR',
      value: LogicOperator.OR,
    },
  ];
  const presetDateRangeList = useMemo(() => {
    return [
      {
        label: '今天',
        value: [dayjs().startOf('d'), dayjs().endOf('d')],
      },
      {
        label: '近1周',
        value: [dayjs().subtract(1, 'week').startOf('d'), dayjs().endOf('d')],
      },
      {
        label: '近1个月',
        value: [dayjs().subtract(1, 'month').startOf('d'), dayjs().endOf('d')],
      },
      {
        label: '近3个月',
        value: [dayjs().subtract(3, 'months').startOf('d'), dayjs().endOf('d')],
      },
      {
        label: '近6个月',
        value: [dayjs().subtract(6, 'months').startOf('d'), dayjs().endOf('d')],
      },
      {
        label: '近1年',
        value: [dayjs().subtract(1, 'year').startOf('d'), dayjs().endOf('d')],
      },
    ];
  }, []);

  // 初始化FormData
  const initialFormData = (
    _dimensionOptions: Array<SelectOption<ValueType>>,
  ) => {
    const formData: UserGroupForm = initialUserGroupFormData(_dimensionOptions);
    // 更新form表单数据
    form.setFieldsValue({
      ...formData,
    });
  };

  // 初始化FormData对应的RowData
  const initialRowData = (
    _dimensionOptions: Array<SelectOption<ValueType>>,
    _dimensionValueOptionsByDimensionId: Partial<{
      [key: number]: CascaderOption[];
    }>,
  ) => {
    const _rowDataByConditions = initOneRowConditionDataGroup(
      _dimensionOptions,
      _dimensionValueOptionsByDimensionId,
    );
    setOneRowConditionDataGroupList(_rowDataByConditions);
  };

  // 获取逻辑条件数据
  const requestLogicCondition = async () => {
    const logicConditionList = await fetchLogicCondition({
      type: LogicSpecTypeEnum.SELECTOR,
    });
    const timeLogicConditionList = await fetchLogicCondition({
      type: LogicSpecTypeEnum.DATEPICKER,
    });
    const _logicSpecOption = { ...logicSpecOption };
    const _options: SelectOption[] = logicConditionList.map((item) => ({
      value: item.id,
      label: item.spec,
    }));
    _logicSpecOption[LogicSpecTypeEnum.SELECTOR] = _options;
    const _timeOptions: SelectOption[] = timeLogicConditionList.map((item) => ({
      value: item.id,
      label: item.spec,
    }));
    _logicSpecOption[LogicSpecTypeEnum.DATEPICKER] =
      initTimeLogicOptions.concat(_timeOptions);
    setLogicSpecOption({ ..._logicSpecOption });
    setAllLogicSpecList([...logicConditionList, ...timeLogicConditionList]);
  };

  // 快速设置条件
  const setRowCondition = (
    index: number,
    nestIndex: number,
    value: number | null,
  ) => {
    const formData = form.getFieldsValue();
    formData.dimensionTypeList[index].dimensionValueList[
      nestIndex
    ].conditionType = value;
    form.setFieldsValue(formData);
  };

  // 快速选择时间段操作
  const setRowValue = (
    index: number,
    nestIndex: number,
    value: number | Dayjs | Dayjs[] | null,
  ) => {
    const formData = form.getFieldsValue();
    formData.dimensionTypeList[index].dimensionValueList[nestIndex].value =
      value;
    form.setFieldsValue(formData);
  };

  // 控制每行中的range picker展示逻辑
  const setRowRangePickerOpen = (
    ev: boolean,
    index: number,
    nestIndex: number,
  ) => {
    const _oneRowConditionDataGroupList = cloneDeep(
      oneRowConditionDataGroupList,
    );
    _oneRowConditionDataGroupList[index].oneRowDataList[nestIndex].isRangeOpen =
      ev;
    setOneRowConditionDataGroupList(_oneRowConditionDataGroupList);
  };

  // 根据id获取详情
  const requestDetailById = async (id: number) => {
    const _detail = await fetchUserGroupDetail(id);
    setDetail(_detail);
  };

  const getRowDateBySelection = (
    index: number,
    nestIndex: number,
    selectedCascaderOptions: CascaderOption[] = [],
    selectedLogicOption?: SelectOption,
  ) => {
    const _oneRowConditionDataGroupList = cloneDeep(
      oneRowConditionDataGroupList,
    );
    // 获取当前所选的那一行的数据信息
    const _oneRowConditionDataGroup = {
      ..._oneRowConditionDataGroupList[index],
    };
    const _oneRowConditionData = {
      ..._oneRowConditionDataGroup.oneRowDataList[nestIndex],
    };

    const _selectedCascaderOptions = selectedCascaderOptions.length
      ? selectedCascaderOptions
      : _oneRowConditionData.selectedDimensionValueOptions;
    // 最后一个维度的选项
    // debugger;
    const _secondDimension = _selectedCascaderOptions[0];
    // const _lastLevelDimensionOption = _selectedCascaderOptions[1] || _secondDimension;
    const _rowDataByCondition = {
      ...initOneConditionData,
      ..._oneRowConditionData,
    };
    // 将所选的一二级属性结果赋到整行的数据上
    _rowDataByCondition.selectedDimensionValueOptions = [
      ..._selectedCascaderOptions,
    ];

    // 处理属性选择后的处理逻辑
    // 一级被选中id为value[0]，二级为value[1]
    const isTimeDimension = (_secondDimension.label as string).includes('时间');
    // 条件需要被默认选择
    let _selectedLogicOption = selectedLogicOption;
    if (isTimeDimension) {
      _rowDataByCondition.conditionLogicOptions =
        logicSpecOption[LogicSpecTypeEnum.DATEPICKER];
      _selectedLogicOption =
        _selectedLogicOption ||
        _rowDataByCondition.conditionLogicOptions.find(
          (item) => item.label === '区间',
        );
      setRowCondition(
        index,
        nestIndex,
        (_selectedLogicOption?.value as number) || null,
      );
    } else {
      _rowDataByCondition.conditionLogicOptions =
        logicSpecOption[LogicSpecTypeEnum.SELECTOR];
      _selectedLogicOption =
        _selectedLogicOption ||
        _rowDataByCondition.conditionLogicOptions.find(
          (item) => item.label === '大于',
        );
      setRowCondition(
        index,
        nestIndex,
        (_selectedLogicOption?.value as number) || null,
      );
    }

    // 处理条件选择后的处理逻辑
    if (_selectedLogicOption?.label === '至今') {
      // 日期选择
      _rowDataByCondition.valueFormType = ConditionValueFormTypeEnum.DATEPICKER;
      // 需要重置下改行的row值
      setRowValue(index, nestIndex, null);
    } else if (_selectedLogicOption?.label === '区间') {
      // 日期段选择
      _rowDataByCondition.valueFormType =
        ConditionValueFormTypeEnum.DATERANGEPICKER;
      // 需要重置下改行的row值
      setRowValue(index, nestIndex, []);
    } else if (_selectedLogicOption?.label === '最近') {
      // 天数输入框
      _rowDataByCondition.valueFormType =
        ConditionValueFormTypeEnum.NUMBERINPUT;
      // 需要重置下改行的row值
      setRowValue(index, nestIndex, null);
    } else {
      // 默认选择框
      _rowDataByCondition.valueFormType = ConditionValueFormTypeEnum.SELECTOR;
      _rowDataByCondition.valueSelectOptions = getNumberOptions(
        _secondDimension.label?.toString(),
      );
      // 需要重置下改行的row值
      setRowValue(index, nestIndex, null);
    }

    _oneRowConditionDataGroupList[index].oneRowDataList[nestIndex] = {
      ..._rowDataByCondition,
    };

    return _oneRowConditionDataGroupList;
  };

  // 选择维度属性值时的回调
  const onDimensionValueChanged = (
    value: Array<string | number>,
    selectOptions: CascaderOption[],
    index: number,
    nestIndex: number,
  ) => {
    // 重置逻辑条件选择器和值选择器
    const formData = form.getFieldsValue();
    formData.dimensionTypeList[index].dimensionValueList[
      nestIndex
    ].conditionType = null;
    form.setFieldsValue({ ...formData });
    setRowValue(index, nestIndex, null);

    if (!selectOptions || !selectOptions.length) {
      // 删除操作
      return;
    }

    const _rowDataByConditions = getRowDateBySelection(
      index,
      nestIndex,
      selectOptions,
    );
    setOneRowConditionDataGroupList(_rowDataByConditions);
  };

  // 逻辑选择的回调
  const onLogicSelected = (
    value: ValueType,
    option: SelectOption,
    index: number,
    nestIndex: number,
  ) => {
    const _rowDataByConditions = getRowDateBySelection(
      index,
      nestIndex,
      [],
      option,
    );
    setOneRowConditionDataGroupList(_rowDataByConditions);
  };

  // 提交
  const submit = async (formData: UserGroupForm) => {
    setLoading(true);
    const _param = transferFormDataToParam(
      formData,
      { allLogicSpecList, oneRowConditionDataGroupList },
      +(param?.id || 0),
    );
    // console.log(formData, _param);
    let state = '';
    try {
      if (param && param.id && +param.id) {
        state = '更新';
        await fetchUpdateUserGroup(_param);
      } else {
        state = '创建';
        await fetchCreateUserGroup(_param);
      }

      message.success(`${state}成功！`);
      history.back();
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  // 增加子条件
  const addNestCondition = (
    add: (defaultValue?: any, insertIndex?: number | undefined) => void,
    index: number,
  ) => {
    const _oneRowConditionDataGroupList = cloneDeep(
      oneRowConditionDataGroupList,
    );
    _oneRowConditionDataGroupList[index].oneRowDataList.push({
      ...initOneConditionData,
    });
    setOneRowConditionDataGroupList(_oneRowConditionDataGroupList);
    add(initialDimensionValueForm);
  };

  // 删除子条件
  const removeNestCondition = (
    remove: (index: number | number[]) => void,
    index: number,
    nestIndex: number,
  ) => {
    const _oneRowConditionDataGroupList = cloneDeep(
      oneRowConditionDataGroupList,
    );
    const _oneRowConditionDataGroup = _oneRowConditionDataGroupList[index];
    _oneRowConditionDataGroup.oneRowDataList.splice(nestIndex, 1);
    setOneRowConditionDataGroupList([..._oneRowConditionDataGroupList]);
    remove(nestIndex);
  };

  const resetForm = () => {
    form.resetFields();
    // setLogicSpecOption({ ...logicSpecOption });
    initialFormData(dimensionOptions);
    initialRowData(dimensionOptions, dimensionValueOptionsByDimensionId);
  };

  // // 增加条件
  // const addCondition = (add: Function) => {
  //   setRowDataByConditions(
  //     cloneDeep([...rowDataByConditions, [...initOneConditionData[0], ...initOneConditionData[0]]]),
  //   );
  //   // add(initialTargetConditionGroupForm);
  //   add({
  //     ...initialTargetConditionGroupForm,
  //     targetConditionList: [{ ...initialTargetConditionForm }, { ...initialTargetConditionForm }],
  //   });
  // };

  // // 删除条件
  // const removeCondition = (remove: Function, index: number) => {
  //   const _rowDataByConditions = cloneDeep(rowDataByConditions);
  //   _rowDataByConditions.splice(index, 1);
  //   setRowDataByConditions(_rowDataByConditions);
  //   remove(index);
  // };

  useEffect(() => {
    dispatch({ type: 'tag/requestDimensionAndValueList' });
    // 获取逻辑条件 时间 + 大小等于
    requestLogicCondition();
  }, [dispatch]);

  useEffect(() => {
    if (form && dimensionOptions && dimensionOptions.length) {
      initialFormData(dimensionOptions);
      initialRowData(dimensionOptions, dimensionValueOptionsByDimensionId);
    }

    if (
      detail &&
      dimensionOptions &&
      dimensionOptions.length &&
      logicSpecOption &&
      dimensionValueOptionsByDimensionId &&
      JSON.stringify(dimensionValueOptionsByDimensionId) !== '{}'
    ) {
      const _rowDataConditions = getRowDataConditions(
        detail,
        dimensionOptions,
        logicSpecOption,
        dimensionValueOptionsByDimensionId,
      );
      setOneRowConditionDataGroupList(_rowDataConditions);

      const formData = transferDetailToFormData(
        detail,
        dimensionOptions,
        dimensionValueOptionsByDimensionId,
      );
      form.setFieldsValue(formData);
    }
  }, [
    dimensionOptions,
    form,
    dimensionValueOptionsByDimensionId,
    detail,
    logicSpecOption,
  ]);

  useEffect(() => {
    if (param && param.id && +param.id) {
      requestDetailById(+param.id);
    }
  }, [param]);

  return (
    <ProCard>
      <Spin spinning={false}>
        <Form
          className={styles.form}
          {...layout}
          form={form}
          onFinish={submit}
          initialValues={initialUserGroupForm}
        >
          <Form.Item
            name="name"
            label="分群名称"
            rules={[{ required: true, message: '请输入用户分群名称' }]}
          >
            <Input showCount placeholder="请输入用户分群名称" />
          </Form.Item>
          <Form.Item label="目标用户">
            <Row
              style={{ background: '#f9f9f9', padding: '16px' }}
              align="middle"
            >
              {(dimensionTypeList || []).length > 1 && (
                <Col span={2}>
                  <Form.Item name="operator">
                    <Select bordered={false} options={conditionLogicOptions} />
                  </Form.Item>
                </Col>
              )}
              <Col span={21}>
                <Form.List
                  name="dimensionTypeList"
                  rules={[
                    {
                      validator: async (rule, value: DimensionTypeForm[]) => {
                        if (
                          value.some((item) =>
                            item.dimensionValueList.some(
                              (_item) =>
                                _item.dimensionAttrValueList &&
                                _item.dimensionAttrValueList.length &&
                                _item.conditionType &&
                                (_item.value !== null ||
                                  _item.value !== undefined),
                            ),
                          )
                        ) {
                          return Promise.resolve();
                        }
                        return Promise.reject(new Error('请添加至少一个条件'));
                      },
                    },
                  ]}
                >
                  {(fields, _, { errors }) => (
                    <>
                      {fields
                        .filter((field) => field)
                        .map((field, index) => (
                          <Row key={field.key} align="middle">
                            <Col span={4}>
                              <Form.Item
                                name={[field.name, 'dimensionTypeId']}
                                // rules={[{ required: true, message: '请选择维度属性' }]}
                              >
                                <Select options={dimensionOptions} />
                              </Form.Item>
                            </Col>
                            <Col span={2}>
                              {dimensionTypeList[index].dimensionValueList
                                .length > 1 && (
                                <Form.Item name={[field.name, 'operator']}>
                                  <Select
                                    bordered={false}
                                    options={conditionLogicOptions}
                                  />
                                </Form.Item>
                              )}
                            </Col>
                            <Col span={18}>
                              <Form.List
                                {...field}
                                name={[field.name, 'dimensionValueList']}
                                // rules={[
                                //   {
                                //     validator: async (rule, value) => {
                                //       if (value.length) {
                                //         return Promise.resolve();
                                //       }
                                //       return Promise.reject(new Error('请添加至少一个条件'));
                                //     },
                                //   },
                                // ]}
                              >
                                {(
                                  nestFields,
                                  { add: nestAdd, remove: nestRemove },
                                ) => (
                                  <>
                                    {(nestFields || [])
                                      .filter((nestField) => nestField)
                                      .map((nestField, nestIndex) => (
                                        <Row
                                          key={nestField.key}
                                          justify="space-between"
                                        >
                                          <Col span={7}>
                                            <Form.Item
                                              name={[
                                                nestField.name,
                                                'dimensionAttrValueList',
                                              ]}
                                              // rules={[
                                              //   { required: true, message: '请选择一个维度属性值' },
                                              // ]}
                                            >
                                              <Cascader
                                                placeholder="请选择维度属性值"
                                                options={
                                                  oneRowConditionDataGroupList[
                                                    index
                                                  ]?.dimensionValueOptions
                                                }
                                                onChange={(
                                                  value,
                                                  selectedOptions,
                                                ) => {
                                                  onDimensionValueChanged(
                                                    value,
                                                    selectedOptions,
                                                    index,
                                                    nestIndex,
                                                  );
                                                }}
                                              />
                                            </Form.Item>
                                          </Col>
                                          <Col span={4}>
                                            <Form.Item
                                              name={[
                                                nestField.name,
                                                'conditionType',
                                              ]}
                                              // rules={[{ required: true, message: '请选择设置条件' }]}
                                            >
                                              <Select
                                                placeholder="请选择一种条件"
                                                options={
                                                  oneRowConditionDataGroupList[
                                                    index
                                                  ] &&
                                                  oneRowConditionDataGroupList[
                                                    index
                                                  ].oneRowDataList &&
                                                  oneRowConditionDataGroupList[
                                                    index
                                                  ].oneRowDataList[nestIndex] &&
                                                  oneRowConditionDataGroupList[
                                                    index
                                                  ].oneRowDataList[nestIndex]
                                                    .conditionLogicOptions
                                                    ? oneRowConditionDataGroupList[
                                                        index
                                                      ].oneRowDataList[
                                                        nestIndex
                                                      ].conditionLogicOptions
                                                    : []
                                                }
                                                onSelect={(
                                                  value: number,
                                                  option: SelectOption,
                                                ) =>
                                                  onLogicSelected(
                                                    value,
                                                    option,
                                                    index,
                                                    nestIndex,
                                                  )
                                                }
                                              />
                                            </Form.Item>
                                          </Col>
                                          {oneRowConditionDataGroupList[index]
                                            ?.oneRowDataList[nestIndex]
                                            ?.valueFormType &&
                                            (oneRowConditionDataGroupList[index]
                                              .oneRowDataList[nestIndex]
                                              .valueFormType ===
                                              ConditionValueFormTypeEnum.SELECTOR ||
                                              oneRowConditionDataGroupList[
                                                index
                                              ].oneRowDataList[nestIndex]
                                                .valueFormType ===
                                                ConditionValueFormTypeEnum.NONE) && (
                                              <Col span={10}>
                                                <Form.Item
                                                  name={[
                                                    nestField.name,
                                                    'value',
                                                  ]}
                                                  // rules={[{ required: true, message: '请选择值' }]}
                                                >
                                                  <Select
                                                    placeholder="请选择预设值"
                                                    options={
                                                      oneRowConditionDataGroupList[
                                                        index
                                                      ].oneRowDataList[
                                                        nestIndex
                                                      ].valueSelectOptions
                                                    }
                                                  />
                                                </Form.Item>
                                              </Col>
                                            )}
                                          {oneRowConditionDataGroupList[index]
                                            ?.oneRowDataList[nestIndex]
                                            ?.valueFormType &&
                                            oneRowConditionDataGroupList[index]
                                              .oneRowDataList[nestIndex]
                                              .valueFormType ===
                                              ConditionValueFormTypeEnum.NUMBERINPUT && (
                                              <Col span={10}>
                                                <Form.Item
                                                  name={[
                                                    nestField.name,
                                                    'value',
                                                  ]}
                                                  // rules={[{ required: true, message: '请选择值' }]}
                                                >
                                                  <InputNumber
                                                    min={0}
                                                    placeholder="请输入大于等于0的整数"
                                                    addonAfter="天"
                                                  />
                                                </Form.Item>
                                              </Col>
                                            )}
                                          {oneRowConditionDataGroupList[index]
                                            ?.oneRowDataList[nestIndex]
                                            ?.valueFormType &&
                                            oneRowConditionDataGroupList[index]
                                              .oneRowDataList[nestIndex]
                                              .valueFormType ===
                                              ConditionValueFormTypeEnum.DATEPICKER && (
                                              <Col span={10}>
                                                <Form.Item
                                                  name={[
                                                    nestField.name,
                                                    'value',
                                                  ]}
                                                  // rules={[
                                                  //   { required: true, message: '请选择开始时间' },
                                                  // ]}
                                                >
                                                  <DatePicker
                                                    placeholder="请选择开始时间"
                                                    showToday={false}
                                                    disabledDate={(
                                                      currentDate: Dayjs,
                                                    ) =>
                                                      currentDate.valueOf() >
                                                      dayjs()
                                                        .endOf('d')
                                                        .valueOf()
                                                    }
                                                  />
                                                </Form.Item>
                                              </Col>
                                            )}
                                          {oneRowConditionDataGroupList[index]
                                            ?.oneRowDataList[nestIndex]
                                            ?.valueFormType &&
                                            oneRowConditionDataGroupList[index]
                                              .oneRowDataList[nestIndex]
                                              .valueFormType ===
                                              ConditionValueFormTypeEnum.DATERANGEPICKER && (
                                              <Col span={10}>
                                                <Form.Item
                                                  name={[
                                                    nestField.name,
                                                    'value',
                                                  ]}
                                                  // rules={[
                                                  //   { required: true, message: '请选择时间段' },
                                                  // ]}
                                                >
                                                  <DatePicker.RangePicker
                                                    open={
                                                      oneRowConditionDataGroupList[
                                                        index
                                                      ].oneRowDataList[
                                                        nestIndex
                                                      ].isRangeOpen
                                                    }
                                                    placeholder={[
                                                      '开始时间',
                                                      '结束时间',
                                                    ]}
                                                    onOpenChange={(ev) =>
                                                      setRowRangePickerOpen(
                                                        ev,
                                                        index,
                                                        nestIndex,
                                                      )
                                                    }
                                                    renderExtraFooter={() => (
                                                      <section>
                                                        {presetDateRangeList.map(
                                                          (tag) => (
                                                            <Tag
                                                              style={{
                                                                cursor:
                                                                  'pointer',
                                                              }}
                                                              key={tag.label}
                                                              color="blue"
                                                              onClick={() => {
                                                                setRowValue(
                                                                  index,
                                                                  nestIndex,
                                                                  tag.value,
                                                                );
                                                                setRowRangePickerOpen(
                                                                  false,
                                                                  index,
                                                                  nestIndex,
                                                                );
                                                              }}
                                                            >
                                                              {tag.label}
                                                            </Tag>
                                                          ),
                                                        )}
                                                      </section>
                                                    )}
                                                  />
                                                </Form.Item>
                                              </Col>
                                            )}
                                          <Col span={2}>
                                            <Form.Item>
                                              <PlusCircleOutlined
                                                className={styles.icon}
                                                onClick={() =>
                                                  addNestCondition(
                                                    nestAdd,
                                                    index,
                                                  )
                                                }
                                                size={20}
                                              />
                                              {nestFields.length > 1 ? (
                                                <MinusCircleOutlined
                                                  className={styles.icon}
                                                  onClick={() =>
                                                    removeNestCondition(
                                                      nestRemove,
                                                      index,
                                                      nestIndex,
                                                    )
                                                  }
                                                  size={20}
                                                />
                                              ) : (
                                                ''
                                              )}
                                            </Form.Item>
                                          </Col>
                                        </Row>
                                      ))}
                                  </>
                                )}
                              </Form.List>
                            </Col>
                          </Row>
                        ))}
                      <Space align="center" style={{ marginBottom: 10 }}>
                        <Button
                          size="large"
                          // onClick={() => addCondition(add)}
                          disabled={!!(dimensionTypeList || []).length}
                        >
                          添加条件
                        </Button>
                        <Alert
                          style={{
                            backgroundColor: 'transparent',
                            border: 'none',
                          }}
                          message="“或”关系，取并集；“与”关系，取交集"
                          type="info"
                          showIcon
                        />
                      </Space>
                      <Form.ErrorList errors={errors} />
                    </>
                  )}
                </Form.List>
              </Col>
            </Row>
          </Form.Item>
          <Form.Item style={{ textAlign: 'right' }}>
            <Button
              type="primary"
              htmlType="submit"
              style={{ marginRight: 16 }}
              loading={loading}
              disabled={loading}
            >
              提交
            </Button>
            <Button onClick={resetForm} style={{ marginRight: 16 }} danger>
              重置
            </Button>
            <Button type="default" onClick={history.back}>
              取消
            </Button>
          </Form.Item>
        </Form>
      </Spin>
    </ProCard>
  );
};

export default Edit;
