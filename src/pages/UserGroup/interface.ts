import { CascaderOption, SelectOption } from '@/models/common.interface';
import { LogicOperator, LogicSpecTypeEnum } from '@/models/tag/interface';
import { Dayjs } from 'dayjs';

export enum ConditionEnum {
  BIGGER_THAN = 'BIGGER_THAN',
  EQUAL = 'EQUAL',
  LESS_THAN = 'LESS_THAN',
  BIGGER_EQUAL = 'BIGGER_EQUAL',
  LESS_EQUAL = 'LESS_EQUAL',
}

export interface UserGroupForm {
  name: string;
  operator: LogicOperator;
  dimensionTypeList: DimensionTypeForm[];
}

export interface DimensionTypeForm {
  dimensionTypeId: number;
  operator: LogicOperator;
  dimensionValueList: DimensionValueForm[];
}

export interface DimensionValueForm {
  dimensionAttrValueList: number[];
  conditionType: number | null;
  value: number | Dayjs | Dayjs[] | null;
}

export enum ConditionValueFormTypeEnum {
  NONE = 'none',
  SELECTOR = 'selector',
  DATEPICKER = 'datePicker',
  NUMBERINPUT = 'numberInput',
  DATERANGEPICKER = 'dateRangePicker',
}

// 逻辑条件数据
export type LogicSpecOption = {
  [key in LogicSpecTypeEnum]: SelectOption[];
};

export enum DimensionTypeEnum {
  PET = 'PET',
  DEVICE = 'DEVICE',
  USER = 'USER',
}

export interface OneRowConditionDataGroup {
  dimensionTypeId: number;
  dimensionValueOptions: CascaderOption[];
  oneRowDataList: OneRowData[];
}

// 单行条件数据提供
export interface OneRowData {
  // 所选择的维度属性值信息
  selectedDimensionValueOptions: CascaderOption[];
  // 逻辑条件选项
  conditionLogicOptions: SelectOption[];
  // 值的form表单类型
  valueFormType: ConditionValueFormTypeEnum;
  // 值的form表单选择器的选项
  valueSelectOptions: SelectOption[];
  // 控制rangePicker日历的展示与否
  isRangeOpen: boolean;
}
