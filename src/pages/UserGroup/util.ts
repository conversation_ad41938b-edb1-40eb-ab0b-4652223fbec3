import { CascaderOption, SelectOption } from '@/models/common.interface';
import {
  EstimateCountCalculationParam,
  LogicInfoAttrParam,
  LogicOperator,
  LogicSpecInfo,
  LogicSpecTypeEnum,
  UserGroupDetail,
  UserGroupDetailLogicInfoAttr,
  UserGroupParam,
} from '@/models/tag/interface';
import dayjs from 'dayjs';
import { isArray, isObject } from 'lodash';
import {
  ConditionValueFormTypeEnum,
  DimensionTypeForm,
  DimensionValueForm,
  LogicSpecOption,
  OneRowConditionDataGroup,
  OneRowData,
  UserGroupForm,
} from './interface';

export const initialDimensionValueForm: DimensionValueForm = {
  dimensionAttrValueList: [],
  conditionType: null,
  value: null,
};

export const initialDimensionTypeForm: DimensionTypeForm = {
  dimensionTypeId: 0,
  operator: LogicOperator.OR,
  dimensionValueList: [],
};

export const initialUserGroupForm: UserGroupForm = {
  name: '',
  operator: LogicOperator.OR,
  dimensionTypeList: [],
};

export const initialUserGroupFormData = (
  dimensionOptions: SelectOption[],
  initialValue = true,
): UserGroupForm => {
  const formData: UserGroupForm = {
    ...initialUserGroupForm,
    dimensionTypeList: dimensionOptions.map((dimensionOption) => ({
      ...initialDimensionTypeForm,
      dimensionTypeId: +dimensionOption.value,
      dimensionValueList: initialValue
        ? [{ ...initialDimensionValueForm }]
        : [],
    })),
  };
  return formData;
};

export const getNumberOptions = (dimensionName?: string): SelectOption[] => {
  const length = 20 * 12;
  let numberOptions: SelectOption[] = [];
  for (let i = 0; i <= length; i++) {
    numberOptions.push({ value: i, label: i.toString() });
  }

  if (dimensionName && dimensionName.includes('年龄')) {
    // 为年龄时，需要去除option.value为0的情况
    numberOptions = numberOptions
      .filter(
        (option) =>
          +option.value !== 0 &&
          (+option.value <= 11 || +option.value % 12 === 0),
      )
      .map((option) => ({
        ...option,
        label:
          +option.value < 12
            ? `${option.value}个月`
            : `${+option.value / 12}岁`,
      }));
  } else {
    numberOptions = numberOptions.slice(0, 21);
  }
  return numberOptions;
};

export const initOneConditionData: OneRowData = {
  // 所选择的纬度属性值信息
  selectedDimensionValueOptions: [],
  // 逻辑条件选项
  conditionLogicOptions: [],
  // 值的form表单类型
  valueFormType: ConditionValueFormTypeEnum.SELECTOR,
  // 值的form表单选择器的选项
  valueSelectOptions: [],
  // 控制rangePicker日历的展示与否
  isRangeOpen: false,
};

export const transferFormDataToCalculateParam = (
  formData: UserGroupForm,
  extraData: {
    allLogicSpecList: LogicSpecInfo[];
    oneRowConditionDataGroupList: OneRowConditionDataGroup[];
  } = { allLogicSpecList: [], oneRowConditionDataGroupList: [] },
): EstimateCountCalculationParam => {
  const { allLogicSpecList, oneRowConditionDataGroupList } = extraData;
  const param: EstimateCountCalculationParam = {
    logicInfo: formData.dimensionTypeList.map((dimensionType, index) =>
      dimensionType.dimensionValueList
        .filter(
          (dimensionValue) =>
            dimensionValue.dimensionAttrValueList &&
            dimensionValue.dimensionAttrValueList.length &&
            dimensionValue.conditionType &&
            dimensionValue.value !== undefined,
        )
        .map((dimensionValue) => {
          const oneRowConditionDataGroup = oneRowConditionDataGroupList[index];
          // const oneRowConditionData = oneRowConditionDataGroup.oneRowDataList[nestIndex];

          let logicSpecInfo = null;
          if (dimensionValue.conditionType === -1) {
            logicSpecInfo = allLogicSpecList.find(
              (info) => info.logicSpec === 'between',
            );
          } else {
            logicSpecInfo = allLogicSpecList.find(
              (info) => info.id === dimensionValue.conditionType,
            );
          }
          // let logicConditionDescription = '';
          // let valueDescription = '';
          const logicInfoAttrParam: LogicInfoAttrParam = {
            typeId: oneRowConditionDataGroup.dimensionTypeId,
            detailId:
              dimensionValue.dimensionAttrValueList[
                dimensionValue.dimensionAttrValueList.length - 1
              ] || 0,
            logicId: dimensionValue.conditionType || 0,
            value: [0],
            operator: dimensionType.operator,
          };
          if (isArray(dimensionValue.value)) {
            logicInfoAttrParam.value = dimensionValue.value.map((item) =>
              item.valueOf(),
            );
            // logicConditionDescription = '=';
            // valueDescription = dimensionValue.value
            //   .map((item) => dayjs(item).format('YYYY-MM-DD'))
            //   .join(' - ');
          } else if (isObject(dimensionValue.value)) {
            logicInfoAttrParam.value = [
              dimensionValue.value.startOf('d').valueOf(),
            ];
            // logicConditionDescription = '=';
            // valueDescription = `${dayjs(dimensionValue.value).format('YYYY-MM-DD')} - 至今`;
          } else if (dimensionValue.value && logicSpecInfo?.spec === '最近') {
            // 有最近几天的，也有数量的，也有年龄的
            logicInfoAttrParam.value = [dimensionValue.value];
            // logicConditionDescription = '=';
            // valueDescription = `最近${dimensionValue.value}天`;
          } else if (
            dimensionValue.value !== undefined &&
            dimensionValue.value !== null
          ) {
            logicInfoAttrParam.value = [dimensionValue.value];
            // const valueOption = oneConditionData?.valueSelectOptions.find(
            //   (option) => option.value === dimensionValue.value,
            // );
            // logicConditionDescription =
            //   logicSpecInfo?.logicSpec !== undefined ? logicSpecInfo?.logicSpec : '';
            // valueDescription = valueOption?.label !== undefined ? valueOption?.label : '';
          }
          if (dimensionValue.conditionType === -1) {
            const betweenLogic = allLogicSpecList.find(
              (item) => item.logicSpec === 'between',
            );
            if (betweenLogic) logicInfoAttrParam.logicId = betweenLogic.id;
            logicInfoAttrParam.value.push(-1);
          }
          // if (logicSpecInfo && oneConditionData) {
          //   _condition.description = getDescription(
          //     condition,
          //     oneConditionData,
          //     logicConditionDescription,
          //     valueDescription,
          //   );
          // }
          // `${oneConditionData.selectedDimensionOptions[oneConditionData.selectedDimensionOptions.length - 1].label}${logicSpecInfo?.logicSpec}${}`
          return logicInfoAttrParam;
        }),
    ),
  };
  param.logicInfo = param.logicInfo.filter((item) => item && item.length);
  return param;
};

// 将formData转换为param
export const transferFormDataToParam = (
  formData: UserGroupForm,
  extraData: {
    allLogicSpecList: LogicSpecInfo[];
    oneRowConditionDataGroupList: OneRowConditionDataGroup[];
  },
  id?: number,
): UserGroupParam => {
  const param: UserGroupParam = {
    ...transferFormDataToCalculateParam(formData, extraData),
    operator: formData.operator,
    tagName: formData.name,
  };
  if (id) param.tagId = id;
  return param;
};

export const getSelectedDimensionValueOptions = (
  detailId: number,
  dimensionValueOptionsByDimensionId: CascaderOption[],
): CascaderOption[] => {
  // if (detailId === 19) debugger;
  if (
    !dimensionValueOptionsByDimensionId ||
    !dimensionValueOptionsByDimensionId.length
  )
    return [];

  const selectedDimensionValueOptions: CascaderOption[] = [];
  const dict: Partial<{ [key: string]: CascaderOption[] | undefined }> = {};

  dimensionValueOptionsByDimensionId.forEach((dimensionValue) => {
    if (!dimensionValue.value) return;
    if (!dimensionValue.children?.length) {
      const valueOption = dimensionValueOptionsByDimensionId.find(
        (item) => item.value === detailId,
      );
      if (valueOption) selectedDimensionValueOptions.unshift(valueOption);
    } else {
      dict[+dimensionValue.value] = dimensionValue.children;
    }
  });

  for (const secondDimensionId in dict) {
    if (secondDimensionId && dict[secondDimensionId]) {
      const options = dict[secondDimensionId];
      const option = (options || []).find((item) => item.value === detailId);

      if (option) {
        selectedDimensionValueOptions.unshift(option);
        const valueOption = dimensionValueOptionsByDimensionId.find(
          (item) => item.value === +secondDimensionId,
        );
        if (valueOption) selectedDimensionValueOptions.unshift(valueOption);
      }
    }
  }

  // dimensionValueOptionsByDimensionId.forEach((item) => {
  //   if (item.children && item.children.length) {
  //     const _selectedDimensionValueOptions = getSelectedDimensionValueOptions(
  //       detailId,
  //       item.children,
  //     );
  //     if (selectedDimensionValueOptions && selectedDimensionValueOptions.length) {
  //       selectedDimensionValueOptions.unshift(..._selectedDimensionValueOptions);
  //       selectedDimensionValueOptions.unshift(item);
  //     }
  //   } else if (item.value === detailId) {
  //     console.log('getSelectedDimensionValueOptions', detailId, cloneDeep(item));
  //     selectedDimensionValueOptions.unshift(item);
  //   }
  // });
  return selectedDimensionValueOptions;
};

// 将接口返回的详情数据转换为formData
export const transferDetailToFormData = (
  detail: UserGroupDetail,
  dimensionOptions: SelectOption[],
  dimensionValueOptionsByDimensionId: Partial<{
    [key: number]: CascaderOption[];
  }>,
): UserGroupForm => {
  // const dimensionTypeIdList: number[] = [];
  const initFormData: UserGroupForm =
    initialUserGroupFormData(dimensionOptions);
  // debugger;
  const formData: UserGroupForm = {
    ...initFormData,
    name: detail.tagName,
    operator: detail.operator || initFormData.operator,
    dimensionTypeList: initFormData.dimensionTypeList.map((dimensionType) => {
      const _dimensionType: DimensionTypeForm = {
        ...dimensionType,
        dimensionValueList: [],
      };
      detail.logicInfo.forEach((oneLogicInfo) => {
        oneLogicInfo.attrList
          .filter((attr) => attr.typeId === dimensionType.dimensionTypeId)
          .forEach((attr) => {
            _dimensionType.operator = attr.operator || _dimensionType.operator;
            const _dimensionValue: DimensionValueForm = {
              dimensionAttrValueList: getSelectedDimensionValueOptions(
                attr.detailId,
                dimensionValueOptionsByDimensionId[
                  dimensionType.dimensionTypeId
                ] || [],
              ).map((item) => +(item?.value || 0)),
              conditionType: attr.logicId,
              value: null,
            };

            if (attr.value.length > 1 && !attr.value.includes(-1)) {
              // 区间
              _dimensionValue.value = attr.value.map((item) => dayjs(item));
            } else if (attr.value.length > 1 && attr.value.includes(-1)) {
              // 至今
              _dimensionValue.conditionType = -1;
              _dimensionValue.value = dayjs(attr.value[0]);
            } else if (attr.value) {
              // 天数 or 月份 or 数值
              _dimensionValue.value = attr.value[0];
            }
            _dimensionType.dimensionValueList.push(_dimensionValue);
          });
      });

      if (!_dimensionType.dimensionValueList.length) {
        _dimensionType.dimensionValueList.push({
          ...initialDimensionValueForm,
        });
      }
      return _dimensionType;
    }),
  };

  // detail.logicInfo.map<DimensionTypeForm>((info) => {
  //   let dimensionTypeId = 0;
  //   const dimensionTypeInfo = {
  //     operator: info.attrList[0]?.operator || LogicOperator.OR,
  //     dimensionTypeId,
  //     dimensionValueList: info.attrList.map((attr) => {
  //       const dimension = dimensionOptions.find((item) => item.value === attr.typeId);
  //       dimension && (dimensionTypeId = dimension.value as number);
  //       const _attr: DimensionValueForm = {
  //         dimensionAttrValueList: getSelectedDimensionValueOptions(
  //           attr.detailId,
  //           dimensionValueOptionsByDimensionId[dimensionTypeId] || [],
  //         ).map((item) => +(item?.value || 0)),
  //         conditionType: attr.logicId,
  //         value: null,
  //       };
  //       if (attr.value.length > 1 && !attr.value.includes(-1)) {
  //         // 区间
  //         _attr.value = attr.value.map((item) => dayjs(item));
  //       } else if (attr.value.length > 1 && attr.value.includes(-1)) {
  //         // 至今
  //         _attr.conditionType = -1;
  //         _attr.value = dayjs(attr.value[0]);
  //       } else if (attr.value) {
  //         // 天数 or 月份 or 数值
  //         _attr.value = attr.value[0];
  //       }
  //       return _attr;
  //     }),
  //   };
  //   dimensionTypeInfo.dimensionTypeId = dimensionTypeId;
  //   dimensionTypeIdList.push(dimensionTypeId);
  //   return dimensionTypeInfo;
  // }),

  // dimensionOptions
  //   .filter((dimensionOption) => !dimensionTypeIdList.includes(+dimensionOption.value))
  //   .forEach((dimensionOption) => {
  //     formData.dimensionTypeList.push({
  //       ...initialDimensionTypeForm,
  //       dimensionTypeId: +dimensionOption.value,
  //       dimensionValueList: [
  //         {
  //           ...initialDimensionValueForm,
  //         },
  //       ],
  //     });
  //   });

  return formData;
};

// 初始化页面首次后的每行条件选项数据
export const initOneRowConditionDataGroup = (
  dimensionOptions: SelectOption[],
  dimensionValueOptionsByDimensionId: Partial<{
    [key: number]: CascaderOption[];
  }>,
  initialOneRow = true,
): OneRowConditionDataGroup[] => {
  const _rowDataByConditions: OneRowConditionDataGroup[] = [];
  dimensionOptions.forEach((dimensionOption) => {
    _rowDataByConditions.push({
      dimensionTypeId: +dimensionOption.value,
      dimensionValueOptions:
        dimensionValueOptionsByDimensionId[+dimensionOption.value] || [],
      oneRowDataList: initialOneRow
        ? [
            {
              ...initOneConditionData,
            },
          ]
        : [],
    });
  });
  return _rowDataByConditions;
};

// 根据attr.value获取值的结果
const getValueInfoByAttrValue = (
  attr: UserGroupDetailLogicInfoAttr,
  logicSpecOptions: SelectOption[],
  selectedDimensionOptions: CascaderOption[],
): {
  valueFormType: ConditionValueFormTypeEnum;
  valueSelectOptions: SelectOption[];
} => {
  let valueFormType: ConditionValueFormTypeEnum =
    ConditionValueFormTypeEnum.NONE;
  let valueSelectOptions: SelectOption[] = [];
  const selectedLogicOption = logicSpecOptions.find(
    (item) => item.value === attr.logicId,
  );
  const secondDimensionOption = selectedDimensionOptions[0];
  // console.log('before attr.value', attr, attr.value);
  // if (selectedLogicOption?.label === '至今') {
  if (attr.value.includes(-1)) {
    // 至今 日期选择
    valueFormType = ConditionValueFormTypeEnum.DATEPICKER;
  } else if (selectedLogicOption?.label === '区间') {
    // 日期段选择
    valueFormType = ConditionValueFormTypeEnum.DATERANGEPICKER;
  } else if (selectedLogicOption?.label === '最近') {
    // 天数输入框
    valueFormType = ConditionValueFormTypeEnum.NUMBERINPUT;
  } else {
    // 默认选择框
    valueFormType = ConditionValueFormTypeEnum.SELECTOR;
    valueSelectOptions = getNumberOptions(
      secondDimensionOption?.label?.toString(),
    );
  }
  return {
    valueFormType,
    valueSelectOptions,
  };
};

const getLogicSpecOptions = (
  logicSpecOption: LogicSpecOption,
  secondDimension: CascaderOption,
): SelectOption[] => {
  let logicSpecOptions: SelectOption[] = [];
  const isTimeDimension =
    secondDimension && (secondDimension.label as string).includes('时间');
  if (isTimeDimension) {
    // _rowDataByCondition.dimensionOptionType = LogicSpecTypeEnum.DATEPICKER;
    logicSpecOptions = logicSpecOption[LogicSpecTypeEnum.DATEPICKER];
  } else {
    // _rowDataByCondition.dimensionOptionType = LogicSpecTypeEnum.SELECTOR;
    logicSpecOptions = logicSpecOption[LogicSpecTypeEnum.SELECTOR];
  }
  return logicSpecOptions;
};

// 根据接口返回的详情获取rowDataConditions
export const getRowDataConditions = (
  detail: UserGroupDetail,
  dimensionOptions: SelectOption[],
  logicSpecOption: LogicSpecOption,
  dimensionValueOptionsByDimensionId: Partial<{
    [key: number]: CascaderOption[];
  }>,
): OneRowConditionDataGroup[] => {
  const oneRowConditionDataGroupList = initOneRowConditionDataGroup(
    dimensionOptions,
    dimensionValueOptionsByDimensionId,
    false,
  );

  detail.logicInfo.forEach((info) => {
    info.attrList.forEach((attr) => {
      const oneRowConditionDataGroup = oneRowConditionDataGroupList.find(
        (group) => group.dimensionTypeId === attr.typeId,
      );

      if (!oneRowConditionDataGroup) return;
      // 获取所选择的纬度属性值信息
      const selectedDimensionOptions: CascaderOption[] =
        getSelectedDimensionValueOptions(
          attr.detailId,
          dimensionValueOptionsByDimensionId[
            oneRowConditionDataGroup.dimensionTypeId
          ] || [],
        );

      const secondDimension = selectedDimensionOptions[0];

      // 获取逻辑类型
      const logicSpecOptions = getLogicSpecOptions(
        logicSpecOption,
        secondDimension,
      );

      // 获取值相关信息
      const { valueFormType, valueSelectOptions } = getValueInfoByAttrValue(
        attr,
        logicSpecOptions,
        selectedDimensionOptions,
      );

      oneRowConditionDataGroup.oneRowDataList.push({
        // 所选择的纬度属性值信息
        selectedDimensionValueOptions: selectedDimensionOptions,
        // 逻辑条件选项
        conditionLogicOptions: logicSpecOptions,
        // 值的form表单类型
        valueFormType,
        // 值的form表单选择器的选项
        valueSelectOptions,
        // 控制rangePicker日历的展示与否
        isRangeOpen: false,
      });
    });
  });

  oneRowConditionDataGroupList.forEach((group) => {
    if (!group.oneRowDataList.length)
      group.oneRowDataList = [{ ...initOneConditionData }];
  });

  // console.log('getRowDataConditions', cloneDeep(oneRowConditionDataGroupList));
  return oneRowConditionDataGroupList;
};

// export const getDescription = (
//   conditionForm: TargetConditionForm,
//   oneConditionData: OneConditionData,
//   logicConditionDescription: string,
//   valueDescription: string,
// ): string => {
//   // 获取一二级维度信息
//   const selectedDimensionList = oneConditionData.selectedDimensionOptions;
//   const dimensionDescription = selectedDimensionList[selectedDimensionList.length - 1].label;
//   // 获取逻辑条件信息
//   // 获取值的信息
//   return `${dimensionDescription}${logicConditionDescription}${valueDescription}`;
// };
