import { LeftOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { Outlet, useLocation } from '@umijs/max';
import React, { useEffect, useState } from 'react';

const oldRoutes = [
  {
    path: '',
    breadcrumbName: '用户',
  },
  {
    path: '',
    breadcrumbName: '用户分群',
  },
];

const UserGroup: React.FC = () => {
  const location = useLocation();
  const [title, setTitle] = useState('用户分群管理');
  const [showBackIcon, setShowBackIcon] = useState(false);
  const [routes, setRoutes] = useState(oldRoutes);
  useEffect(() => {
    if (!location) return;
    if (!location?.pathname.includes('list')) {
      setShowBackIcon(true);
      setRoutes([...oldRoutes, { path: '', breadcrumbName: '分群详情' }]);
      setTitle('用户分群详情');
    } else {
      setShowBackIcon(false);
      setRoutes(oldRoutes);
      setTitle('用户分群管理');
    }
  }, [location]);

  return (
    <PageContainer
      header={{
        backIcon: showBackIcon ? <LeftOutlined /> : '',
        onBack: () => history.back(),
        title,
        ghost: true,
        breadcrumb: {
          itemRender: (route) => <span>{route.title}</span>,
          routes,
        },
      }}
    >
      <Outlet />
    </PageContainer>
  );
};

export default UserGroup;
