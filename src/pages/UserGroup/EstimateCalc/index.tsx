import { fetchEstimatePeopleCountByTagId } from '@/models/tag/fetch';
import { ReloadOutlined } from '@ant-design/icons';
import React, { useEffect, useState } from 'react';

interface Props {
  rowId: number;
  estimate: number;
  isReload?: boolean;
}

const EstimateCalc: React.FC<Props> = ({ estimate, rowId, isReload = true }: Props) => {
  const [number, setNumber] = useState(estimate);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setNumber(estimate);
  }, [estimate]);
  // 刷新人数
  const reloadPeopleCount = async (id: number) => {
    setLoading(true);
    try {
      const result = await fetchEstimatePeopleCountByTagId(id);
      setNumber(result);
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };
  return (
    <>
      {number === -1 ? '计算中...' : number}
      {isReload && !loading && (
        <ReloadOutlined style={{ marginLeft: 8 }} onClick={() => reloadPeopleCount(rowId)} />
      )}
    </>
  );
};

export default EstimateCalc;
