import { ProColumns, ProTable } from '@ant-design/pro-components';
import { Button } from 'antd';
import React, { useEffect } from 'react';

interface Props {
  deviceId: number;
}

const CloudServiceList: React.FC<Props> = ({ deviceId }: Props) => {
  const columns: Array<ProColumns<any>> = [
    {
      title: 'SKU ID',
      dataIndex: 'id',
    },
    {
      title: 'SKU名称',
      dataIndex: 'name',
    },
    {
      title: 'SKU能力',
      dataIndex: 'name',
    },
    {
      title: '订单号',
      dataIndex: 'name',
    },
    {
      title: '价格（元）',
      dataIndex: 'name',
    },
    {
      title: '支付方式',
      dataIndex: 'name',
    },
    {
      title: '自动续费',
      dataIndex: 'name',
    },
    {
      title: '订阅状态',
      dataIndex: 'name',
    },
    {
      title: '服务状态',
      dataIndex: 'name',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
    },
    {
      title: '到期时间',
      dataIndex: 'expiredTime',
    },
    {
      title: '操作',
      dataIndex: 'action',
      render: (_, row) => (
        <Button type="link" onClick={() => showServiceDetail(row.id)}>
          服务详情
        </Button>
      ),
    },
  ];

  useEffect(() => {
    console.log(deviceId);
  }, [deviceId]);

  const showServiceDetail = (skuId: number) => {
    console.log(skuId);
  };

  return (
    <ProTable
      dataSource={[]}
      columns={columns}
      headerTitle={false}
      search={false}
      options={false}
      pagination={false}
    />
  );
};

export default CloudServiceList;
