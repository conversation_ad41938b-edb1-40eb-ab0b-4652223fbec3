import {
  AdminGroupDetail,
  AdminGroupParam,
  Permission,
} from '@/models/admin/interface';
// import { AdminGroupDetail, AdminGroupParam } from '@/models/adminGroup/interface';
import { TreeNode } from '@/models/common.interface';
import { flatTreeNode } from '@/models/common.util';
import { TreeProps } from 'antd';
import { isArray, isObject } from 'lodash';
import { AdminGroupForm } from './interface';

export const initialAdminGroupForm: AdminGroupForm = {
  name: '',
  root: false,
};

export const transferPermissionsToApiPermissionTree = (
  permissionList: Permission[] = [],
  parentPermission: Permission | null = null,
): TreeNode[] => {
  const apiPermissionTree: TreeNode[] = permissionList.map((permission) => ({
    key: parentPermission
      ? `${parentPermission.key}/${permission.key}`
      : permission.key,
    value: parentPermission
      ? `${parentPermission.key}/${permission.key}`
      : permission.key,
    title: permission.name,
    children: transferPermissionsToApiPermissionTree(
      permission.permissions,
      permission,
    ),
    nativeTitle: permission.name,
    nativePermission: permission.key,
  }));

  if (!parentPermission) {
    return [
      {
        title: '全部权限',
        value: 'allPermission',
        key: 'allPermission',
        children: apiPermissionTree,
        nativeTitle: '全部',
        nativePermission: 'allPermission',
      },
    ];
  }

  return apiPermissionTree;
};

// 将formData转换为param
export const transferFormDataToParam = (
  formData: AdminGroupForm,
  _apiPermissionList: TreeProps['checkedKeys'],
  _menuPermissionList: TreeProps['checkedKeys'],
  id: number,
): AdminGroupParam => {
  let apiPermissionList: string[] = [];
  let menuPermissionList: string[] = [];
  if (isArray(_apiPermissionList)) {
    apiPermissionList = [..._apiPermissionList].map((item) => `${item}`);
  } else if (isObject(_apiPermissionList)) {
    apiPermissionList = [..._apiPermissionList.checked]
      .concat([..._apiPermissionList.halfChecked])
      .map((item) => `${item}`);
  }
  if (isArray(_menuPermissionList)) {
    menuPermissionList = [..._menuPermissionList].map((item) => `${item}`);
  } else if (isObject(_menuPermissionList)) {
    menuPermissionList = [..._menuPermissionList.checked]
      .concat([..._menuPermissionList.halfChecked])
      .map((item) => `${item}`);
  }
  const param: AdminGroupParam = {
    name: formData.name,
    root: formData.root,
    permissions: '',
  };
  if (!formData.root) {
    const permissionList: string[] = [...apiPermissionList].concat(
      menuPermissionList
        .filter((mp) => mp !== 'all' && !mp.includes('noPermission'))
        .map((mp) => mp.split('-')[0]),
    );
    param.permissions = Array.from(new Set(permissionList)).join('|');
  }
  if (id) param.id = id;
  return param;
};

// 将接口返回的详情数据转换为formData
export const transferDetailToFormData = (
  detail: AdminGroupDetail,
): AdminGroupForm => {
  const formData: AdminGroupForm = {
    name: detail.name,
    root: detail.root,
  };
  return formData;
};

export const getPermissionList = (
  detail?: AdminGroupDetail,
  permissionTree?: TreeNode[],
): string[] => {
  if (!detail || !permissionTree || !permissionTree.length || detail.root)
    return [];
  const detailPermissionList = detail.permissions
    .split('|')
    .map((item) => (item.indexOf('/') === 0 ? item.substring(1) : item));
  const permissionList: string[] = [];
  flatTreeNode(permissionTree, (node) => {
    if (node.key && (!node.children || !node.children?.length)) {
      const nodeKey = node.key.toString().split('-')[0];
      if (detailPermissionList.includes(nodeKey)) {
        if (permissionList.includes(nodeKey))
          permissionList.push(`${nodeKey}-${node.nativeTitle}`);
        else permissionList.push(nodeKey);
      }
    }
  });
  return permissionList;
};
