import {
  fetchAdminGroupDetail,
  fetchAdminGroupSaving,
  fetchPermissionList,
} from '@/models/admin/fetch';
import { AdminGroupDetail } from '@/models/admin/interface';
import {
  ApiSuccessEnum,
  SelectOption,
  TreeNode,
} from '@/models/common.interface';
import { ConnectState } from '@/models/connect';
import { ProCard } from '@ant-design/pro-components';
import { history, useDispatch, useParams, useSelector } from '@umijs/max';
import { Button, Col, Form, Input, Radio, Row, Tree, message } from 'antd';
import React, { useEffect, useState } from 'react';
import { AdminGroupForm } from '../interface';
import {
  getPermissionList,
  initialAdminGroupForm,
  transferDetailToFormData,
  transferFormDataToParam,
  transferPermissionsToApiPermissionTree,
} from '../util';
// import styles from './index.less';

const Edit: React.FC = () => {
  const dispatch = useDispatch();
  const param = useParams<{ id: string }>();
  const [form] = Form.useForm<AdminGroupForm>();
  const [loading, setLoading] = useState(false);
  const [detail, setDetail] = useState<AdminGroupDetail>();
  const [selectedApiPermissionList, setSelectedApiPermissionList] = useState<
    string[]
  >([]);
  const [selectedMenuPermissionList, setSelectedMenuPermissionList] = useState<
    string[]
  >([]);
  const expandedPermission = 'allPermission';
  const [expandedApiPermissionList, setExpandedApiPermissionList] = useState<
    string[]
  >([]);
  const [expandedMenuPermissionList, setExpandedMenuPermissionList] = useState<
    string[]
  >([]);
  const [apiPermissionTree, setApiPermissionTree] = useState<TreeNode[]>([]);
  const menuPermissionTree: TreeNode[] = useSelector(
    ({ admin }: ConnectState) => admin.menuPermissionTree,
  );
  const root = Form.useWatch('root', form);
  const layout = {
    labelCol: { span: 3 },
    wrapperCol: { span: 20 },
  };

  const rootOptions: Array<SelectOption<boolean>> = [
    {
      value: false,
      label: '管理员',
    },
    {
      value: true,
      label: '超级管理员',
    },
  ];

  // 获取权限Tree
  const requestPermissionList = async () => {
    const permissionList = await fetchPermissionList();
    setApiPermissionTree(
      transferPermissionsToApiPermissionTree(permissionList),
    );
  };

  // 根据id获取详情数据
  const requestDetailById = async (id: number) => {
    const _detail = await fetchAdminGroupDetail(id);
    setDetail(_detail);
    const formData = transferDetailToFormData(_detail);
    form.setFieldsValue(formData);
  };

  // 提交form表单
  const submit = async (formData: AdminGroupForm) => {
    setLoading(true);
    const _param = transferFormDataToParam(
      formData,
      selectedApiPermissionList,
      selectedMenuPermissionList,
      +(param?.id || 0),
    );
    // console.log(_param);
    let result = '';
    let state = '';
    result = await fetchAdminGroupSaving(_param);
    try {
      if (param && param.id && +param.id) {
        state = '更新';
      } else {
        state = '创建';
      }

      if (result === ApiSuccessEnum.success) {
        message.success(`${state}成功！`);
        history.back();
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    requestPermissionList();
    if (param && param.id && +param.id) {
      requestDetailById(+param.id);
    }
  }, [param]);

  useEffect(() => {
    // 额外dva接口请求
    dispatch({ type: 'admin/getMenuPermissionTree' });
  }, [dispatch]);

  useEffect(() => {
    const _selectedApiPermissionList = getPermissionList(
      detail,
      apiPermissionTree,
    );
    const _selectedMenuPermissionList = getPermissionList(
      detail,
      menuPermissionTree,
    );

    setSelectedApiPermissionList(_selectedApiPermissionList);
    setSelectedMenuPermissionList(_selectedMenuPermissionList);
  }, [detail, apiPermissionTree, menuPermissionTree]);

  return (
    <ProCard>
      <Form
        {...layout}
        form={form}
        onFinish={submit}
        initialValues={initialAdminGroupForm}
      >
        <Form.Item
          name="name"
          label="名称"
          rules={[{ required: true, message: '请输入名称' }]}
        >
          <Input showCount maxLength={40} placeholder="请输入名称" />
        </Form.Item>
        <Form.Item
          name="root"
          label="类型"
          rules={[{ required: true, message: '请输入名称' }]}
        >
          {/* <Select options={rootOptions} /> */}
          <Radio.Group>
            {rootOptions.map((option) => (
              <Radio.Button key={option.label} value={option.value}>
                {option.label}
              </Radio.Button>
            ))}
          </Radio.Group>
        </Form.Item>
        {}
        <Row style={{ marginBottom: 32 }}>
          <Col offset={2} span={10}>
            <ProCard title="选择接口权限值">
              {!root ? (
                <Tree
                  height={450}
                  checkable
                  treeData={apiPermissionTree}
                  onCheck={(checkedKeys) =>
                    setSelectedApiPermissionList(checkedKeys as string[])
                  }
                  checkedKeys={selectedApiPermissionList}
                  expandedKeys={[
                    expandedPermission,
                    ...expandedApiPermissionList,
                  ]}
                  onExpand={(expandedKeys) =>
                    setExpandedApiPermissionList(expandedKeys as string[])
                  }
                />
              ) : (
                <div style={{ color: 'red' }}>所有权限</div>
              )}
            </ProCard>
          </Col>
          <Col span={10}>
            <ProCard title="选择菜单权限值">
              {!root ? (
                <Tree
                  height={450}
                  checkable
                  treeData={menuPermissionTree}
                  onCheck={(checkedKeys) =>
                    setSelectedMenuPermissionList(checkedKeys as string[])
                  }
                  checkedKeys={selectedMenuPermissionList}
                  expandedKeys={[
                    expandedPermission,
                    ...expandedMenuPermissionList,
                  ]}
                  onExpand={(expandedKeys) =>
                    setExpandedMenuPermissionList(expandedKeys as string[])
                  }
                />
              ) : (
                <div style={{ color: 'red' }}>所有权限</div>
              )}
            </ProCard>
          </Col>
        </Row>
        <Form.Item wrapperCol={{ span: 24 }} style={{ textAlign: 'center' }}>
          <Button
            type="primary"
            htmlType="submit"
            style={{ marginRight: 16 }}
            loading={loading}
            disabled={loading}
          >
            提交
          </Button>
          <Button htmlType="reset" style={{ marginRight: 16 }} danger>
            重置
          </Button>
          <Button type="default" onClick={history.back}>
            取消
          </Button>
        </Form.Item>
      </Form>
    </ProCard>
  );
};

export default Edit;
