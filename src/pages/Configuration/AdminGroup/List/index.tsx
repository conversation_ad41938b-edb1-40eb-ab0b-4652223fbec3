import {
  fetchAdminGroupDeleting,
  fetchAdminGroupList,
} from '@/models/admin/fetch';
import { AdminGroup, AdminGroupListParam } from '@/models/admin/interface';
import { initAdminGroupListParam } from '@/models/admin/util';
import { Pagination, initPagination } from '@/utils/request';
import { PlusOutlined } from '@ant-design/icons';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { Button, Popconfirm, Space, message } from 'antd';
import React, { useEffect, useState } from 'react';

const List: React.FC = () => {
  const [adminGroupList, setAdminGroupList] = useState<AdminGroup[]>([]);
  const [listParam, setListParam] = useState<AdminGroupListParam>(
    initAdminGroupListParam,
  );
  const [pagination, setPagination] = useState<Pagination>(initPagination);
  const columns: Array<ProColumns<AdminGroup>> = [
    {
      title: '名称',
      dataIndex: 'name',
      width: 200,
    },
    {
      title: '权限',
      dataIndex: 'permissions',
      render: (_, row) => (row.root ? '超级管理员' : row.permissions),
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 100,
      render: (_, row) => (
        <Space>
          <Button type="link" onClick={() => editAdminGroup(row.id)}>
            编辑
          </Button>
          <Popconfirm
            title="确定要删除该权限组么？"
            onConfirm={() => deleteAdminGroup(row.id)}
          >
            <Button type="link">删除</Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  useEffect(() => {
    requestAdminGroupList(listParam);
  }, [listParam]);

  // 获取列表数据
  const requestAdminGroupList = async (param: AdminGroupListParam) => {
    const { items, ...rest } = await fetchAdminGroupList(param);
    setAdminGroupList(items);
    setPagination(rest);
  };

  // 编辑
  const editAdminGroup = (id: number) => {
    history.push(`/admin/group/edit/${id}`);
  };

  // 删除
  const deleteAdminGroup = async (id: number) => {
    await fetchAdminGroupDeleting(id);
    message.success('删除成功!');
    const index = adminGroupList.findIndex((group) => group.id === id);
    adminGroupList.splice(index, 1);
    setAdminGroupList([...adminGroupList]);
  };

  const onPaginationChange = (page: number, pageSize: number) => {
    let index = page;
    if (pageSize !== listParam.limit) {
      index = 1;
    }
    setListParam({
      limit: pageSize,
      offset: pageSize * (index - 1),
    });
  };

  return (
    <ProTable<AdminGroup>
      dataSource={adminGroupList}
      columns={columns}
      defaultSize="small"
      rowKey="id"
      style={{
        tableLayout: 'fixed',
        wordBreak: 'break-all',
        wordWrap: 'break-word',
      }}
      scroll={{
        x: columns
          .filter((col) => col.dataIndex === 'action')
          .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
      }}
      search={false}
      pagination={{
        total: pagination.total,
        pageSize: pagination.limit,
        onChange: onPaginationChange,
      }}
      toolbar={{
        actions: [
          <Button
            key="button"
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => editAdminGroup(0)}
          >
            新增
          </Button>,
        ],
      }}
    />
  );
};

export default List;
