import { SystemLicense, SystemLicenseParam } from '@/models/system/interface';
import { LicenseBatchForm } from './interface';

export const initialLicenseBatchForm: LicenseBatchForm = {
  pid: '',
  count: null,
  used: null,
};

// 将formData转换为param
export const transferFormDataToParam = (
  formData: LicenseBatchForm,
  id?: number,
): SystemLicenseParam => {
  const param: SystemLicenseParam = {
    pid: formData.pid,
    count: formData.count || 1,
  };
  if (formData.used) {
    param.used = formData.used || 0;
  }
  if (id) {
    param.id = id;
    param.remind = +formData.remind;
  }
  return param;
};

// 将接口返回的详情数据转换为formData
export const transferDetailToFormData = (
  detail: SystemLicense,
): LicenseBatchForm => {
  const formData: LicenseBatchForm = {
    pid: detail.pid,
    count: detail.count,
    used: detail.used || 0,
    remind: !!detail.remind,
  };
  return formData;
};
