import {
  fetchSystemLicenseCreation,
  fetchSystemLicenseUpdate,
} from '@/models/system/fetch';
import { SystemLicense } from '@/models/system/interface';
import { useDispatch, useParams } from '@umijs/max';
import { Button, Form, Input, InputNumber, Modal, Switch, message } from 'antd';
import React, { useEffect, useState } from 'react';
import { LicenseBatchForm } from '../interface';
import {
  initialLicenseBatchForm,
  transferDetailToFormData,
  transferFormDataToParam,
} from '../util';

interface Props {
  detail?: SystemLicense;
  onConfirm: () => void;
  onCancel: () => void;
}

const Edit: React.FC<Props> = ({ detail, onConfirm, onCancel }: Props) => {
  const dispatch = useDispatch();
  const param = useParams<{ id: string }>();
  const [form] = Form.useForm<LicenseBatchForm>();
  const [loading, setLoading] = useState(false);
  const layout = {
    labelCol: { span: 3 },
    wrapperCol: { span: 20 },
  };

  const back = () => {
    onCancel();
  };

  // 提交form表单
  const submit = async (formData: LicenseBatchForm) => {
    setLoading(true);
    const _param = transferFormDataToParam(formData, detail?.id);
    let state = '';
    try {
      if (detail && +detail.id) {
        state = '更新';
        await fetchSystemLicenseUpdate(_param);
      } else {
        state = '创建';
        await fetchSystemLicenseCreation(_param);
      }
      message.success(`${state}成功！`);
      onConfirm();
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (detail) {
      form.setFieldsValue(transferDetailToFormData(detail));
    }
  }, [param]);

  useEffect(() => {
    // 额外dva接口请求
  }, [dispatch]);

  return (
    <Modal
      open
      footer={null}
      onCancel={back}
      width={800}
      title={`${detail ? '编辑' : '创建'}声网批次`}
    >
      <Form
        {...layout}
        form={form}
        onFinish={submit}
        initialValues={initialLicenseBatchForm}
      >
        {!detail ? (
          <Form.Item
            name="pid"
            label="批次号"
            rules={[{ required: true, message: '请输入批次号' }]}
          >
            <Input placeholder="请输入批次号" />
          </Form.Item>
        ) : null}
        <Form.Item
          name="count"
          label="批次数量"
          rules={[
            { required: true, message: '请输入批次数量' },
            {
              validator: (_, value) => {
                const used = form.getFieldValue('used');
                if (value < used) {
                  return Promise.reject('批次数量必须大于已使用量');
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <InputNumber min={0.01} placeholder="请输入批次数量" />
        </Form.Item>
        {detail ? (
          <>
            <Form.Item
              name="used"
              label="已使用量"
              rules={[
                {
                  validator: (_, value) => {
                    const count = form.getFieldValue('count');
                    if (value > count) {
                      return Promise.reject('已使用量必须小于批次数量');
                    }
                    return Promise.resolve();
                  },
                },
              ]}
            >
              <InputNumber min={0} placeholder="请输入批次号" />
            </Form.Item>
            <Form.Item name="remind" label="是否提醒" valuePropName="checked">
              <Switch unCheckedChildren="否" checkedChildren="是" />
            </Form.Item>
          </>
        ) : null}
        <Form.Item style={{ textAlign: 'center' }} wrapperCol={{ span: 24 }}>
          <Button
            type="primary"
            htmlType="submit"
            style={{ marginRight: 16 }}
            loading={loading}
            disabled={loading}
          >
            提交
          </Button>
          <Button type="default" onClick={back}>
            取消
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default Edit;
