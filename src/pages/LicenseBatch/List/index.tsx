import { fetchSystemLicenseList } from '@/models/system/fetch';
import {
  SystemLicense,
  SystemLicenseListParam,
} from '@/models/system/interface';
import { initSystemLicenseListParam } from '@/models/system/util';
import { Pagination, initPagination } from '@/utils/request';
import { PlusOutlined } from '@ant-design/icons';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProTable,
} from '@ant-design/pro-components';
import { Button } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import Edit from '../Edit';

const List: React.FC = () => {
  const [dataList, setDataList] = useState<SystemLicense[]>([]);
  const [listParam, setListParam] = useState<SystemLicenseListParam>(
    initSystemLicenseListParam,
  );
  const [pagination, setPagination] = useState<Pagination>(initPagination);
  const [selectedDetail, setSelectedDetail] = useState<SystemLicense>();
  const [showEdit, setShowEdit] = useState(false);

  // 获取列表数据
  const requestLicenseBatchList = async (
    param: SystemLicenseListParam = initSystemLicenseListParam,
  ) => {
    const { items, ...rest } = await fetchSystemLicenseList(param);
    setPagination(rest);
    setDataList(items);
  };

  // 编辑
  const edit = (record?: SystemLicense) => {
    setSelectedDetail(record);
    setShowEdit(true);
  };

  const onEditCancel = () => {
    setShowEdit(false);
  };

  const onEditConfirm = () => {
    onEditCancel();
    setListParam({ ...initSystemLicenseListParam });
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    const formData = form?.getFieldsValue();
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          const _listParam: SystemLicenseListParam = {
            ...initSystemLicenseListParam,
          };
          if (formData.pid) {
            _listParam.pid = formData.pid;
          }
          setListParam(_listParam);
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          form?.resetFields();
          setListParam({
            ...initSystemLicenseListParam,
          });
        }}
      >
        重置
      </Button>,
    ];
  };

  const onPaginationChanged = (page: number, pageSize: number) => {
    let index = page;
    if (pageSize !== listParam.limit) {
      index = 1;
    }
    setListParam({
      ...listParam,
      limit: pageSize,
      offset: (index - 1) * pageSize,
    });
  };

  const columns: Array<ProColumns<SystemLicense>> = [
    {
      title: 'PID',
      dataIndex: 'pid',
      width: 120,
    },
    {
      title: '批次数量',
      dataIndex: 'count',
      width: 100,
      search: false,
    },
    {
      title: '是否提醒',
      dataIndex: 'remind',
      width: 100,
      search: false,
      render: (_, record) => (record.remind ? '是' : '否'),
    },
    {
      title: '使用量',
      dataIndex: 'used',
      width: 100,
      search: false,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 180,
      search: false,
      render: (_, record) =>
        dayjs(record.createdAt * 1000).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '修改时间',
      dataIndex: 'updatedAt',
      width: 180,
      search: false,
      render: (_, record) =>
        dayjs(record.createdAt * 1000).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 100,
      search: false,
      render: (_, record) => (
        <Button type="link" onClick={() => edit(record)}>
          编辑
        </Button>
      ),
    },
  ];

  useEffect(() => {
    requestLicenseBatchList(listParam);
  }, [listParam]);

  return (
    <>
      <ProTable<SystemLicense>
        dataSource={dataList}
        columns={columns}
        defaultSize="small"
        rowKey="id"
        search={{
          defaultCollapsed: false,
          span: 6,
          optionRender: searchOptionRender,
        }}
        scroll={{
          x: columns
            .filter((col) => col.dataIndex !== 'action')
            .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
        }}
        options={{
          reload: () => {
            setListParam({ ...listParam });
          },
        }}
        toolbar={{
          actions: [
            <Button
              key="button"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => edit()}
            >
              新增
            </Button>,
          ],
        }}
        pagination={{
          pageSize: pagination.limit,
          total: pagination.total,
          showQuickJumper: true,
          onChange: onPaginationChanged,
        }}
      />
      {showEdit ? (
        <Edit
          detail={selectedDetail}
          onConfirm={onEditConfirm}
          onCancel={onEditCancel}
        />
      ) : null}
    </>
  );
};

export default List;
