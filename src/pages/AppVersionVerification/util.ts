import { DevicePlatformEnum } from '@/models/common.enum';
import { AppVersionVerificationForm } from './interface';
import { AppVersionVerificationParam } from '@/models/app/interface';

export const initAppVersionVerificationForm: AppVersionVerificationForm = {
  version: '',
  platform: DevicePlatformEnum.iOS,
};

// 将formData转换为param
export const transferFormDataToParam = (
  formData: AppVersionVerificationForm,
  id?: number,
): AppVersionVerificationParam => {
  const param: AppVersionVerificationParam = {
    version: formData.version,
    platform: formData.platform,
  };
  id && (param.id = id);
  return param;
};

// 将接口返回的详情数据转换为formData
// export const transferDetailToFormData = (
//   detail: AppVersionVerificationDetail,
// ): AppVersionVerificationForm => {
//   const formData: AppVersionVerificationForm = {};
//   return formData;
// };
