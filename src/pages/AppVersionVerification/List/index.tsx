import {
  fetchAppVersionDataVerifying,
  fetchAppVersionVerificationDeletion,
  fetchAppVersionVerificationList,
} from '@/models/app/fetch';
import {
  AppVersionStateEnum,
  AppVersionVerification,
  AppVersionVerificationListParam,
} from '@/models/app/interface';
import {
  appVersionStateName,
  initAppVersionVerification,
  initAppVersionVerificationListParam,
} from '@/models/app/util';
import { ApiSuccessEnum } from '@/models/common.interface';
import {
  devicePlatformName,
  spanConfig,
  transferFormDataToParam,
} from '@/models/common.util';
import { Pagination, initPagination } from '@/utils/request';
import { PlusOutlined } from '@ant-design/icons';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProTable,
} from '@ant-design/pro-components';
import { Button, Modal, Popconfirm, Space, message } from 'antd';
import React, { useEffect, useState } from 'react';
import Edit from '../Edit';

const List: React.FC = () => {
  const [dataList, setDataList] = useState<AppVersionVerification[]>([]);
  const [listParam, setListParam] = useState<AppVersionVerificationListParam>(
    initAppVersionVerificationListParam,
  );
  const [pagination, setPagination] = useState<Pagination>(initPagination);
  const [selectedVerification, setSelectedVerification] =
    useState<AppVersionVerification>();
  const columns: Array<ProColumns<AppVersionVerification>> = [
    {
      title: '版本号',
      dataIndex: 'version',
      width: 200,
    },
    {
      title: '应用平台',
      dataIndex: 'platform',
      width: 200,
      render: (_, row) => devicePlatformName[row.platform],
    },
    {
      title: '审核状态',
      dataIndex: 'state',
      width: 200,
      render: (_, row) => appVersionStateName[row.state],
    },
    {
      title: '操作',
      dataIndex: 'action',
      render: (_, row) => {
        const stateSwitchButton: {
          [key in AppVersionStateEnum]: React.ReactNode;
        } = {
          [AppVersionStateEnum.WAIT_VERIFY]: (
            <Popconfirm
              title="确定开始审核么？"
              onConfirm={() =>
                requestSwitchVerificationState(
                  row.id,
                  AppVersionStateEnum.VERIFYING,
                )
              }
            >
              <Button type="link">开始审核</Button>
            </Popconfirm>
          ),
          [AppVersionStateEnum.VERIFYING]: (
            <Popconfirm
              title="确定进行发布么？"
              onConfirm={() =>
                requestSwitchVerificationState(
                  row.id,
                  AppVersionStateEnum.PUBLISHED,
                )
              }
            >
              <Button type="link">发布</Button>
            </Popconfirm>
          ),
          [AppVersionStateEnum.PUBLISHED]: null,
        };

        return (
          <Space split="|">
            <Button type="link" onClick={() => setSelectedVerification(row)}>
              编辑
            </Button>
            <Popconfirm
              title="确定删除么？"
              onConfirm={() => requestDeleteVerificationState(row.id)}
            >
              <Button type="link">删除</Button>
            </Popconfirm>
            {stateSwitchButton[row.state]}
          </Space>
        );
      },
    },
  ];

  useEffect(() => {
    requestAppVersionVerificationList(listParam);
  }, [listParam]);

  // 获取列表数据
  const requestAppVersionVerificationList = async (
    param: AppVersionVerificationListParam = initAppVersionVerificationListParam,
  ) => {
    const { items, ...rest } = await fetchAppVersionVerificationList(param);
    setPagination(rest);
    setDataList(items);
  };

  // 切换审核状态
  const requestSwitchVerificationState = async (
    id: number,
    state: AppVersionStateEnum,
  ) => {
    let fetchFunction;
    let action = '';
    if (state === AppVersionStateEnum.VERIFYING) {
      fetchFunction = fetchAppVersionDataVerifying;
      action = '提审';
    } else if (state === AppVersionStateEnum.PUBLISHED) {
      fetchFunction = fetchAppVersionDataVerifying;
      action = '发布';
    }

    if (!fetchFunction) return;

    const result = await fetchFunction(id, state);

    if (result === ApiSuccessEnum.success) {
      message.success(`${action}成功!`);
      setListParam({ ...listParam });
    }
  };

  // 删除审核数据
  const requestDeleteVerificationState = async (id: number) => {
    const result = await fetchAppVersionVerificationDeletion(id);
    if (result === ApiSuccessEnum.success) {
      message.success('删除成功！');
      setListParam({ ...listParam });
    }
  };

  // 编辑
  const editAppVersionVerification = (detail: AppVersionVerification) => {
    setSelectedVerification(detail);
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          const _param = transferFormDataToParam<
            any,
            AppVersionVerificationListParam
          >(form?.getFieldsValue());
          console.log(form?.getFieldsValue(), _param);
          // setListParam(_param);
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          form?.resetFields();
          setListParam(initAppVersionVerificationListParam);
        }}
      >
        重置
      </Button>,
    ];
  };

  const onPaginationChanged = (page: number, pageSize: number) => {
    let index = page;
    if (pageSize !== listParam.limit) {
      index = 1;
    }
    setListParam({
      ...listParam,
      limit: pageSize,
      offset: (index - 1) * pageSize,
    });
  };

  const onCloseModal = (refreshList?: boolean) => {
    setSelectedVerification(undefined);

    refreshList && setListParam({ ...listParam });
  };

  return (
    <>
      <ProTable<AppVersionVerification>
        dataSource={dataList}
        columns={columns}
        defaultSize="small"
        rowKey="id"
        search={{
          defaultCollapsed: false,
          span: spanConfig,
          optionRender: searchOptionRender,
        }}
        scroll={{
          x: columns
            .filter((col) => col.dataIndex !== 'action')
            .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
        }}
        toolbar={{
          actions: [
            <Button
              key="button"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() =>
                editAppVersionVerification(initAppVersionVerification)
              }
            >
              新增
            </Button>,
          ],
        }}
        pagination={{
          pageSize: pagination.limit,
          total: pagination.total,
          showQuickJumper: true,
          onChange: onPaginationChanged,
        }}
      />
      {selectedVerification ? (
        <Modal
          open
          footer={null}
          width={800}
          destroyOnClose
          onCancel={() => onCloseModal()}
        >
          <Edit detail={selectedVerification} onClose={onCloseModal} />
        </Modal>
      ) : (
        ''
      )}
    </>
  );
};

export default List;
