import { fetchAppVersionVerificationSaving } from '@/models/app/fetch';
import { AppVersionVerification } from '@/models/app/interface';
import { DevicePlatformEnum } from '@/models/common.enum';
import { ApiSuccessEnum } from '@/models/common.interface';
import { devicePlatformName } from '@/models/common.util';
import { ProCard } from '@ant-design/pro-components';
import { Button, Form, Input, Radio, message } from 'antd';
import React, { useEffect, useState } from 'react';
import { AppVersionVerificationForm } from '../interface';
import {
  initAppVersionVerificationForm,
  transferFormDataToParam,
} from '../util';

interface Props {
  detail: AppVersionVerification;
  onClose?: (refreshList?: boolean) => void;
}

const Edit: React.FC<Props> = ({ detail, onClose }: Props) => {
  const [form] = Form.useForm<AppVersionVerificationForm>();
  const [loading, setLoading] = useState(false);
  const layout = {
    labelCol: { span: 3 },
    wrapperCol: { span: 20 },
  };

  useEffect(() => {
    if (!detail || !form) return;

    form.setFieldsValue({
      version: detail.version,
      platform: detail.platform,
    });
  }, [detail, form]);

  // 提交form表单
  const submit = async (formData: AppVersionVerificationForm) => {
    setLoading(true);
    const _param = transferFormDataToParam(formData, detail.id || 0);
    let result = '';
    let state = '';
    try {
      if (detail && detail.id) {
        state = '更新';
      } else {
        state = '创建';
      }
      result = await fetchAppVersionVerificationSaving(_param);

      if (result === ApiSuccessEnum.success) {
        message.success(`${state}成功！`);
        onClose && onClose(true);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <ProCard>
      <Form
        {...layout}
        form={form}
        onFinish={submit}
        initialValues={initAppVersionVerificationForm}
      >
        <Form.Item
          name="version"
          label="版本号"
          rules={[{ required: true, message: '请输入版本号' }]}
        >
          <Input placeholder="请输入版本号" />
        </Form.Item>
        <Form.Item name="platform" label="应用平台">
          <Radio.Group>
            <Radio.Button
              key={DevicePlatformEnum.iOS}
              value={DevicePlatformEnum.iOS}
            >
              {devicePlatformName[DevicePlatformEnum.iOS]}
            </Radio.Button>
            <Radio.Button
              key={DevicePlatformEnum.Android}
              value={DevicePlatformEnum.Android}
            >
              {devicePlatformName[DevicePlatformEnum.Android]}
            </Radio.Button>
          </Radio.Group>
        </Form.Item>
        <Form.Item style={{ textAlign: 'right' }}>
          <Button
            type="primary"
            htmlType="submit"
            style={{ marginRight: 16, width: '100%' }}
            loading={loading}
            disabled={loading}
          >
            提交
          </Button>
          <Button type="default" onClick={() => onClose && onClose()}>
            取消
          </Button>
        </Form.Item>
      </Form>
    </ProCard>
  );
};

export default Edit;
