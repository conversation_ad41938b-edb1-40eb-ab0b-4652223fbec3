import { ApiSuccessEnum, StatusEnum } from '@/models/common.interface';
import { getStatusMap, postMessageFunction } from '@/models/common.util';
import {
  fetchSystemTipInfoDeletion,
  fetchSystemTipInfoEnable,
  fetchSystemTipInfoList,
  getSystemTipInfoExportUrl,
} from '@/models/system/fetch';
import {
  SystemTipInfo,
  SystemTipInfoEnableParam,
  SystemTipInfoListParam,
  TipInfoExportTypeEnum,
} from '@/models/system/interface';
import { Pagination, initPagination } from '@/utils/request';
import { PlusOutlined } from '@ant-design/icons';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProTable,
} from '@ant-design/pro-components';
import { history, useDispatch, useSearchParams } from '@umijs/max';
import {
  Button,
  FormInstance,
  Modal,
  Popconfirm,
  Space,
  Switch,
  message,
} from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { TipInfoTableForm } from '../interface';
import { transferTableFormToListParam } from '../util';

const List: React.FC = () => {
  const dispatch = useDispatch();
  const tableFormRef = useRef<FormInstance<TipInfoTableForm>>();
  const [urlParam] = useSearchParams();
  const [dataList, setDataList] = useState<SystemTipInfo[]>([]);
  const [listParam, setListParam] = useState<SystemTipInfoListParam>();
  const [pagination, setPagination] = useState<Pagination>(initPagination);

  // 获取列表数据
  const requestTipInfoList = async (param: SystemTipInfoListParam) => {
    const { items, ...rest } = await fetchSystemTipInfoList(param);
    setPagination(rest);
    setDataList(items);
  };

  // 切换状态
  const switchStatus = async (ev: boolean, id: number) => {
    try {
      const param: SystemTipInfoEnableParam = {
        id,
        doc: JSON.stringify({ enable: +ev }),
      };
      const result = await fetchSystemTipInfoEnable(param);
      if (result === ApiSuccessEnum.success) {
        message.success(`${ev ? '启用' : '禁用'}成功!`);
        if (!listParam) return;
        setListParam({ ...listParam });
        // const index = [...dataList].findIndex((item) => item.id === id);
        // dataList[index].enable = +ev as StatusEnum;
        // setDataList([...dataList]);
      }
    } catch (error) {
      console.log(error);
    }
  };

  // 编辑
  const editTipInfo = (id: number) => {
    history.push(`/system/tip-info/edit/${id}`);
  };

  // 删除
  const deleteTipInfo = async (id: number) => {
    try {
      const result = await fetchSystemTipInfoDeletion(id);
      if (result === ApiSuccessEnum.success) {
        message.success('删除成功!');
        if (!listParam) return;
        setListParam({ ...listParam });
      }
    } catch (error) {
      console.log(error);
    }
  };

  // 导出
  const exportTipsInfoWithType = (type: TipInfoExportTypeEnum) => {
    const url = getSystemTipInfoExportUrl({ bundle: type });
    window.open(url);
  };

  // 导入
  const gotoImportPage = (type: TipInfoExportTypeEnum) => {
    const url = `/system/tips_localebundle_import?bundle=${type}`;
    Modal.confirm({
      content: '将导入到提示表？',
      onOk: () => {
        postMessageFunction({
          type: 'redirect',
          content: {
            redirectUrl: url,
          },
        });
      },
    });
  };

  // 处理url参数
  const getUrlParamByUrlParams = (_urlParams: URLSearchParams) => {
    const keyId = _urlParams.get('keyId');
    const enable = _urlParams.get('enable');
    const formData: TipInfoTableForm = {
      // keyId: string;
      // enable: StatusEnum;
    };
    if (keyId) formData.keyId = keyId;
    if (enable) formData.enable = +enable;

    return formData;
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          const formData: TipInfoTableForm = form?.getFieldsValue();

          const _param: Pick<TipInfoTableForm, 'keyId' | 'enable'> = {};
          if (formData.keyId) _param.keyId = formData.keyId;
          if (formData.enable !== undefined && formData.enable !== null)
            _param.enable = formData.enable;

          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: '/system/tip_info_list',
              param: _param as Record<string, string>,
            },
          });
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: '/system/tip_info_list',
            },
          });
        }}
      >
        重置
      </Button>,
    ];
  };

  const onPaginationChanged = (page: number, pageSize: number) => {
    let index = page;
    if (!listParam || pageSize !== listParam.limit) {
      index = 1;
    }
    setListParam({
      ...listParam,
      limit: pageSize,
      offset: (index - 1) * pageSize,
    });
  };

  const columns: Array<ProColumns<SystemTipInfo>> = [
    { title: 'id', dataIndex: 'id', width: 80, search: false },
    { title: 'key', dataIndex: 'keyId', width: 120 },
    { title: '描述', dataIndex: 'annotation', width: 150, search: false },
    {
      title: '是否启用',
      dataIndex: 'enable',
      valueEnum: getStatusMap(),
      valueType: 'select',
      initialValue: StatusEnum.ENABLE,
      width: 100,
      render: (_, record) => (
        <Switch
          checked={!!record.enable}
          checkedChildren="启用"
          unCheckedChildren="禁用"
          onChange={(ev) => switchStatus(ev, record.id)}
        />
      ),
    },
    {
      title: '操作',
      dataIndex: 'action',
      search: false,
      width: 80,
      render: (_, record) => (
        <Space>
          <Button type="primary" onClick={() => editTipInfo(record.id)}>
            编辑
          </Button>
          <Popconfirm
            title="确定要删除该提示信息么？"
            onConfirm={() => deleteTipInfo(record.id)}
          >
            <Button type="primary" danger>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  useEffect(() => {
    dispatch({ type: 'app/requestLocaleList' });
  }, []);

  useEffect(() => {
    const formData = getUrlParamByUrlParams(urlParam);
    const tableForm = tableFormRef.current;
    tableForm?.setFieldsValue(formData);
    setListParam(transferTableFormToListParam(formData));
  }, [urlParam]);

  useEffect(() => {
    if (!listParam) return;
    requestTipInfoList(listParam);
  }, [listParam]);

  return (
    <ProTable<SystemTipInfo>
      dataSource={dataList}
      columns={columns}
      formRef={tableFormRef}
      defaultSize="small"
      rowKey="id"
      options={{
        reload: () => listParam && setListParam({ ...listParam }),
      }}
      search={{
        defaultCollapsed: false,
        span: 6,
        optionRender: searchOptionRender,
      }}
      scroll={{
        x: columns
          .filter((col) => col.dataIndex !== 'action')
          .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
      }}
      toolbar={{
        actions: [
          <Button
            key="button"
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => editTipInfo(0)}
          >
            新增
          </Button>,
          <Button
            key="button"
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => exportTipsInfoWithType(TipInfoExportTypeEnum.TIPS)}
            style={{ backgroundColor: '#5bc0de' }}
          >
            导出描述
          </Button>,
          <Button
            key="button"
            type="primary"
            icon={<PlusOutlined />}
            style={{ backgroundColor: '#5bc0de' }}
            onClick={() =>
              exportTipsInfoWithType(TipInfoExportTypeEnum.TIPSURL)
            }
          >
            导出图片
          </Button>,
          <Button
            key="button"
            type="primary"
            icon={<PlusOutlined />}
            style={{ backgroundColor: '#f0ad4e' }}
            onClick={() => gotoImportPage(TipInfoExportTypeEnum.TIPS)}
          >
            导入描述
          </Button>,
          <Button
            key="button"
            type="primary"
            icon={<PlusOutlined />}
            style={{ backgroundColor: '#f0ad4e' }}
            onClick={() => gotoImportPage(TipInfoExportTypeEnum.TIPSURL)}
          >
            导入图片
          </Button>,
        ],
      }}
      pagination={{
        pageSize: pagination.limit,
        total: pagination.total,
        showQuickJumper: true,
        onChange: onPaginationChanged,
      }}
    />
  );
};

export default List;
