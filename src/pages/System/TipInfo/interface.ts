import { LocaleContentForm } from '@/components/LocaleContentModal/interface';
import { StatusEnum } from '@/models/common.interface';
import { TipMediaTypeEnum } from '@/models/system/interface';
import { UploadFile } from 'antd';

export interface TipInfoTableForm {
  keyId?: string;
  enable?: StatusEnum;
}

export interface TipInfoLocaleContentForm extends LocaleContentForm {
  description: string;
  files: UploadFile[];
}

export interface TipInfoForm {
  keyId: string;
  description: string;
  status: boolean;
  mediaType: TipMediaTypeEnum;
}
