// import {
//   TipInfoDetail,
//   TipInfoParam,
// } from '@/models/tipInfo/interface';
// import { TipInfoForm, TipInfoLocaleContentForm } from './interface';
import { UploadFile } from 'antd';

import { initLocaleContentForm } from '@/components/LocaleContentModal/util';
import { LocaleObject } from '@/models/common.interface';
import {
  SystemTipInfoDetail,
  SystemTipInfoListParam,
  SystemTipInfoParam,
  TipMediaTypeEnum,
} from '@/models/system/interface';
import { initSystemTipInfoListParam } from '@/models/system/util';
import { uuid } from '@/utils/uuid';
import {
  TipInfoForm,
  TipInfoLocaleContentForm,
  TipInfoTableForm,
} from './interface';

export const transferTableFormToListParam = (
  formData: TipInfoTableForm,
): SystemTipInfoListParam => {
  const listParam: SystemTipInfoListParam = {
    ...initSystemTipInfoListParam,
    keyId: formData.keyId,
    enable: formData.enable,
  };
  return listParam;
};

// 将formData转换为param
export const transferFormDataToParam = (
  formData: TipInfoForm,
  localeContentList: TipInfoLocaleContentForm[],
  id?: number,
): SystemTipInfoParam => {
  const param: SystemTipInfoParam = {
    keyId: formData.keyId,
    annotation: formData.description,
    enable: +formData.status,
    url: '',
    tipDesc: '',
    mediaType: formData.mediaType,
  };
  const url: LocaleObject = {};
  const description: LocaleObject = {};

  localeContentList.forEach((localeContent) => {
    url[localeContent.language] = localeContent.files
      .map((file) => file.url)
      .join();
    description[localeContent.language] = localeContent.description;
  });

  param.url = JSON.stringify(url);
  param.tipDesc = JSON.stringify(description);

  if (id) param.id = id;
  return param;
};

// 将接口返回的详情数据转换为formData
export const transferDetailToFormData = (
  detail: SystemTipInfoDetail,
): TipInfoForm => {
  const formData: TipInfoForm = {
    keyId: detail.keyId,
    description: detail.annotation,
    status: !!detail.enable,
    mediaType: TipMediaTypeEnum.IMAGE,
  };

  switch (detail.mediaType) {
    case TipMediaTypeEnum.VIDEO:
      formData.mediaType = TipMediaTypeEnum.VIDEO;
      break;
    default:
      formData.mediaType = TipMediaTypeEnum.IMAGE;
      break;
  }

  return formData;
};

export const initialTipInfoForm: TipInfoForm = {
  keyId: '',
  description: '',
  status: true,
  mediaType: TipMediaTypeEnum.IMAGE,
};

export const initialTipInfoLocaleContentForm: TipInfoLocaleContentForm = {
  ...initLocaleContentForm,
  description: '',
  files: [],
};

export const transferLocaleContentToFormData = (
  detail: SystemTipInfoDetail,
): TipInfoLocaleContentForm[] => {
  const formDataList: TipInfoLocaleContentForm[] = [];

  const tipDesc = detail.localizedProps.tipDesc;
  const url = detail.localizedProps.url;

  // 处理tipDesc
  for (const language in tipDesc) {
    if (tipDesc[language]) {
      const index = formDataList.findIndex(
        (item) => item.language === language,
      );
      const formData = formDataList[index] || {
        ...initialTipInfoLocaleContentForm,
        language,
      };
      formData.description = tipDesc[language];
      if (index === -1) {
        formDataList.push(formData);
      }
    }
  }

  // 处理url
  for (const language in url) {
    if (url[language]) {
      const index = formDataList.findIndex(
        (item) => item.language === language,
      );
      const formData = formDataList[index] || {
        ...initialTipInfoLocaleContentForm,
        language,
      };
      formData.files = url[language].split(',').map(
        (item) =>
          ({
            url: item.trim(),
            name: item.trim(),
            uid: uuid(),
          } as UploadFile),
      );

      if (index === -1) {
        formDataList.push(formData);
      }
    }
  }

  return formDataList;
};
