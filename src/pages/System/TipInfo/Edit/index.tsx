import LocaleContentModal from '@/components/LocaleContentModal';
import Uploader from '@/components/Uploader';
import { ApiSuccessEnum, SelectOption } from '@/models/common.interface';
import { ConnectState } from '@/models/connect';
import {
  fetchSystemTipInfoDetail,
  fetchSystemTipInfoEdition,
} from '@/models/system/fetch';
import {
  SystemTipInfoDetail,
  TipMediaTypeEnum,
} from '@/models/system/interface';
import { UploadTokenTypeEnum } from '@/services/qiniuOss/interface';
import normFile from '@/utils/normFile';
import { PlusOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { history, useDispatch, useParams, useSelector } from '@umijs/max';
import {
  Button,
  Form,
  Image,
  Input,
  List,
  Select,
  Switch,
  Typography,
  UploadFile,
  message,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { TipInfoForm, TipInfoLocaleContentForm } from '../interface';
import {
  initialTipInfoForm,
  initialTipInfoLocaleContentForm,
  transferDetailToFormData,
  transferFormDataToParam,
  transferLocaleContentToFormData,
} from '../util';

const Edit: React.FC = () => {
  const dispatch = useDispatch();
  const param = useParams<{ id: string }>();
  const [form] = Form.useForm<TipInfoForm>();
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [detail, setDetail] = useState<SystemTipInfoDetail>();
  const [selectedLocaleContent, setSelectedLocaleContent] =
    useState<TipInfoLocaleContentForm>();
  const [localeContentList, setLocaleContentList] = useState<
    TipInfoLocaleContentForm[]
  >([]);
  const [mediaInfoCache, setMediaInfoCache] = useState<{
    [key in TipMediaTypeEnum]?: TipInfoLocaleContentForm[];
  }>({});

  const mediaType: TipMediaTypeEnum = Form.useWatch('mediaType', form); // 多媒体类型

  const tipMediaTypeOptions: SelectOption<TipMediaTypeEnum>[] = [
    { label: '图片', value: TipMediaTypeEnum.IMAGE },
    { label: '视频', value: TipMediaTypeEnum.VIDEO },
  ];

  const localeOptions: SelectOption[] = useSelector(
    ({ app }: ConnectState) => app.localeOptions,
  );

  const layout = {
    labelCol: { span: 3 },
    wrapperCol: { span: 20 },
  };

  const hideLocaleContentModal = () => {
    setVisible(false);
    setSelectedLocaleContent(undefined);
  };

  // 编辑本地化内容
  const editLocaleContent = (localeContent?: TipInfoLocaleContentForm) => {
    setSelectedLocaleContent(localeContent);
    setVisible(true);
  };

  // 删除本地化内容
  const deleteLocaleContent = (index: number) => {
    localeContentList.splice(index, 1);
    setLocaleContentList([...localeContentList]);
  };

  // 保存本地化内容
  const setLocaleContent = (formValue: TipInfoLocaleContentForm) => {
    const existedLocaleContent: TipInfoLocaleContentForm | undefined =
      localeContentList.find((item) => item.language === formValue.language);
    if (existedLocaleContent) {
      existedLocaleContent.description = formValue.description;
      existedLocaleContent.files = formValue.files;
      setLocaleContentList([...localeContentList]);
    } else {
      setLocaleContentList([...localeContentList, formValue]);
    }
  };

  // 根据id获取详情数据
  const requestDetailById = async (id: number) => {
    const detail = await fetchSystemTipInfoDetail(id);
    const formData = transferDetailToFormData(detail);
    form.setFieldsValue(formData);
    const localeContentList = transferLocaleContentToFormData(detail);
    // console.log(localeContentList);
    setLocaleContentList(localeContentList);
    setDetail(detail);
  };

  // 提交form表单
  const submit = async (formData: TipInfoForm) => {
    setLoading(true);
    const _param = transferFormDataToParam(
      formData,
      localeContentList,
      +(param?.id || 0),
    );
    console.log(formData, _param);
    let result = '';
    let state = '';
    try {
      if (param && +(param.id || 0)) {
        state = '更新';
      } else {
        state = '创建';
      }
      result = await fetchSystemTipInfoEdition(_param);
      if (result === ApiSuccessEnum.success) {
        message.success(`${state}成功！`);
        history.back();
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const renderMediaNode = (file: UploadFile<string>, index: number) => {
    // console.log(file);
    if (!file) return;
    if (
      file.type?.startsWith('video') ||
      mediaType === TipMediaTypeEnum.VIDEO
    ) {
      return (
        <video
          key={String(index)}
          autoPlay={false}
          style={{ width: 500 }}
          controls
          src={file.url}
        />
      );
    } else {
      return (
        <Image key={String(index)} alt={file.name} width={150} src={file.url} />
      );
    }
  };

  useEffect(() => {
    if (param && +(param.id || 0)) {
      requestDetailById(+(param.id || 0));
    }
  }, [param]);

  useEffect(() => {
    // 额外dva接口请求
  }, [dispatch]);

  useEffect(() => {
    if (detail && detail.mediaType === TipMediaTypeEnum.OLD) {
      return;
    }
    // console.log('mediaType onChange', mediaType);
    if (detail && detail.mediaType !== mediaType) {
      setLocaleContentList([]);
      return;
    }
    if (!detail) {
      // console.log(mediaType);
      mediaInfoCache[+!mediaType as TipMediaTypeEnum] = localeContentList;
      // console.log(mediaInfoCache);
      setMediaInfoCache({ ...mediaInfoCache });

      setLocaleContentList(mediaInfoCache[mediaType] || []);
    }
  }, [mediaType]);

  return (
    <>
      <ProCard>
        <Form
          {...layout}
          form={form}
          onFinish={submit}
          initialValues={initialTipInfoForm}
        >
          <Form.Item
            name="keyId"
            label="设备Key"
            rules={[{ required: true, message: '请输入设备Key' }]}
          >
            <Input placeholder="请输入设备Key" />
          </Form.Item>
          <Form.Item
            name="description"
            label="描述"
            rules={[{ required: true, message: '请输入描述' }]}
          >
            <Input placeholder="请输入描述" />
          </Form.Item>
          <Form.Item label="是否启用" name="status" valuePropName="checked">
            <Switch checkedChildren="是" unCheckedChildren="否" />
          </Form.Item>
          <Form.Item label="多媒体类型" name="mediaType">
            <Select options={tipMediaTypeOptions} disabled={!!detail} />
          </Form.Item>
          <Form.Item wrapperCol={{ span: 20, offset: 2 }}>
            <Button icon={<PlusOutlined />} onClick={() => editLocaleContent()}>
              本地化内容
            </Button>
          </Form.Item>
          <Form.Item wrapperCol={{ span: 20, offset: 2 }}>
            <List<TipInfoLocaleContentForm>
              size="small"
              bordered={false}
              dataSource={localeContentList}
              renderItem={(item, index) => (
                <List.Item>
                  <div>
                    <div>
                      <Typography.Text strong>语言：</Typography.Text>
                      {localeOptions.find((opt) => opt.value === item.language)
                        ?.label || item.language}
                      <Button
                        type="link"
                        onClick={() => editLocaleContent(item)}
                      >
                        编辑
                      </Button>{' '}
                      <Button
                        type="link"
                        onClick={() => deleteLocaleContent(index)}
                      >
                        删除
                      </Button>
                    </div>
                    <div>
                      <Typography.Text strong>描述：</Typography.Text>
                      {item.description}
                    </div>
                    <div>
                      <Typography.Text strong>
                        {mediaType === TipMediaTypeEnum.VIDEO ? '视频' : '图片'}
                        ：
                      </Typography.Text>
                      <Image.PreviewGroup
                        preview={{
                          onChange: (current, prev) =>
                            console.log(
                              `current index: ${current}, prev index: ${prev}`,
                            ),
                        }}
                      >
                        {item.files.map((file, index) =>
                          renderMediaNode(file, index),
                        )}
                      </Image.PreviewGroup>
                    </div>
                  </div>
                </List.Item>
              )}
            />
          </Form.Item>
          <Form.Item wrapperCol={{ span: 24 }} style={{ textAlign: 'center' }}>
            <Button
              type="primary"
              htmlType="submit"
              style={{ marginRight: 16 }}
              loading={loading}
              disabled={loading}
            >
              保存
            </Button>
            <Button type="default" onClick={history.back}>
              取消
            </Button>
          </Form.Item>
        </Form>
      </ProCard>
      <LocaleContentModal<TipInfoLocaleContentForm>
        visible={visible}
        localeOptionList={localeOptions}
        initialFormValues={initialTipInfoLocaleContentForm}
        detail={selectedLocaleContent}
        onCancel={hideLocaleContentModal}
        onConfirm={(fd) => {
          setLocaleContent(fd);
          hideLocaleContentModal();
        }}
      >
        <Form.Item
          colon
          name="description"
          label="描述"
          rules={[{ required: true, message: '请输入描述内容' }]}
          extra="描述内容，多段描述以,分隔"
        >
          <Input />
        </Form.Item>
        <Form.Item
          label={mediaType === TipMediaTypeEnum.VIDEO ? '视频' : '图片'}
          name="files"
          getValueFromEvent={normFile}
          valuePropName="fileList"
          shouldUpdate
        >
          <Uploader
            accept={
              mediaType === TipMediaTypeEnum.VIDEO ? 'video/*' : 'image/*'
            }
            type={
              mediaType === TipMediaTypeEnum.VIDEO
                ? UploadTokenTypeEnum.VIDEO
                : UploadTokenTypeEnum.IMAGE
            }
            maxCount={10}
            uploadButtonText={`上传${
              mediaType === TipMediaTypeEnum.VIDEO ? '视频' : '图片'
            }`}
          />
        </Form.Item>
      </LocaleContentModal>
    </>
  );
};

export default Edit;
