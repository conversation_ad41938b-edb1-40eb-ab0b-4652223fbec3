import { StatusEnum } from '@/models/common.interface';
import {
  fetchVirtualDeviceList,
  fetchVirtualDeviceShelfState,
} from '@/models/virtualDevice/fetch';
import {
  VirtualDevice,
  VirtualDeviceListParam,
  VirtualDeviceShelfStateParam,
} from '@/models/virtualDevice/interface';
import useUrlState from '@ahooksjs/use-url-state';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { Col, message, Row, Switch } from 'antd';
import React, { useEffect, useState } from 'react';
import CategoryList from '../CategoryList';
import styles from '../index.less';

interface UrlParam {
  categoryId: string;
}

const List: React.FC = () => {
  const [urlParam] = useUrlState<UrlParam>();
  const [dataList, setDataList] = useState<VirtualDevice[]>([]);
  const [listParam, setListParam] = useState<VirtualDeviceListParam>();
  const [messageApi, contextHolder] = message.useMessage();

  // 获取列表数据
  const requestVirtualDeviceConfigList = async (
    param: VirtualDeviceListParam,
  ) => {
    const list = await fetchVirtualDeviceList(param);
    setDataList(list);
  };

  const onSwitchChanged = async (ev: boolean, record: VirtualDevice) => {
    const param: VirtualDeviceShelfStateParam = {
      typeId: record.typeId,
      deviceType: record.deviceType,
      shelfState: +ev as StatusEnum,
    };
    await fetchVirtualDeviceShelfState(param);
    messageApi.success(`${ev ? '上架' : '下架'}成功`);

    if (!listParam || !listParam.typeId) return;
    setListParam({ ...listParam });
  };

  const columns: Array<ProColumns<VirtualDevice>> = [
    {
      title: '设备型号',
      dataIndex: 'deviceType',
    },
    {
      title: '状态',
      render: (_, record) => (
        <Switch
          unCheckedChildren="下架"
          checkedChildren="上架"
          checked={!!record.shelfState}
          onChange={(ev) => onSwitchChanged(ev, record)}
        />
      ),
    },
  ];

  useEffect(() => {
    if (!urlParam || !urlParam.categoryId) return;
    setListParam({
      typeId: urlParam.categoryId,
    });
  }, [urlParam]);

  useEffect(() => {
    if (!listParam || !listParam.typeId) return;
    requestVirtualDeviceConfigList(listParam);
  }, [listParam]);

  return (
    <>
      {contextHolder}
      <Row justify="space-between" className={styles.content}>
        <Col span={5}>
          <CategoryList />
        </Col>
        <Col span={18}>
          <ProTable<VirtualDevice>
            dataSource={dataList}
            columns={columns}
            defaultSize="small"
            rowKey="deviceType"
            search={false}
            scroll={{
              x: columns
                .filter((col) => col.dataIndex !== 'action')
                .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
            }}
            options={{
              reload: () => {
                if (listParam) {
                  setListParam({ ...listParam });
                }
              },
            }}
            pagination={false}
          />
        </Col>
      </Row>
    </>
  );
};

export default List;
