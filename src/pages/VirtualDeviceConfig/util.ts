
import {
  VirtualDeviceConfigDetail,
  VirtualDeviceConfigParam,
} from '@/models/virtualDeviceConfig/interface';
import { VirtualDeviceConfigForm } from './interface';

export const initialVirtualDeviceConfigForm: VirtualDeviceConfigForm = {
};
  
// 将formData转换为param
export const transferFormDataToParam = (formData: VirtualDeviceConfigForm, id?: number): VirtualDeviceConfigParam => {
  const param: VirtualDeviceConfigParam = {
  };
  id && (param.id = id);
  return param;
};
  
// 将接口返回的详情数据转换为formData
export const transferDetailToFormData = (detail: VirtualDeviceConfigDetail): VirtualDeviceConfigForm => {
  const formData: VirtualDeviceConfigForm = {
  };
  return formData;
};
