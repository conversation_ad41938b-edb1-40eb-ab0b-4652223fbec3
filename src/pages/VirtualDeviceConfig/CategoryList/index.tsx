import { SelectOption } from '@/models/common.interface';
import { fetchVirtualDeviceCategoryList } from '@/models/virtualDevice/fetch';
import { ProCard } from '@ant-design/pro-components';
import { history } from '@umijs/max';
import React, { useEffect, useState } from 'react';
import styles from '../index.less';

const CategoryList: React.FC = () => {
  const [selectedTypeId, setSelectedTypeId] = useState<number>(0);
  const [categoryList, setCategoryList] = useState<SelectOption[]>([]);

  const selectCategory = (id: number) => {
    setSelectedTypeId(id);
  };

  const requestCategoryList = async () => {
    const result = await fetchVirtualDeviceCategoryList();
    setCategoryList(
      result.devices.map((device) => ({
        label: device.typeName,
        value: device.typeId,
      })),
    );
    if (result.devices && result.devices.length > 0) {
      setSelectedTypeId(result.devices[0].typeId);
    }
  };

  useEffect(() => {
    requestCategoryList();
  }, []);

  useEffect(() => {
    if (!selectedTypeId) return;
    history.push(`/virtual-device-config/list?categoryId=${selectedTypeId}`);
  }, [selectedTypeId]);

  return (
    <ProCard className={styles.categoryContainer}>
      {categoryList.map((item) => (
        <div
          key={item.value}
          className={`${styles.category} ${
            selectedTypeId === item.value ? styles.categoryActive : ''
          }`}
          onClick={() => selectCategory(item.value as number)}
        >
          {item.label}
        </div>
      ))}
    </ProCard>
  );
};

export default CategoryList;
