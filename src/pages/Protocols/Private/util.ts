import { DeviceTypeEnum } from '@/models/common.interface';
import { PrivacyDetail, PrivacyParam } from '@/models/privacy/interface';
import dayjs from 'dayjs';
import { PrivateForm } from './interface';

export const initialPrivateForm: PrivateForm = {
  deviceTypeList: [
    DeviceTypeEnum.D4sh,
    DeviceTypeEnum.D4H,
    DeviceTypeEnum.T5,
    DeviceTypeEnum.T6,
  ],
  protocolTime: null,
  description: {
    zh_CN: '',
    en_US: '',
  },
};

// 将formData转换为param
export const transferFormDataToParam = (
  formData: PrivateForm,
  id?: number,
): PrivacyParam => {
  const param: PrivacyParam = {
    type: '*',
    publishStart: (formData.protocolTime || dayjs()).startOf('d').valueOf(),
    context: JSON.stringify(formData.description),
  };
  if (id) {
    param.id = id;
  }
  return param;
};

// 将接口返回的详情数据转换为formData
export const transferDetailToFormData = (
  detail: PrivacyDetail,
): PrivateForm => {
  try {
    const descriptionInfo = JSON.parse(detail.context || '{}');
    const formData: PrivateForm = {
      deviceTypeList: [
        DeviceTypeEnum.D4sh,
        DeviceTypeEnum.D4H,
        DeviceTypeEnum.T5,
        DeviceTypeEnum.T6,
      ],
      protocolTime: dayjs(detail.publishStart),
      description: {
        zh_CN: descriptionInfo.zh_CN || '',
        en_US: descriptionInfo.en_US || '',
      },
    };
    return formData;
  } catch (error) {
    console.log(error);
    return initialPrivateForm;
  }
};
