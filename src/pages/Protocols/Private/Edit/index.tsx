import WangEditor from '@/components/WangEditor';
import {
  ApiSuccessEnum,
  DeviceTypeEnum,
  SelectOption,
} from '@/models/common.interface';
import {
  fetchPrivacyCreation,
  fetchPrivacyDetail,
  fetchPrivacyUpdate,
} from '@/models/privacy/fetch';
import { ProCard } from '@ant-design/pro-components';
import { history, useDispatch, useParams } from '@umijs/max';
import { Button, DatePicker, Form, Modal, Radio, Select, message } from 'antd';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { PrivateForm } from '../interface';
import {
  initialPrivateForm,
  transferDetailToFormData,
  transferFormDataToParam,
} from '../util';

const Edit: React.FC = () => {
  const dispatch = useDispatch();
  const param = useParams<{ id: string }>();
  const [form] = Form.useForm<PrivateForm>();
  const [loading, setLoading] = useState(false);
  const layout = {
    labelCol: { span: 3 },
    wrapperCol: { span: 20 },
  };
  const avaiableDeviceTypes = [
    DeviceTypeEnum.D4sh,
    DeviceTypeEnum.D4H,
    DeviceTypeEnum.T5,
    DeviceTypeEnum.T6,
  ];
  const deviceTypeOptions = Object.values(DeviceTypeEnum)
    .filter(
      (item) => typeof item === 'number' && avaiableDeviceTypes.includes(item),
    )
    .map((item) => ({
      label: DeviceTypeEnum[item as keyof typeof DeviceTypeEnum],
      value: item,
    }));
  const previewTypeOptions: SelectOption[] = [
    { value: 'zh_CN', label: '中文' },
    { value: 'en_US', label: '英文' },
  ];
  const [previewTypeOption, setPreviewTypeOption] =
    useState<keyof PrivateForm['description']>('zh_CN');
  const [showPreivew, setShowPreivew] = useState(false);
  const [descriptionInfo, setDescriptionInfo] =
    useState<PrivateForm['description']>();
  const scrollRef = useRef<HTMLDivElement>(null);

  const isEditing = useMemo(() => !!param.id, [param]);

  // 根据id获取详情数据
  const requestDetailById = async (id: number) => {
    const detail = await fetchPrivacyDetail(id);
    const formData = transferDetailToFormData(detail);
    form.setFieldsValue(formData);
  };

  const closePreview = () => {
    setShowPreivew(false);
    setPreviewTypeOption('zh_CN');
  };

  const previewBeforeSubmit = (formData: PrivateForm) => {
    setDescriptionInfo(formData.description);
    setShowPreivew(true);
  };

  // 提交form表单
  const submit = async () => {
    setLoading(true);
    const formData = form.getFieldsValue();
    const _param = transferFormDataToParam(formData, +(param?.id || 0));
    let result = '';
    let state = '';
    console.log('submit', formData, _param);
    try {
      if (param && param.id && +param.id) {
        state = '更新';
        result = await fetchPrivacyUpdate(_param);
      } else {
        state = '创建';
        result = await fetchPrivacyCreation(_param);
      }

      if (result === ApiSuccessEnum.success) {
        message.success(`${state}成功！`);
        history.back();
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const confirmBeforeSubmit = () => {
    if (isEditing) {
      submit();
      return;
    }
    Modal.confirm({
      title: '提交前确认',
      content: '新增后所有绑定该适用设备的用户需要重新授权该协议。',
      onOk: () => {
        submit();
      },
    });
  };

  useEffect(() => {
    // 额外dva接口请求
  }, [dispatch]);

  useEffect(() => {
    if (param && param.id && +param.id) {
      requestDetailById(+param.id);
    }
  }, [param]);

  return (
    <>
      <ProCard>
        <Form
          {...layout}
          form={form}
          onFinish={previewBeforeSubmit}
          initialValues={initialPrivateForm}
          scrollToFirstError
        >
          <Form.Item
            name="deviceTypeList"
            label="适用设备"
            rules={[{ required: true, message: '请选择适用设备' }]}
          >
            <Select
              mode="multiple"
              options={deviceTypeOptions}
              showSearch={false}
              disabled
            />
          </Form.Item>
          <Form.Item
            name="protocolTime"
            label="协议上架时间"
            rules={[{ required: true, message: '请选择上架时间' }]}
          >
            <DatePicker
              style={{ width: '50%', minWidth: 400 }}
              placeholder="请选择上架时间"
            />
            {/* <DatePicker.RangePicker
              style={{ width: '50%', minWidth: 400 }}
              placeholder={['开始时间', '结束时间']}
            /> */}
          </Form.Item>
          <Form.Item
            name={['description', 'zh_CN']}
            label="协议内容（中文）"
            valuePropName="value"
            shouldUpdate
            rules={[
              {
                required: true,
                message: '请输入中文协议内容',
              },
              {
                validator: async (_, value) => {
                  console.log(value);
                  if (value === '<p><br></p>')
                    return Promise.reject('请输入中文协议内容');
                  return Promise.resolve();
                },
              },
            ]}
          >
            <WangEditor />
          </Form.Item>
          <Form.Item
            name={['description', 'en_US']}
            label="协议内容（英文）"
            valuePropName="value"
            shouldUpdate
            rules={[
              {
                required: true,
                message: '请输入英文协议内容',
              },
              {
                validator: async (_, value) => {
                  if (value === '<p><br></p>')
                    return Promise.reject('请输入英文协议内容');
                  return Promise.resolve();
                },
              },
            ]}
          >
            <WangEditor />
          </Form.Item>
          <Form.Item wrapperCol={{ span: 24 }} style={{ textAlign: 'center' }}>
            <Button
              type="primary"
              htmlType="submit"
              style={{ marginRight: 16 }}
              loading={loading}
              disabled={loading}
            >
              提交
            </Button>
            <Button type="default" onClick={history.back}>
              取消
            </Button>
          </Form.Item>
        </Form>
      </ProCard>
      <Modal
        width={500}
        open={showPreivew}
        onCancel={closePreview}
        destroyOnClose
        title="APP内预览"
        maskClosable={false}
        footer={
          <div style={{ textAlign: 'center' }}>
            <Button
              type="primary"
              size="large"
              style={{ width: 120 }}
              onClick={confirmBeforeSubmit}
            >
              提交
            </Button>
          </div>
        }
      >
        <div>
          <Radio.Group
            style={{ marginBottom: 20 }}
            options={previewTypeOptions}
            optionType="button"
            value={previewTypeOption}
            onChange={(ev) => {
              setPreviewTypeOption(ev.target.value);
              scrollRef.current?.scrollTo({ top: 0, behavior: 'smooth' });
            }}
          />
          <div
            style={{
              margin: '0 auto',
              width: 375,
              height: 500,
              overflow: 'auto',
            }}
            ref={scrollRef}
          >
            <div
              dangerouslySetInnerHTML={{
                __html: descriptionInfo?.[previewTypeOption] || '',
              }}
            ></div>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default Edit;
