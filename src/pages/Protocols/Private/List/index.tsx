import { fetchPrivacyList } from '@/models/privacy/fetch';
import { Privacy, PrivacyListParam } from '@/models/privacy/interface';
import { initPrivacyListParam } from '@/models/privacy/util';
import { Pagination, initPagination } from '@/utils/request';
import { PlusOutlined } from '@ant-design/icons';
import {
  ProColumns,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { Button, Space } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useRef, useState } from 'react';
import ChangeHistory from '../ChangeHistory';
import { PrivateTableForm } from '../interface';

const List: React.FC = () => {
  // const [urlParam] = useUrlState<UrlParam>();
  const [dataList, setDataList] = useState<Privacy[]>([]);
  const [listParam, setListParam] =
    useState<PrivacyListParam>(initPrivacyListParam);
  const [paginator, setPaginator] = useState<Pagination>(initPagination);
  const formRef = useRef<ProFormInstance<PrivateTableForm>>();
  const [selectedRecord, setSelectedRecord] = useState<Privacy>();

  // const deviceTypeOptionList: SelectOption[] = [
  //   { label: 'D4sh', value: DeviceTypeEnum.D4sh },
  //   { label: 'D4H', value: DeviceTypeEnum.D4H },
  //   { label: 'T6', value: DeviceTypeEnum.T5 },
  //   { label: 'T5', value: DeviceTypeEnum.T6 },
  // ];

  // 获取列表数据
  const requestProtocolsList = async (param: PrivacyListParam) => {
    const { items, ...rest } = await fetchPrivacyList(param);
    setPaginator(rest);
    setDataList(items);
  };

  // 编辑
  const edit = (id: number) => {
    history.push(`/protocols/private/edit/${id}`);
  };

  // 展示变更历史
  const openHistoryList = (record: Privacy) => {
    setSelectedRecord(record);
  };

  const closeChangeHistory = () => {
    setSelectedRecord(undefined);
  };

  // Search的表单按钮渲染
  // const searchOptionRender = (
  //   searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  // ) => {
  //   const { form } = searchConfig;
  //   return [
  //     <Button
  //       key="search"
  //       type="primary"
  //       onClick={() => {
  //         const formData: PrivateTableForm = form?.getFieldsValue();
  //         const param: UrlParam = {};
  //         if (formData.deviceType) {
  //           param.type = formData.deviceType.join();
  //         }
  //         postMessageFunction({
  //           type: 'redirect',
  //           content: {
  //             redirectUrl: `/protocols/private`,
  //             param: param as unknown as Record<string, string>,
  //           },
  //         });
  //       }}
  //     >
  //       查询
  //     </Button>,
  //     <Button
  //       key="reset"
  //       type="default"
  //       onClick={() => {
  //         form?.resetFields();
  //         postMessageFunction({
  //           type: 'redirect',
  //           content: {
  //             redirectUrl: `/protocols/private`,
  //           },
  //         });
  //       }}
  //     >
  //       重置
  //     </Button>,
  //   ];
  // };

  const onPaginationChanged = (page: number, pageSize: number) => {
    let index = page;
    if (pageSize !== listParam?.limit) {
      index = 1;
    }
    setListParam({
      ...listParam,
      type: listParam?.type || '*',
      limit: pageSize,
      offset: (index - 1) * pageSize,
    });
  };

  const columns: Array<ProColumns<Privacy>> = [
    // {
    //   title: '设备',
    //   dataIndex: 'deviceType',
    //   hideInTable: true,
    //   valueType: 'select',
    //   renderFormItem: (_, ...rest) => (
    //     <Select
    //       {...rest}
    //       mode="multiple"
    //       options={deviceTypeOptionList}
    //     ></Select>
    //   ),
    // },
    { title: '版本', dataIndex: 'versionNo', width: 100, search: false },
    {
      title: '上架时间',
      dataIndex: 'publishStart',
      search: false,
      render: (_, record) =>
        `${dayjs(record.publishStart).format('YYYY-MM-DD HH:mm:ss')}`,
    },
    {
      title: '创建时间',
      dataIndex: 'createdTime',
      search: false,
      render: (_, record) => (
        <>{dayjs(record.createdTime).format('YYYY-MM-DD HH:mm:ss')}</>
      ),
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      width: 200,
      search: false,
      render: (_, record) => (
        <Space split="|">
          <Button type="link" onClick={() => edit(record.id)}>
            编辑
          </Button>
          <Button type="link" onClick={() => openHistoryList(record)}>
            变更历史
          </Button>
        </Space>
      ),
    },
  ];

  // useEffect(() => {
  //   if (!formRef.current) return;
  //   const formData: PrivateTableForm = {};
  //   const _listParam: PrivacyListParam = {
  //     ...initPrivacyListParam,
  //   };

  //   if (urlParam.type && urlParam.type !== '*') {
  //     formData.deviceType = urlParam.type
  //       .split(',')
  //       .map((item: string) => Number(item));
  //   }
  //   _listParam.type = urlParam.type || '*';

  //   console.log(_listParam);

  //   formRef.current.setFieldsValue(formData);
  //   setListParam(_listParam);
  // }, [urlParam, formRef]);

  useEffect(() => {
    if (!listParam) return;
    requestProtocolsList(listParam);
  }, [listParam]);

  return (
    <>
      <ProTable<Privacy>
        dataSource={dataList}
        columns={columns}
        defaultSize="small"
        rowKey="id"
        formRef={formRef}
        // search={{
        //   defaultCollapsed: false,
        //   span: spanConfig,
        //   optionRender: searchOptionRender,
        // }}
        search={false}
        options={{
          reload: () => {
            if (listParam) setListParam({ ...listParam });
          },
        }}
        scroll={{
          x: columns
            .filter((col) => col.dataIndex !== 'action')
            .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
        }}
        toolbar={{
          actions: [
            <Button
              key="button"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => edit(0)}
            >
              新增版本号
            </Button>,
          ],
        }}
        pagination={{
          pageSize: paginator.limit,
          total: paginator.total,
          showQuickJumper: true,
          onChange: onPaginationChanged,
        }}
      />
      {selectedRecord && (
        <ChangeHistory privacy={selectedRecord} onClose={closeChangeHistory} />
      )}
    </>
  );
};

export default List;
