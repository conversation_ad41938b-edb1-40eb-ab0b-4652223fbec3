import { fetchPrivacyChangeHistory } from '@/models/privacy/fetch';
import {
  Privacy,
  PrivacyChangeHistory,
  PrivacyChangeHistoryListParam,
} from '@/models/privacy/interface';
import { initPrivacyListParam } from '@/models/privacy/util';
import { Pagination, initPagination } from '@/utils/request';
import { Modal, Table, TableProps } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';

interface Props {
  privacy: Privacy;
  onClose: () => void;
}

const ChangeHistory: React.FC<Props> = ({ privacy, onClose }: Props) => {
  const [dataList, setDataList] = useState<PrivacyChangeHistory[]>([]);
  const [listParam, setListParam] = useState<PrivacyChangeHistoryListParam>();
  const [paginator, setPaginator] = useState<Pagination>(initPagination);

  const fieldNameMap: Record<string, string> = {
    time: '协议上新时间',
    zh_CN: '协议内容（中文）',
    en_US: '协议内容（英文）',
  };

  const requestHistoryList = async (param: PrivacyChangeHistoryListParam) => {
    const { items, ...rest } = await fetchPrivacyChangeHistory(param);
    setDataList(items);
    setPaginator(rest);
  };

  const columns: TableProps<PrivacyChangeHistory>['columns'] = [
    {
      title: '变更字段',
      dataIndex: 'field',
      width: 100,
      render: (_, record) => fieldNameMap[record.field] || record.field,
    },
    {
      title: '变更前',
      dataIndex: 'before',
      width: 200,
      render: (_, record) => {
        if (record.field === 'time') {
          return record.before
            .split(',')
            .map((time) => dayjs(+time).format('YYYY-MM-DD HH:mm:ss'))
            .join();
        }
        return _;
      },
    },
    {
      title: '变更后',
      dataIndex: 'after',
      width: 200,
      render: (_, record) => {
        if (record.field === 'time') {
          return record.after
            .split(',')
            .map((time) => dayjs(+time).format('YYYY-MM-DD HH:mm:ss'))
            .join();
        }
        return _;
      },
    },
    {
      title: '操作人',
      dataIndex: 'operator',
      width: 120,
    },
    {
      title: '操作时间',
      // 2025-03-18由operaterTime调整为operateTime
      dataIndex: 'operateTime',
      width: 150,
      render: (_, record) =>
        dayjs(record.operateTime).format('YYYY-MM-DD HH:mm:ss'),
    },
  ];

  const onPaginationChanged = (page: number, pageSize: number) => {
    let index = page;
    if (listParam && pageSize !== listParam.limit) {
      index = 1;
    }
    setListParam({
      ...listParam,
      id: privacy.id,
      limit: pageSize,
      offset: (index - 1) * pageSize,
    });
  };

  useEffect(() => {
    if (!privacy) return;

    setListParam({
      ...initPrivacyListParam,
      id: privacy.id,
    });
  }, [privacy]);

  useEffect(() => {
    if (!listParam) return;

    requestHistoryList(listParam);
  }, [listParam]);

  return (
    <Modal width={1000} title="变更历史" open onCancel={onClose}>
      <Table<PrivacyChangeHistory>
        dataSource={dataList}
        columns={columns}
        rowKey="id"
        pagination={{
          pageSize: paginator.limit,
          total: paginator.total,
          showQuickJumper: true,
          onChange: onPaginationChanged,
        }}
      />
    </Modal>
  );
};

export default ChangeHistory;
