import { BreadcrumbInfo } from '@/models/common.interface';
import global from '@/utils/global';
import { LeftOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { Outlet, useLocation, useParams } from '@umijs/max';
import React, { useEffect, useState } from 'react';

const breadcrumbInfo: BreadcrumbInfo = {
  path: '',
  breadcrumbName: '',
};

const History: React.FC = () => {
  const location = useLocation();
  const [title] = useState(`绑定历史`);
  // const [showBackIcon, setShowBackIcon] = useState(false);
  const [breadcrumbInfoList, setBreadcrumbInfoList] = useState<
    BreadcrumbInfo[]
  >([breadcrumbInfo]);
  const param = useParams<{ device: string }>();

  useEffect(() => {
    const _breadcrumbInfoList = global.getBreadcrumbInfo(
      {
        ...breadcrumbInfo,
        breadcrumbName: global.deviceNames[param.device as string],
      },
      '绑定历史',
      location,
    );
    setBreadcrumbInfoList(_breadcrumbInfoList);
  }, [location, param]);

  return (
    <PageContainer
      header={{
        backIcon: <LeftOutlined />,
        onBack: () => history.back(),
        title,
        ghost: true,
        breadcrumb: {
          itemRender: (route) => <span key={route.path}>{route.title}</span>,
          routes: breadcrumbInfoList,
        },
      }}
    >
      <Outlet />
    </PageContainer>
  );
};

export default History;
