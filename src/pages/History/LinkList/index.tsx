import UserIdLink from '@/components/TableRowLink/UserIdLink';
import { fetchLinkHistory } from '@/models/device/fetch';
import { LinkHistory, LinkHistoryParam } from '@/models/device/interface';
import { initHistoryListParam } from '@/models/device/util';
import {
  initPagination,
  initPaginatorParam,
  Pagination,
} from '@/utils/request';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { useParams } from '@umijs/max';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';

const LinkList: React.FC = () => {
  const [dataList, setDataList] = useState<LinkHistory[]>([]);
  const urlParam = useParams<{ device: string; deviceId: string }>();
  const [pagination, setPagination] = useState<Pagination>(initPagination);
  const [listParam, setListParam] =
    useState<LinkHistoryParam>(initHistoryListParam);

  // 获取用户分组列表数据
  const requestHistoryList = async (param: LinkHistoryParam) => {
    const { items, ...rest } = await fetchLinkHistory(
      param,
      urlParam.device || '',
    );
    setDataList(items);
    setPagination(rest);
  };

  const onPaginationChanged = (page: number, pageSize: number) => {
    let index = page;
    if (listParam.limit !== pageSize) {
      index = 1;
    }
    setListParam({
      ...listParam,
      limit: pageSize,
      offset: (index - 1) * pageSize,
    });
  };

  const columns: Array<ProColumns<LinkHistory>> = [
    {
      title: '设备ID',
      dataIndex: 'deviceId',
    },
    {
      title: '用户ID',
      dataIndex: 'userId',
      render: (_, row) => <UserIdLink userId={row.userId || ''} />,
    },
    {
      title: '绑定时间',
      dataIndex: 'createdAt',
      render: (_, row) =>
        dayjs(new Date(row.createdAt)).format('YYYY-MM-DD HH:mm:ss'),
    },
  ];

  useEffect(() => {
    requestHistoryList({
      ...initPaginatorParam,
      ...listParam,
      id: +(urlParam.deviceId || 0),
    });
  }, [urlParam, listParam]);

  return (
    <ProTable<LinkHistory>
      dataSource={dataList}
      columns={columns}
      defaultSize="small"
      rowKey="createdAt"
      search={false}
      pagination={{
        pageSize: pagination.limit,
        total: pagination.total,
        showQuickJumper: true,
        onChange: onPaginationChanged,
      }}
      scroll={{
        x: columns
          .filter((col) => col.dataIndex === 'action')
          .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
      }}
    />
  );
};

export default LinkList;
