import { ApiSuccessEnum } from '@/models/common.interface';
import {
  fetchSpecAttrCreation,
  fetchSpecAttrUpdating,
} from '@/models/standard/fetch';
import { SpecAttr } from '@/models/standard/interface';
import { ProCard } from '@ant-design/pro-components';
import { useDispatch } from '@umijs/max';
import {
  Button,
  Drawer,
  Form,
  Input,
  InputNumber,
  message,
  Switch,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { SpecAttrForm } from '../interface';
import {
  initialSpecAttrForm,
  transferDetailToFormData,
  transferFormDataToParam,
} from '../util';

interface Props {
  showEdit: boolean;
  specId: number;
  detail?: SpecAttr;
  onClose?: (success: boolean) => void;
}

const Edit: React.FC<Props> = ({
  showEdit,
  specId,
  detail,
  onClose,
}: Props) => {
  const dispatch = useDispatch();
  const [form] = Form.useForm<SpecAttrForm>();
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const layout = {
    labelCol: { span: 3 },
    wrapperCol: { span: 20 },
  };

  useEffect(() => {
    setVisible(showEdit);
  }, [showEdit]);

  useEffect(() => {
    if (!detail) return;
    const formData = transferDetailToFormData(detail);
    form.setFieldsValue(formData);
  }, [detail]);

  useEffect(() => {
    // 额外dva接口请求
  }, [dispatch]);

  // 根据id获取详情数据
  // const requestDetailById = async (id: number) => {
  //   const detail = await fetchSpecDetail(id);
  //   const formData = transferDetailToFormData(detail);
  //   form.setFieldsValue(formData);
  // };

  const onDrawerClosed = (success: boolean) => {
    if (onClose) onClose(success);
    form.resetFields();
  };

  // 提交form表单
  const submit = async (formData: SpecAttrForm) => {
    // setLoading(true);
    const _param = transferFormDataToParam(formData, specId, detail?.id);
    let result = '';
    let state = '';
    try {
      if (detail) {
        state = '更新';
        result = await fetchSpecAttrUpdating(_param);
      } else {
        state = '创建';
        result = await fetchSpecAttrCreation(_param);
      }
      if (result === ApiSuccessEnum.success) {
        message.success(`${state}成功！`);
        onDrawerClosed(true);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Drawer
      open={visible}
      placement="right"
      width={500}
      onClose={() => onDrawerClosed(false)}
    >
      <ProCard>
        <Form
          {...layout}
          form={form}
          onFinish={submit}
          initialValues={initialSpecAttrForm}
        >
          <Form.Item
            name="name"
            label="名称"
            rules={[{ required: true, message: '请输入名称' }]}
          >
            <Input placeholder="请输入规格值名称" />
          </Form.Item>
          <Form.Item
            name="sortIndex"
            label="排序"
            rules={[{ required: true, message: '请输入排序' }]}
          >
            <InputNumber maxLength={40} placeholder="请输入规格值排序" />
          </Form.Item>
          <Form.Item name="status" label="状态" valuePropName="checked">
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>
          <Form.Item style={{ textAlign: 'right' }}>
            <Button
              type="primary"
              htmlType="submit"
              style={{ marginRight: 16 }}
              loading={loading}
              disabled={loading}
            >
              提交
            </Button>
            <Button htmlType="reset" style={{ marginRight: 16 }} danger>
              重置
            </Button>
            <Button type="default" onClick={() => onDrawerClosed(false)}>
              取消
            </Button>
          </Form.Item>
        </Form>
      </ProCard>
    </Drawer>
  );
};

export default Edit;
