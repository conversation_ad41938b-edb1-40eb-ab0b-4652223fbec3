import { ApiSuccessEnum, StatusEnum } from '@/models/common.interface';
import { getStatusMap, spanConfig } from '@/models/common.util';
import {
  fetchSpecAttrList,
  fetchSpecAttrStatusSwitch,
} from '@/models/standard/fetch';
import { SpecAttr } from '@/models/standard/interface';
import { PlusOutlined } from '@ant-design/icons';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProTable,
} from '@ant-design/pro-components';
import { Button, FormInstance, message, Space, Switch } from 'antd';
import { parse } from 'qs';
import React, { useEffect, useState } from 'react';
import Edit from '../Edit';
import { SpecAttrSearchForm } from '../interface';

const List: React.FC = () => {
  const [specAttrList, setSpecAttrList] = useState<SpecAttr[]>([]);
  const [oldSpecAttrList, setOldSpecAttrList] = useState<SpecAttr[]>([]);
  const [showEdit, setShowEdit] = useState(false);
  const [detail, setDetail] = useState<SpecAttr>();
  const queryInfo = parse(location.href.split('?')[1]);
  const [specId, setSpecId] = useState(0);

  // 获取规格值列表数据
  const requestSpecAttrList = async (_specId: number) => {
    const list = await fetchSpecAttrList(_specId);
    setSpecAttrList(list);
    setOldSpecAttrList(list);
  };

  // 编辑规格值
  const editSpecAttr = (_detail?: SpecAttr) => {
    setShowEdit(true);
    setDetail(_detail);
  };

  // 切换状态
  const switchStatus = async (ev: boolean, id: number) => {
    try {
      const result = await fetchSpecAttrStatusSwitch(id, ev);
      if (result === ApiSuccessEnum.success) {
        message.success(`${ev ? '启用' : '禁用'}成功!`);
        const index = [...specAttrList].findIndex((item) => item.id === id);
        specAttrList[index].status = +ev as StatusEnum;
        setSpecAttrList([...specAttrList]);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const filterList = (form?: FormInstance<SpecAttrSearchForm>) => {
    if (!form) return;
    const { name, status } = form.getFieldsValue();
    let _list = [...oldSpecAttrList];
    if (status !== undefined) {
      _list = _list.filter((item) => item.status === status);
    }
    if (name !== undefined) {
      _list = _list.filter((item) => item.name.includes(name));
    }
    setSpecAttrList(_list);
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button key="search" type="primary" onClick={() => filterList(form)}>
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          form?.resetFields();
          filterList(form);
        }}
      >
        重置
      </Button>,
    ];
  };

  const columns: Array<ProColumns<SpecAttr>> = [
    {
      title: '索引',
      dataIndex: 'id',
      search: false,
    },
    {
      title: '规格名',
      dataIndex: 'name',
    },
    {
      title: '排序',
      dataIndex: 'sortIndex',
      search: false,
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueType: 'select',
      valueEnum: getStatusMap(),
      render: (_, row) => (
        <Switch
          checked={!!row.status}
          checkedChildren="启用"
          unCheckedChildren="禁用"
          onChange={(ev) => switchStatus(ev, row.id)}
        />
      ),
    },
    {
      title: '操作',
      dataIndex: 'action',
      search: false,
      width: 150,
      render: (_, row) => (
        <Space>
          <Button type="link" onClick={() => editSpecAttr(row)}>
            编辑
          </Button>
        </Space>
      ),
    },
  ];

  useEffect(() => {
    if (queryInfo && queryInfo?.specId) {
      setSpecId(+queryInfo.specId);
      requestSpecAttrList(+queryInfo.specId);
    }
  }, []);

  return (
    <>
      <ProTable<SpecAttr>
        dataSource={specAttrList}
        columns={columns}
        defaultSize="small"
        rowKey="id"
        search={{
          defaultCollapsed: false,
          span: spanConfig,
          optionRender: searchOptionRender,
        }}
        scroll={{
          x: columns
            .filter((col) => col.dataIndex === 'action')
            .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
        }}
        toolbar={{
          actions: [
            <Button
              key="button"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => editSpecAttr()}
            >
              新增
            </Button>,
          ],
        }}
      />
      <Edit
        showEdit={showEdit}
        specId={detail?.specId || specId}
        detail={detail}
        onClose={(ev) => {
          setShowEdit(false);
          setDetail(undefined);
          if (ev) requestSpecAttrList(specId);
        }}
      />
    </>
  );
};

export default List;
