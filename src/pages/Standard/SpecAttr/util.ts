import { SpecAttr, SpecAttrParam } from '@/models/standard/interface';
import { SpecAttrForm } from './interface';

export const initialSpecAttrForm: SpecAttrForm = {
  name: '',
  sortIndex: null,
  status: false,
};

// 将formData转换为param
export const transferFormDataToParam = (
  formData: SpecAttrForm,
  specId: number,
  id?: number,
): SpecAttrParam => {
  const param: SpecAttrParam = {
    name: formData.name,
    sortIndex: formData.sortIndex || 0,
    specId,
    status: +formData.status,
  };
  id && (param.id = id);
  return param;
};

// 将接口返回的详情数据转换为formData
export const transferDetailToFormData = (detail: SpecAttr): SpecAttrForm => {
  const formData: SpecAttrForm = {
    name: detail.name,
    sortIndex: detail.sortIndex,
    status: !!detail.status,
  };
  return formData;
};
