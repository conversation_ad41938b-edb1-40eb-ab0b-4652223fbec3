import { ApiSuccessEnum, StatusEnum } from '@/models/common.interface';
import { getStatusMap, spanConfig } from '@/models/common.util';
import { fetchSpecList, fetchSpecStatusSwitch } from '@/models/standard/fetch';
import { Spec } from '@/models/standard/interface';
import { PlusOutlined } from '@ant-design/icons';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProTable,
} from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { Button, FormInstance, message, Space, Switch } from 'antd';
import React, { useEffect, useState } from 'react';
import Edit from '../Edit';

const List: React.FC = () => {
  const [oldSpecList, setOldSpecList] = useState<Spec[]>([]);
  const [specList, setSpecList] = useState<Spec[]>([]);
  const [showEdit, setShowEdit] = useState(false);
  const [detail, setDetail] = useState<Spec>();
  // const [listParam, setListParam] = useState<SpecListParam>(initSpecListParam);
  // const [selectedRows, setSelectedRows] = useState<Spec[]>([]);

  // 获取列表数据
  const requestSpecList = async () => {
    const list = await fetchSpecList();
    setSpecList(list);
    setOldSpecList(list);
  };

  // 编辑规格
  const editSpec = (_detail?: Spec) => {
    // history.push(`/detail/${id}`);
    setShowEdit(true);
    setDetail(_detail);
  };

  // 切换状态
  const switchStatus = async (ev: boolean, id: number) => {
    try {
      const result = await fetchSpecStatusSwitch(id, ev);
      if (result === ApiSuccessEnum.success) {
        message.success(`${ev ? '启用' : '禁用'}成功!`);
        const index = [...specList].findIndex((item) => item.id === id);
        specList[index].status = +ev as StatusEnum;
        setSpecList([...specList]);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const filterList = (form?: FormInstance<any>) => {
    if (!form) return;
    const { name, status } = form.getFieldsValue();
    let _list = [...oldSpecList];
    if (status !== undefined) {
      _list = _list.filter((item) => item.status === status);
    }
    if (name !== undefined) {
      _list = _list.filter((item) => item.name.includes(name));
    }
    setSpecList(_list);
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button key="search" type="primary" onClick={() => filterList(form)}>
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          form?.resetFields();
          filterList(form);
        }}
      >
        重置
      </Button>,
    ];
  };

  const columns: Array<ProColumns<Spec>> = [
    {
      title: '索引',
      dataIndex: 'id',
      search: false,
    },
    {
      title: '规格名',
      dataIndex: 'name',
    },
    {
      title: '排序',
      dataIndex: 'sortIndex',
      search: false,
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueType: 'select',
      valueEnum: getStatusMap(),
      render: (_, row) => (
        <Switch
          checked={!!row.status}
          checkedChildren="启用"
          unCheckedChildren="禁用"
          onChange={(ev) => switchStatus(ev, row.id)}
        />
      ),
    },
    {
      title: '操作',
      dataIndex: 'action',
      search: false,
      width: 150,
      render: (_, row) => (
        <Space>
          <Button
            type="link"
            onClick={() =>
              history.push(`/standard/specAttr/list?specId=${row.id}`)
            }
          >
            查看属性
          </Button>
          <>|</>
          <Button type="link" onClick={() => editSpec(row)}>
            编辑
          </Button>
        </Space>
      ),
    },
  ];

  useEffect(() => {
    requestSpecList();
  }, []);

  return (
    <>
      <ProTable<Spec>
        dataSource={specList}
        columns={columns}
        defaultSize="small"
        rowKey="id"
        search={{
          defaultCollapsed: false,
          span: spanConfig,
          optionRender: searchOptionRender,
        }}
        scroll={{
          x: columns
            .filter((col) => col.dataIndex === 'action')
            .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
        }}
        toolbar={{
          actions: [
            <Button
              key="button"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => editSpec()}
            >
              添加规格
            </Button>,
          ],
        }}
      />
      <Edit
        showEdit={showEdit}
        detail={detail}
        onClose={(ev) => {
          setShowEdit(false);
          if (ev) requestSpecList();
        }}
      />
    </>
  );
};

export default List;
