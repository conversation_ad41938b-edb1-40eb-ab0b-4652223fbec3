import { StatusEnum } from '@/models/common.interface';
import { Spec, SpecParam } from '@/models/standard/interface';
import { SpecForm } from './interface';

export const initialSpecForm: SpecForm = {
  name: '',
  sortIndex: null,
  status: false,
};

// 将formData转换为param
export const transferFormDataToParam = (formData: SpecForm, id?: number): SpecParam => {
  const param: SpecParam = {
    name: formData.name,
    status: +formData.status as StatusEnum,
    sortIndex: formData.sortIndex?.toString() || '',
  };
  id && (param.id = id);
  return param;
};

// 将接口返回的详情数据转换为formData
export const transferDetailToFormData = (detail: Spec): SpecForm => {
  const formData: SpecForm = {
    name: detail.name,
    sortIndex: +detail.sortIndex,
    status: !!detail.status,
  };
  return formData;
};
