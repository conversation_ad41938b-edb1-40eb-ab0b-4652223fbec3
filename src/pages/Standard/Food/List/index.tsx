import { ApiSuccessEnum, StatusEnum } from '@/models/common.interface';
import { getStatusMap, spanConfig } from '@/models/common.util';
import { ConnectState } from '@/models/connect';
import { fetchFoodList, fetchFoodStatusSwitch } from '@/models/standard/fetch';
import { Food } from '@/models/standard/interface';
import { PlusOutlined } from '@ant-design/icons';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProTable,
} from '@ant-design/pro-components';
import { getImageResult } from '@mantas/image-result-json';
import { history, useDispatch, useSelector } from '@umijs/max';
import { Button, FormInstance, Image, Space, Switch, message } from 'antd';
import { parse } from 'qs';
import React, { useEffect, useState } from 'react';
import { FoodSearchForm } from '../interface';

interface BrandInfo {
  id: number;
  name: string;
}

const List: React.FC = () => {
  const dispatch = useDispatch();
  const [foodList, setFoodList] = useState<Food[]>([]);
  const [oldFoodList, setOldFoodList] = useState<Food[]>([]);
  const queryInfo = parse(location.href.split('?')[1]);
  const [brandInfo, setBrandInfo] = useState<BrandInfo>({ id: 0, name: '' });
  // const petTypeList = useSelector(({ standard }: ConnectState) => standard.petTypeList);
  const petTypesMap: Map<string, string> = useSelector(
    ({ standard }: ConnectState) => standard.petTypesMap,
  );

  const getComposeInfo = (compose: string, key: string) => {
    if (!compose || !key) return '';
    const component = JSON.parse(compose);
    return component[key];
  };

  // 获取用户分组列表数据
  const requestFoodList = async (_brandId: number) => {
    const list = await fetchFoodList(_brandId);
    setFoodList(list);
    setOldFoodList(list);
  };

  // 编辑规格值
  const editFood = (id: number, _brandInfo: BrandInfo) => {
    if (!_brandInfo || !_brandInfo.id || !_brandInfo.name) return;

    history.push(
      `/standard/food/edit/${id}?brandId=${_brandInfo.id}&brandName=${_brandInfo.name}`,
    );
  };

  // 切换状态
  const switchStatus = async (ev: boolean, id: number) => {
    try {
      const result = await fetchFoodStatusSwitch(id, ev);
      if (result === ApiSuccessEnum.success) {
        message.success(`${ev ? '启用' : '禁用'}成功!`);
        const index = [...foodList].findIndex((item) => item.id === id);
        foodList[index].status = +ev as StatusEnum;
        setFoodList([...foodList]);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const filterList = (form?: FormInstance<FoodSearchForm>) => {
    if (!form) return;
    const { name, status, breed } = form.getFieldsValue();
    let _list = [...oldFoodList];
    if (status !== undefined) {
      _list = _list.filter((item) => item.status === status);
    }
    if (name !== undefined) {
      _list = _list.filter((item) => item.name.includes(name));
    }
    if (breed !== undefined) {
      _list = _list.filter((item) => item.breed === breed);
    }
    setFoodList(_list);
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button key="search" type="primary" onClick={() => filterList(form)}>
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          form?.resetFields();
          filterList(form);
        }}
      >
        重置
      </Button>,
    ];
  };

  const columns: Array<ProColumns<Food>> = [
    {
      title: '主粮名',
      dataIndex: 'name',
    },
    {
      title: '图片',
      dataIndex: 'images',
      search: false,
      render: (_, row) => (
        <Image width={100} src={getImageResult(row.images).result[0]} />
      ),
    },
    {
      title: '索引',
      dataIndex: 'index',
      search: false,
    },
    {
      title: '蛋白质',
      dataIndex: 'protein',
      search: false,
      render: (_, row) => getComposeInfo(row.compose, 'protein'),
    },
    {
      title: '脂肪',
      dataIndex: 'fat',
      search: false,
      render: (_, row) => getComposeInfo(row.compose, 'fat'),
    },
    {
      title: '水分',
      dataIndex: 'water',
      search: false,
      render: (_, row) => getComposeInfo(row.compose, 'water'),
    },
    {
      title: '纤维',
      dataIndex: 'fibre',
      search: false,
      render: (_, row) => getComposeInfo(row.compose, 'fibre'),
    },
    {
      title: '能量',
      dataIndex: 'energy',
      search: false,
      render: (_, row) => getComposeInfo(row.compose, 'energy'),
    },
    {
      title: '消化率',
      dataIndex: 'digestRate',
      search: false,
      render: (_, row) => getComposeInfo(row.compose, 'digestRate'),
    },
    {
      title: '品种',
      dataIndex: 'breed',
      valueEnum: petTypesMap,
      render: (_, row) => petTypesMap.get(row.breed),
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueType: 'select',
      valueEnum: getStatusMap(),
      render: (_, row) => (
        <Switch
          checked={!!row.status}
          checkedChildren="启用"
          unCheckedChildren="禁用"
          onChange={(ev) => switchStatus(ev, row.id)}
        />
      ),
    },
    {
      title: '操作',
      dataIndex: 'action',
      search: false,
      width: 150,
      render: (_, row) => (
        <Space>
          <Button type="link" onClick={() => editFood(row.id, brandInfo)}>
            编辑
          </Button>
        </Space>
      ),
    },
  ];

  useEffect(() => {
    if (queryInfo && queryInfo?.brandId && queryInfo?.brandName) {
      setBrandInfo({
        id: +queryInfo.brandId,
        name: queryInfo.brandName as string,
      });
      requestFoodList(+queryInfo.brandId);
    }
  }, []);

  useEffect(() => {
    dispatch({ type: 'standard/requestPetTypeList' });
  }, [dispatch]);

  return (
    <ProTable<Food>
      dataSource={foodList}
      columns={columns}
      defaultSize="small"
      rowKey="id"
      search={{
        defaultCollapsed: false,
        span: spanConfig,
        optionRender: searchOptionRender,
      }}
      scroll={{
        x: columns
          .filter((col) => col.dataIndex === 'action')
          .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
      }}
      toolbar={{
        actions: [
          <Button
            key="button"
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => editFood(0, brandInfo)}
          >
            新增
          </Button>,
        ],
      }}
    />
  );
};

export default List;
