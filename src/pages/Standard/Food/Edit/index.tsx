import Uploader from '@/components/Uploader';
import WangEditor from '@/components/WangEditor';
import { ApiSuccessEnum } from '@/models/common.interface';
import { ConnectState } from '@/models/connect';
import {
  fetchFoodCreation,
  fetchFoodDetailById,
  fetchFoodUpdating,
} from '@/models/standard/fetch';
import { MinusCircleOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { history, useDispatch, useParams, useSelector } from '@umijs/max';
import {
  Button,
  Form,
  Input,
  InputNumber,
  message,
  Select,
  Space,
  Switch,
} from 'antd';
import { parse } from 'qs';
import React, { useEffect, useState } from 'react';
import styles from '../index.less';
import { FoodForm } from '../interface';
import {
  initialFoodForm,
  transferDetailToFormData,
  transferFormDataToParam,
} from '../util';

const Edit: React.FC = () => {
  const componentTip = '请输入0~1之内的小数';
  const dispatch = useDispatch();
  const param = useParams<{ id: string }>();
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm<FoodForm>();
  const breed = Form.useWatch('breed', form);
  const petTypeOptions = useSelector(
    ({ standard }: ConnectState) => standard.petTypeOptions,
  );
  const queryInfo = parse(location.href.split('?')[1]);
  const layout = {
    labelCol: { span: 3 },
    wrapperCol: { span: 20 },
  };

  // 根据id获取详情数据
  const requestDetailById = async (id: number) => {
    const detail = await fetchFoodDetailById(id);
    const formData = transferDetailToFormData(detail);
    form.setFieldsValue(formData);
  };

  // 提交form表单
  const submit = async (formData: FoodForm) => {
    // setLoading(true);
    const _param = transferFormDataToParam(
      formData,
      +(queryInfo?.brandId || 0),
      +(param?.id || 0),
    );
    console.log(formData, _param);
    let result = '';
    let state = '';
    try {
      if (param && param.id && +param.id) {
        state = '更新';
        result = await fetchFoodUpdating(_param);
      } else {
        state = '创建';
        result = await fetchFoodCreation(_param);
      }

      if (result === ApiSuccessEnum.success) {
        message.success(`${state}成功！`);
        history.back();
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  // 处理上传文件格式
  const normFile = (e: any) => {
    // console.log('Upload event:', e);
    if (Array.isArray(e)) {
      return e;
    }
    return e && e.fileList;
  };

  useEffect(() => {
    if (param && param.id && +param.id) {
      requestDetailById(+param.id);
    }
  }, [param]);

  useEffect(() => {
    // 额外dva接口请求
    dispatch({ type: 'standard/requestPetTypeList' });
  }, [dispatch]);

  useEffect(() => {
    let _digestRate = 90;
    if (breed === 'Cat') {
      _digestRate = 88;
    }
    form.setFieldsValue({ ...form.getFieldsValue(), digestRate: _digestRate });
  }, [breed]);

  return (
    <Form
      className={styles.form}
      {...layout}
      form={form}
      onFinish={submit}
      initialValues={{
        ...initialFoodForm,
        brandName: queryInfo?.brandName || '',
      }}
    >
      <ProCard title="基本信息" className={styles.card}>
        <Form.Item name="brandName" label="所属品牌">
          <Input disabled />
        </Form.Item>
        <Form.Item
          name="name"
          label="名称"
          rules={[{ required: true, message: '请输入主粮名称' }]}
        >
          <Input placeholder="请输入名称" />
        </Form.Item>
        <Form.Item
          name="breed"
          label="类别"
          rules={[{ required: true, message: '请选择类别' }]}
        >
          <Select placeholder="请选择主粮类别" options={petTypeOptions} />
        </Form.Item>
        <Form.Item
          name="index"
          label="主粮索引"
          rules={[{ required: true, message: '请输入主粮索引' }]}
        >
          <Input placeholder="请输入主粮索引" />
        </Form.Item>
        <Form.Item
          name="status"
          label="状态"
          rules={[{ required: true, message: '请选择状态' }]}
          valuePropName="checked"
        >
          <Switch checkedChildren="启用" unCheckedChildren="禁用" />
        </Form.Item>
        <Form.Item
          name="images"
          label="主粮图片"
          getValueFromEvent={normFile}
          extra="主粮图片"
          shouldUpdate
          valuePropName="fileList"
          // rules={[{ required: true, message: '请选择主粮图片' }]}
        >
          <Uploader />
        </Form.Item>
      </ProCard>
      <ProCard title="成分信息" className={styles.card}>
        <Form.Item
          name="protein"
          label="蛋白质"
          rules={[{ required: true, message: componentTip }]}
        >
          <InputNumber min={0} max={1} step={0.1} placeholder={componentTip} />
        </Form.Item>
        <Form.Item
          name="fat"
          label="脂肪"
          rules={[{ required: true, message: componentTip }]}
        >
          <InputNumber min={0} max={1} step={0.1} placeholder={componentTip} />
        </Form.Item>
        <Form.Item name="water" label="水分">
          <InputNumber min={0} max={1} step={0.1} placeholder={componentTip} />
        </Form.Item>
        <Form.Item name="fibre" label="纤维">
          <InputNumber min={0} max={1} step={0.1} placeholder={componentTip} />
        </Form.Item>
        <Form.Item
          name="energy"
          label="能量"
          // rules={[
          //   {
          //     required: true,
          //     message: '1Kg主粮的卡路里含量，如无法提供上门的成分信息，请务必填写能量信息',
          //   },
          // ]}
        >
          <InputNumber placeholder="1Kg主粮的卡路里含量，如无法提供上门的成分信息，请务必填写能量信息" />
        </Form.Item>
        <Form.Item
          name="digestRate"
          label="消化率"
          rules={[{ required: true, message: '一般情况下，猫为88，狗为90' }]}
        >
          <Input placeholder="一般情况下，猫为88，狗为90" />
        </Form.Item>
        <Form.Item label="其它成分">
          <Form.List name="otherComponents">
            {(fields, { add, remove }) => (
              <>
                {fields.map((field, index) => (
                  <section key={field.key}>
                    <Space className={styles.other} align="center">
                      <Form.Item name={[field.name, 'key']}>
                        <Input placeholder="成分名" />
                      </Form.Item>
                      <Form.Item name={[field.name, 'value']}>
                        <InputNumber placeholder="量" />
                      </Form.Item>
                      {index === fields.length - 1 && (
                        <PlusCircleOutlined
                          className={styles.icon}
                          onClick={() => add()}
                        />
                      )}
                      {index !== fields.length - 1 && (
                        <MinusCircleOutlined
                          className={styles.icon}
                          onClick={() => remove(index)}
                        />
                      )}
                    </Space>
                  </section>
                ))}
              </>
            )}
          </Form.List>
        </Form.Item>
      </ProCard>
      <ProCard title="描述信息" className={styles.card}>
        <Form.Item name="detail" wrapperCol={{ offset: 2, span: 20 }}>
          <Input.TextArea rows={10} placeholder="请输入描述信息" />
        </Form.Item>
      </ProCard>
      <ProCard title="其他信息" className={styles.card}>
        <Form.Item
          name="description"
          wrapperCol={{ offset: 2, span: 20 }}
          valuePropName="value"
          shouldUpdate
        >
          <WangEditor />
        </Form.Item>
      </ProCard>
      <ProCard className={styles.card} style={{ textAlign: 'center' }}>
        {/* <Form.Item
          wrapperCol={{ offset: 2, span: 20 }}
          style={{ width: '100%', textAlign: 'center' }}
        >
        </Form.Item> */}
        <Button
          type="primary"
          htmlType="submit"
          style={{ marginRight: 16 }}
          loading={loading}
          disabled={loading}
        >
          提交
        </Button>
        <Button htmlType="reset" style={{ marginRight: 16 }} danger>
          重置
        </Button>
        <Button type="default" onClick={history.back}>
          取消
        </Button>
      </ProCard>
    </Form>
    // <ProCard>
    // </ProCard>
  );
};

export default Edit;
