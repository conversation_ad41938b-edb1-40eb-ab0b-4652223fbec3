import { StatusEnum } from '@/models/common.interface';
import {
  FoodComponent,
  FoodDetail,
  FoodParam,
  OtherFoodComponentParam,
} from '@/models/standard/interface';
import { uuid } from '@/utils/uuid';
import { getImageResult, getImageResultJson } from '@mantas/image-result-json';
import { FoodForm, OtherComponentsFormData } from './interface';

export const digestRateMap = {
  Dog: 90,
  Cat: 88,
};

export const initialFoodForm: FoodForm = {
  // 基本信息
  brandName: '',
  name: '',
  breed: 'Dog',
  index: '',
  status: false,
  images: [],
  // 成分信息
  protein: null,
  fat: null,
  water: null,
  fibre: null,
  energy: null,
  digestRate: digestRateMap.Dog,
  otherComponents: [{ key: '', value: null }],
  // 描述信息
  detail: '',
  // 其他信息
  description: '',
};

// 将formData转换为param
export const transferFormDataToParam = (
  formData: FoodForm,
  brandId: number,
  id?: number,
): FoodParam => {
  const images = getImageResultJson(
    formData.images.map((img) => img.url || ''),
  );
  const other: OtherFoodComponentParam = {};
  formData.otherComponents.forEach((item) => {
    if (item.value) {
      other[`${item.key}`] = item.value;
    }
  });
  const param: FoodParam = {
    brandId,
    name: formData.name,
    images,
    status: +formData.status as StatusEnum,
    index: formData.index,
    breed: formData.breed,
    detail: formData.detail,
    compose: JSON.stringify({
      digestRate: formData.digestRate || 0,
      energy: formData.energy || 0,
      fat: formData.fat || 0,
      fibre: formData.fibre || 0.1,
      protein: formData.protein || 0,
      water: formData.water || 0.1,
      other,
    }),
    description: formData.description,
  };
  id && (param.id = id);
  return param;
};

// 将接口返回的详情数据转换为formData
export const transferDetailToFormData = (detail: FoodDetail): FoodForm => {
  let compose: FoodComponent = {};
  const otherComponents: OtherComponentsFormData[] = [];
  try {
    compose = detail.compose ? JSON.parse(detail.compose) : {};
  } catch (error) {
    console.log(error);
  }

  if (compose.other) {
    for (const oc in compose.other) {
      if (compose.other[oc]) {
        otherComponents.push({ key: oc, value: compose.other[oc] });
      }
    }
  } else {
    otherComponents.push({ key: '', value: null });
  }

  const images = getImageResult(detail.images).result;

  const formData: FoodForm = {
    // 基本信息
    brandName: detail.brand.name,
    name: detail.name,
    breed: detail.breed,
    index: detail.index,
    status: !!detail.status,
    images: images.map((item) => ({
      url: item,
      uid: uuid(),
      name: '主粮图片',
    })),
    // 成分信息
    protein: compose.protein || null,
    fat: compose.fat || null,
    water: compose.water || null,
    fibre: compose.fibre || null,
    energy: compose.energy || null,
    digestRate: compose.digestRate || null,
    otherComponents,
    // 描述信息
    detail: detail.detail,
    // 其他信息
    description: detail.description,
  };
  return formData;
};
