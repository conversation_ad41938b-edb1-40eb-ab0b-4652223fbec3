import { StatusEnum } from '@/models/common.interface';
import { UploadFile } from 'antd';

export interface FoodSearchForm {
  name: string;
  breed: string;
  status: StatusEnum;
}

export interface FoodForm {
  // 基本信息
  brandName: string;
  name: string;
  breed: string;
  index: string;
  status: boolean;
  images: Array<UploadFile<string>>;
  // 成分信息
  protein: number | null;
  fat: number | null;
  water: number | null;
  fibre: number | null;
  energy: number | null;
  digestRate: number | null;
  otherComponents: OtherComponentsFormData[];
  // 描述信息
  detail: string;
  // 其他信息
  description: string;
}

export interface OtherComponentsFormData {
  key: string;
  value: number | null;
}
