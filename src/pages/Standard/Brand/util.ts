import { StatusEnum } from '@/models/common.interface';
import { BrandDetail, BrandParam } from '@/models/standard/interface';
import { uuid } from '@/utils/uuid';
import { getImageResult } from '@mantas/image-result-json';
import { BrandForm } from './interface';

export const initialBrandForm: BrandForm = {
  name: '',
  index: '',
  images: [],
  description: '',
  status: false,
};

// 将formData转换为param
export const transferFormDataToParam = (
  formData: BrandForm,
  id?: number,
): BrandParam => {
  const param: BrandParam = {
    name: JSON.stringify({ zh_CN: formData.name }),
    icon: formData.images[0].url || '',
    index: JSON.stringify({ zh_CN: formData.index }),
    description: formData.description,
    status: +formData.status as StatusEnum,
  };
  if (id) param.id = id;
  return param;
};

// 将接口返回的详情数据转换为formData
export const transferDetailToFormData = (detail: BrandDetail): BrandForm => {
  const image = getImageResult(detail.icon).result[0];
  const formData: BrandForm = {
    name: detail.name,
    index: detail.index,
    images: image
      ? [
          {
            uid: uuid(),
            name: '标贴图片',
            url: image,
          },
        ]
      : [],
    description: detail.description,
    status: !!detail.status,
  };
  return formData;
};
