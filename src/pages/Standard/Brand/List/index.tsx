import { ApiSuccessEnum, StatusEnum } from '@/models/common.interface';
import { getStatusMap, spanConfig } from '@/models/common.util';
import {
  fetchBrandList,
  fetchBrandStatusSwitch,
} from '@/models/standard/fetch';
import { Brand } from '@/models/standard/interface';
import { PlusOutlined } from '@ant-design/icons';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProTable,
} from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { Button, FormInstance, Image, message, Space, Switch } from 'antd';
import React, { useEffect, useState } from 'react';

const List: React.FC = () => {
  const [brandList, setBrandList] = useState<Brand[]>([]);
  const [oldBrandList, setOldBrandList] = useState<Brand[]>([]);

  // 获取用户分组列表数据
  const requestBrandList = async () => {
    const list = await fetchBrandList();
    setBrandList(list);
    setOldBrandList(list);
  };

  // 编辑规格
  const editBrand = (id: number) => {
    history.push(`/standard/brand/edit/${id}`);
  };

  // 切换状态
  const switchStatus = async (ev: boolean, id: number) => {
    try {
      const result = await fetchBrandStatusSwitch(id, ev);
      if (result === ApiSuccessEnum.success) {
        message.success(`${ev ? '启用' : '禁用'}成功!`);
        const index = [...brandList].findIndex((item) => item.id === id);
        brandList[index].status = +ev as StatusEnum;
        setBrandList([...brandList]);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const filterList = (form?: FormInstance<any>) => {
    if (!form) return;
    const { name, status } = form.getFieldsValue();
    let _list = [...oldBrandList];
    if (status !== undefined) {
      _list = _list.filter((item) => item.status === status);
    }
    if (name !== undefined) {
      _list = _list.filter((item) => item.name.includes(name));
    }
    setBrandList(_list);
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button key="search" type="primary" onClick={() => filterList(form)}>
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          form?.resetFields();
          filterList(form);
        }}
      >
        重置
      </Button>,
    ];
  };

  const columns: Array<ProColumns<Brand>> = [
    {
      title: '品牌图',
      dataIndex: 'icon',
      search: false,
      render: (_, row) => <Image width={100} src={row.icon} />,
    },
    {
      title: '品牌名',
      dataIndex: 'name',
    },
    {
      title: '索引',
      dataIndex: 'index',
      search: false,
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueType: 'select',
      valueEnum: getStatusMap(),
      render: (_, row) => (
        <Switch
          checked={!!row.status}
          checkedChildren="启用"
          unCheckedChildren="禁用"
          onChange={(ev) => switchStatus(ev, row.id)}
        />
      ),
    },
    {
      title: '操作',
      dataIndex: 'action',
      search: false,
      width: 150,
      render: (_, row) => (
        <Space>
          <Button
            type="link"
            onClick={() => {
              history.push(
                `/standard/food/list?brandId=${row.id}&brandName=${row.name}`,
              );
            }}
          >
            查看主粮
          </Button>
          <>|</>
          <Button type="link" onClick={() => editBrand(row.id)}>
            编辑
          </Button>
        </Space>
      ),
    },
  ];

  useEffect(() => {
    requestBrandList();
  }, []);

  return (
    <ProTable<Brand>
      dataSource={brandList}
      columns={columns}
      defaultSize="small"
      rowKey="id"
      search={{
        defaultCollapsed: false,
        span: spanConfig,
        optionRender: searchOptionRender,
      }}
      scroll={{
        x: columns
          .filter((col) => col.dataIndex === 'action')
          .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
      }}
      toolbar={{
        actions: [
          <Button
            key="button"
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => editBrand(0)}
          >
            新增
          </Button>,
        ],
      }}
    />
  );
};

export default List;
