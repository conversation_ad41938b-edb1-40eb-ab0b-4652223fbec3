import Uploader from '@/components/Uploader';
import { ApiSuccessEnum } from '@/models/common.interface';
import {
  fetchBrandCreation,
  fetchBrandDetailById,
  fetchBrandUpdating,
} from '@/models/standard/fetch';
import { ProCard } from '@ant-design/pro-components';
import { history, useParams } from '@umijs/max';
import { Button, Form, Input, message, Switch } from 'antd';
import React, { useEffect, useState } from 'react';
import { BrandForm } from '../interface';
import {
  initialBrandForm,
  transferDetailToFormData,
  transferFormDataToParam,
} from '../util';

const Edit: React.FC = () => {
  const param = useParams<{ id: string }>();
  const [form] = Form.useForm<BrandForm>();
  const [loading, setLoading] = useState(false);
  const layout = {
    labelCol: { span: 3 },
    wrapperCol: { span: 20 },
  };

  // 根据id获取详情数据
  const requestDetailById = async (id: number) => {
    const detail = await fetchBrandDetailById(id);
    const formData = transferDetailToFormData(detail);
    form.setFieldsValue(formData);
  };

  // 提交form表单
  const submit = async (formData: BrandForm) => {
    setLoading(true);
    const _param = transferFormDataToParam(formData, +(param?.id || 0));
    let result = '';
    let state = '';
    try {
      if (param && param.id && +param.id) {
        state = '更新';
        result = await fetchBrandUpdating(_param);
      } else {
        state = '创建';
        result = await fetchBrandCreation(_param);
      }

      if (result === ApiSuccessEnum.success) {
        message.success(`${state}成功！`);
        history.back();
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  // 处理上传文件格式
  const normFile = (e: any) => {
    // console.log('Upload event:', e);
    if (Array.isArray(e)) {
      return e;
    }
    return e && e.fileList;
  };

  useEffect(() => {
    if (param && param.id && +param.id) {
      requestDetailById(+param.id);
    }
  }, [param]);

  return (
    <ProCard>
      <Form
        {...layout}
        form={form}
        onFinish={submit}
        initialValues={initialBrandForm}
      >
        <Form.Item
          name="name"
          label="品牌名称"
          rules={[{ required: true, message: '请输入品牌名称' }]}
        >
          <Input placeholder="请输入品牌名称" />
        </Form.Item>
        <Form.Item
          name="index"
          label="品牌索引"
          rules={[{ required: true, message: '请输入品牌索引' }]}
        >
          <Input placeholder="请输入品牌索引" />
        </Form.Item>
        <Form.Item name="status" label="状态" valuePropName="checked">
          <Switch checkedChildren="启用" unCheckedChildren="禁用" />
        </Form.Item>
        <Form.Item
          name="images"
          label="品牌图"
          getValueFromEvent={normFile}
          extra="品牌图"
          shouldUpdate
          valuePropName="fileList"
          rules={[{ required: true, message: '请选择品牌图' }]}
        >
          <Uploader />
        </Form.Item>
        <Form.Item name="description" label="品牌描述">
          <Input.TextArea placeholder="请输入品牌描述" />
        </Form.Item>
        <div style={{ width: '100%', textAlign: 'center' }}>
          <Button
            type="primary"
            htmlType="submit"
            style={{ marginRight: 16 }}
            loading={loading}
            disabled={loading}
          >
            提交
          </Button>
          <Button htmlType="reset" style={{ marginRight: 16 }} danger>
            重置
          </Button>
          <Button type="default" onClick={history.back}>
            取消
          </Button>
        </div>
      </Form>
    </ProCard>
  );
};

export default Edit;
