import Md5FileValidUpload from '@/components/Md5FileValidUpload';
import Uploader from '@/components/Uploader';
import {
  fetchAttireCreation,
  fetchAttireDetail,
  fetchAttirePriorityJudgement,
  fetchAttireUpdate,
} from '@/models/attire/fetch';
import { AttireDetail } from '@/models/attire/interface';
import { ProCard } from '@ant-design/pro-components';
import { history, useDispatch, useParams } from '@umijs/max';
import {
  Alert,
  Button,
  Col,
  Form,
  Input,
  InputNumber,
  Row,
  Switch,
  Typography,
  message,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { DressUpForm } from '../interface';
import {
  initialDressUpForm,
  transferDetailToFormData,
  transferFormDataToParam,
} from '../util';

const Edit: React.FC = () => {
  const dispatch = useDispatch();
  const nameLabels = ['zh_CN', 'en_US'];
  const urlRestParam = useParams<{ id: string }>();
  const [form] = Form.useForm<DressUpForm>();
  const [loading, setLoading] = useState(false);
  const [detail, setDetail] = useState<AttireDetail>();
  const layout = {
    labelCol: { span: 3 },
    wrapperCol: { span: 20 },
  };

  // 处理上传文件格式
  const normFile = (e: any) => {
    // console.log('Upload event:', e);
    if (Array.isArray(e)) {
      return e;
    }
    return e && e.fileList;
  };

  // 根据id获取详情数据
  const requestDetailById = async (id: number) => {
    const detail = await fetchAttireDetail(id);
    const formData = transferDetailToFormData(detail);
    form.setFieldsValue(formData);
    setDetail(detail);
  };

  // 提交form表单
  const submit = async (formData: DressUpForm) => {
    setLoading(true);
    const _param = transferFormDataToParam(formData, +(urlRestParam.id || 0));
    // console.log('submit', formData, _param);
    let result = '';
    let state = '';
    try {
      if (_param && _param.id && +_param.id) {
        state = '更新';
        result = await fetchAttireUpdate(_param);
      } else {
        state = '创建';
        result = await fetchAttireCreation(_param);
      }

      if (result) {
        message.success(`${state}成功！`);
        history.back();
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (urlRestParam.id && +urlRestParam.id) {
      requestDetailById(+urlRestParam.id);
    }
  }, [urlRestParam]);

  useEffect(() => {
    // 额外dva接口请求
  }, [dispatch]);

  return (
    <Form
      {...layout}
      form={form}
      onFinish={submit}
      initialValues={initialDressUpForm}
    >
      <ProCard>
        <Form.List name="names">
          {(fields) => (
            <>
              <Row>
                <Col offset={1}>装扮名称</Col>
              </Row>
              {fields.map(({ key, name, ...restField }, index) => (
                <Form.Item
                  {...restField}
                  key={String(index)}
                  name={[name, nameLabels[index]]}
                  label={nameLabels[index]}
                  rules={[
                    {
                      required: nameLabels[index] === 'zh_CN',
                      message: '请至少提供中文的装扮名称',
                    },
                  ]}
                >
                  <Input
                    placeholder="请输入装扮名称"
                    maxLength={nameLabels[index] === 'zh_CN' ? 6 : 20}
                  />
                </Form.Item>
              ))}
            </>
          )}
        </Form.List>
        <Form.Item
          name="userIds"
          label="用户ID"
          extra={
            <Alert
              type="info"
              message="用于内部测试，为空则默认全部用户；输入多个以英文逗号进行分隔。"
            />
          }
        >
          <Input placeholder="请输入用户ID" />
        </Form.Item>
        <Form.Item
          name="priority"
          label="优先级"
          extra={<Alert type="info" message="数字越大，优先级越高" />}
          rules={[
            { required: true, message: '请输入优先级（请输入大于0的整数）' },
            {
              validator: async (_, value) => {
                if (!value && value !== 0) {
                  return Promise.resolve();
                }

                if (value === detail?.priority) {
                  return Promise.resolve();
                }

                try {
                  const result = await fetchAttirePriorityJudgement({
                    priority: value,
                    id: detail?.id || undefined,
                  });
                  if (result) {
                    return Promise.resolve();
                  } else {
                    return Promise.reject('已存在该优先级的值');
                  }
                } catch (error: any) {
                  return Promise.reject(
                    error.message || '请尝试其它优先级的值',
                  );
                }
              },
            },
          ]}
        >
          <InputNumber min={0} precision={0} placeholder="请输入大于0的整数" />
        </Form.Item>
        <Form.Item
          name="isSubscribe"
          label="是否开通服务使用"
          valuePropName="checked"
          rules={[
            { required: true, message: '请输入优先级（请输入大于0的整数）' },
          ]}
        >
          <Switch
            unCheckedChildren="否"
            checkedChildren="是"
            disabled={!!+(urlRestParam.id || 0)}
          />
        </Form.Item>
        <Form.Item
          name="thumbnailPics"
          label="装扮缩略图"
          extra="尺寸为327px*228px"
          getValueFromEvent={normFile}
          valuePropName="fileList"
        >
          <Uploader maxCount={1} />
        </Form.Item>
        <Form.Item label="首页直播装扮" extra="尺寸为300px*360px">
          <Form.Item
            name="leftLivePics"
            getValueFromEvent={normFile}
            valuePropName="fileList"
            shouldUpdate
            noStyle
          >
            <Uploader maxCount={1} uploadButtonText="上传左图" />
          </Form.Item>
          <Form.Item
            name="rightLivePics"
            getValueFromEvent={normFile}
            valuePropName="fileList"
            shouldUpdate
            noStyle
          >
            <Uploader maxCount={1} uploadButtonText="上传右图" />
          </Form.Item>
        </Form.Item>
        <Form.Item
          name="backgroundPics"
          label="首页背景"
          getValueFromEvent={normFile}
          valuePropName="fileList"
          shouldUpdate
          extra="尺寸为750px*1624px"
        >
          <Uploader maxCount={1} />
        </Form.Item>
      </ProCard>

      <ProCard title="固件类型" style={{ marginTop: 16 }}>
        <div>
          <Typography.Title style={{ marginLeft: 32 }} level={5}>
            d4sh、d4h
          </Typography.Title>
          <Form.Item label="回放装扮" name="fileValidUpload">
            <Md5FileValidUpload />
          </Form.Item>
        </div>
        <div>
          <Typography.Title style={{ marginLeft: 32 }} level={5}>
            d4sh-2、d4h-2
          </Typography.Title>
          <Form.Item label="回放装扮" name="fileValidUpload2">
            <Md5FileValidUpload />
          </Form.Item>
        </div>
      </ProCard>

      <Form.Item
        wrapperCol={{ span: 24 }}
        style={{ textAlign: 'center', marginTop: 16 }}
      >
        <Button
          type="primary"
          htmlType="submit"
          style={{ marginRight: 16 }}
          loading={loading}
          disabled={loading}
        >
          提交
        </Button>
        <Button htmlType="reset" style={{ marginRight: 16 }} danger>
          重置
        </Button>
        <Button type="default" onClick={history.back}>
          取消
        </Button>
      </Form.Item>
    </Form>
  );
};

export default Edit;
