import { Md5FileResult } from '@/components/Md5FileValidUpload/interface';
import { LocaleObject } from '@/models/common.interface';
import { UploadFile } from 'antd';

export interface DressUpForm {
  names: LocaleObject[];
  userIds: string;
  priority: number | null;
  isSubscribe: boolean;
  thumbnailPics: UploadFile[];
  leftLivePics: UploadFile[];
  rightLivePics: UploadFile[];
  backgroundPics: UploadFile[];
  fileValidUpload: Md5FileResult;
  fileValidUpload2: Md5FileResult;
}
