import { AttireDetail, AttireParam } from '@/models/attire/interface';
import { LocaleObject } from '@/models/common.interface';
import { uuid } from '@/utils/uuid';
import { DressUpForm } from './interface';

export const initialDressUpForm: DressUpForm = {
  names: [
    {
      zh_CN: '',
    },
    {
      en_US: '',
    },
  ],
  userIds: '',
  priority: null,
  isSubscribe: false,
  thumbnailPics: [],
  leftLivePics: [],
  rightLivePics: [],
  backgroundPics: [],
  fileValidUpload: {
    md5: '',
    file: [],
  },
  fileValidUpload2: {
    md5: '',
    file: [],
  },
};

// 将formData转换为param
export const transferFormDataToParam = (
  formData: DressUpForm,
  id?: number,
): AttireParam => {
  const param: AttireParam = {
    localeName: '',
    userIds: formData.userIds,
    priority: formData.priority || 0,
    subscribe: formData.isSubscribe,
    thumbnail: formData.thumbnailPics[0]?.url || '',
    leftLivePic: formData.leftLivePics[0]?.url || '',
    rightLivePic: formData.rightLivePics[0]?.url || '',
    backgroundPic: formData.backgroundPics[0]?.url || '',
    binFile: (formData.fileValidUpload.file[0]?.url || '').replace(
      'https://',
      'http://',
    ),
    binEncrypt: formData.fileValidUpload.md5,
    binFile2: (formData.fileValidUpload2.file[0]?.url || '').replace(
      'https://',
      'http://',
    ),
    binEncrypt2: formData.fileValidUpload2.md5,
  };
  if (id) param.id = id;

  const localeName: LocaleObject = {};
  formData.names.forEach((nameInfo) => {
    const key = Object.keys(nameInfo)[0];
    const value = nameInfo[key];
    localeName[key] = value;
  });
  param.localeName = JSON.stringify(localeName);

  return param;
};

// 将接口返回的详情数据转换为formData
export const transferDetailToFormData = (detail: AttireDetail): DressUpForm => {
  const localeName = JSON.parse(detail.localeName);
  const formData: DressUpForm = {
    names: [
      {
        zh_CN: localeName['zh_CN'] || '',
      },
      {
        en_US: localeName['en_US'] || '',
      },
    ],
    userIds: detail.userIds,
    priority: detail.priority,
    isSubscribe: detail.subscribe,
    thumbnailPics: detail.thumbnail
      ? [
          {
            uid: uuid(),
            name: '装扮缩略图',
            url: detail.thumbnail,
          },
        ]
      : [],
    leftLivePics: detail.leftLivePic
      ? [
          {
            uid: uuid(),
            name: '左图',
            url: detail.leftLivePic,
          },
        ]
      : [],
    rightLivePics: detail.rightLivePic
      ? [
          {
            uid: uuid(),
            name: '右图',
            url: detail.rightLivePic,
          },
        ]
      : [],
    fileValidUpload: {
      md5: detail.binEncrypt || '',
      file: detail.binFile
        ? [
            {
              uid: uuid(),
              name: detail.binFile,
              url: detail.binFile,
            },
          ]
        : [],
    },
    fileValidUpload2: {
      md5: detail.binEncrypt2 || '',
      file: detail.binFile2
        ? [
            {
              uid: uuid(),
              name: detail.binFile2,
              url: detail.binFile2,
            },
          ]
        : [],
    },
    backgroundPics: detail.backgroundPic
      ? [
          {
            uid: uuid(),
            name: '装扮缩略图',
            url: detail.backgroundPic,
          },
        ]
      : [],
  };
  return formData;
};
