import Uploader from '@/components/Uploader';
import { fetchAttireDelete, fetchAttireExcelImport, fetchAttireList, fetchAttireStatusUpdate } from '@/models/attire/fetch';
import { Attire, AttireListParam } from '@/models/attire/interface';
import { initAttireListParam } from '@/models/attire/util';
import { ApiSuccessEnum, StatusEnum } from '@/models/common.interface';
import { attireApiOption } from '@/services/api/attire';
import { UploadTokenTypeEnum } from '@/services/qiniuOss/interface';
import { getCurrentPrefix } from '@/utils/currentPrefix';
import { Pagination, initPagination } from '@/utils/request';
import { PlusOutlined } from '@ant-design/icons';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { Button, Image, Modal, Popconfirm, Space, Switch, UploadFile, message } from 'antd';
import { UploadChangeParam } from 'antd/es/upload';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';

const List: React.FC = () => {
  const [dataList, setDataList] = useState<Attire[]>([]);
  const [listParam, setListParam] =
    useState<AttireListParam>(initAttireListParam);
  const [pagination, setPagination] = useState<Pagination>(initPagination);
  const [showUpload, setShowUpload] = useState(false);
  const [tempUploadFiles, setTempUploadFiles] = useState<UploadFile[]>([]);

  // 获取列表数据
  const requestDressUpList = async (
    param: AttireListParam = initAttireListParam,
  ) => {
    const { items, ...rest } = await fetchAttireList(param);
    setPagination(rest);
    setDataList(items);
  };

  const previewImageList = (
    record: Attire,
    key: keyof Attire,
    defaultValue?: string,
  ) => {
    return record[key] ? (
      <Image.PreviewGroup
        items={[
          record.thumbnail,
          record.leftLivePic,
          record.rightLivePic,
          record.backgroundPic,
        ]}
      >
        <Image width={100} src={`${record[key]}`} />
      </Image.PreviewGroup>
    ) : (
      defaultValue || '-'
    );
  };

  // 编辑
  const editDressUp = (id: number) => {
    history.push(`/dress-up/edit/${id}`);
  };

  // 删除
  const deleteDressUp = async (id: number) => {
    try {
      const result = await fetchAttireDelete(id);
      if (result === ApiSuccessEnum.success) {
        message.success('删除成功');
        setListParam({ ...listParam });
      }
    } catch (error) {
      console.log(error);
    }
  };

  // 切换状态
  const switchStatus = async (ev: boolean, id: number) => {
    console.log('switchStatus', ev, +!ev, id);
    try {
      const result = await fetchAttireStatusUpdate(
        id,
        +ev as StatusEnum,
      );
      if (result === ApiSuccessEnum.success) {
        message.success(`${ev ? '上架' : '下架'}成功!`);
        setListParam({ ...listParam });
      }
    } catch (error) {
      console.log(error);
    }
  };

  const exportStrings = () => {
    window.open(
      `${location.origin}${getCurrentPrefix()}${
        attireApiOption.attireExcelExport.url
      }?X-Admin-Session=${localStorage.getItem('sessionToken')}`,
    );
  };

  const importStrings = () => {
    setShowUpload(true);
  };

  const onUploaderChange = (info: UploadChangeParam<UploadFile<any>>) => {
    const file = { ...info.file };
    file.percent = 100;
    file.status = 'done';
    setTempUploadFiles([file]);
  };

  const cancelUpload = () => {
    setTempUploadFiles([]);
    setShowUpload(false);
  };

  const confirmUpload = async () => {
    if (
      !tempUploadFiles ||
      !tempUploadFiles.length ||
      !tempUploadFiles[0].originFileObj
    ) {
      message.warning('请先选择要导入的文件');
      return;
    }
    const result = await fetchAttireExcelImport({
      file: tempUploadFiles[0].originFileObj,
    });
    if (result === ApiSuccessEnum.success) {
      message.success('导入成功');
      setListParam({ ...initAttireListParam });
      cancelUpload();
    } else {
      message.warning('导入出错，请重新导入');
      setTempUploadFiles([]);
    }
  };

  // Search的表单按钮渲染
  // const searchOptionRender = (
  //   searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  // ) => {
  //   const { form } = searchConfig;
  //   return [
  //     <Button key="search" type="primary" onClick={() => {}}>
  //       查询
  //     </Button>,
  //     <Button
  //       key="reset"
  //       type="default"
  //       onClick={() => {
  //         form?.resetFields();
  //         setListParam(initDressUpListParam);
  //       }}
  //     >
  //       重置
  //     </Button>,
  //   ];
  // };

  const onPaginationChanged = (page: number, pageSize: number) => {
    let index = page;
    if (pageSize !== listParam.pageSize) {
      index = 1;
    }
    setListParam({
      ...listParam,
      pageSize,
      pageIndex: index,
    });
  };

  const columns: Array<ProColumns<Attire>> = [
    {
      title: '装扮名称',
      dataIndex: 'name',
      width: 100,
    },
    {
      title: '是否开通服务使用',
      dataIndex: 'subscribe',
      width: 80,
      render: (_, record) => (record.subscribe ? '是' : '否'),
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      width: 80,
    },
    {
      title: '装扮缩略图',
      dataIndex: 'thumbnail',
      width: 100,
      render: (_, record) => previewImageList(record, 'thumbnail'),
    },
    {
      title: '首页直播装扮',
      dataIndex: 'livePic',
      width: 220,
      render: (_, record) =>
        record.leftLivePic && record.rightLivePic ? (
          <Space>
            {previewImageList(record, 'leftLivePic', '')}
            {previewImageList(record, 'rightLivePic', '')}
          </Space>
        ) : (
          '-'
        ),
    },
    {
      title: '首页背景',
      dataIndex: 'backgroundPic',
      width: 100,
      render: (_, record) => previewImageList(record, 'backgroundPic'),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 150,
      render: (_, record) =>
        dayjs(record.createdAt * 1000).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 80,
      render: (_, record) => <Switch
          checked={!!record.status}
          checkedChildren="上架"
          unCheckedChildren="下架"
          onChange={(ev) => switchStatus(ev, record.id)}
        />,
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      width: 130,
      render: (_, record) => (
        <Space>
          <Button type="link" onClick={() => editDressUp(record.id)}>
            编辑
          </Button>
          {!record.status ? <Popconfirm title="确定要删除该装扮么？" onConfirm={() => deleteDressUp(record.id)}>
            <Button type="link">
              删除
            </Button>
          </Popconfirm> : null}
        </Space>
      ),
    },
  ];

  useEffect(() => {
    requestDressUpList(listParam);
  }, [listParam]);

  return (
    <>
      <ProTable<Attire>
        dataSource={dataList}
        columns={columns}
        defaultSize="small"
        rowKey="id"
        search={false}
        scroll={{
          x: columns
            .filter((col) => col.dataIndex !== 'action')
            .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
        }}
        options={{
          reload: () => {
            if (listParam) {
              setListParam({ ...listParam });
            }
          },
        }}
        toolbar={{
          actions: [
            <Button
              key="button"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => editDressUp(0)}
            >
              新增
            </Button>,
            <Button key="export" onClick={exportStrings}>
              导出字串表
            </Button>,
            <Button key="import" onClick={importStrings}>
              导入字串表
            </Button>,
          ],
        }}
        pagination={{
          pageSize: pagination.limit,
          total: pagination.total,
          showQuickJumper: true,
          onChange: onPaginationChanged,
        }}
      />
      {showUpload ? (
        <Modal open footer={null} onCancel={cancelUpload}>
          <Uploader
            accept=".xlsx, .xls"
            uploadButtonText="上传文件"
            maxCount={1}
            type={UploadTokenTypeEnum.FILE}
            customRequest={() => {}}
            fileList={tempUploadFiles}
            onChange={onUploaderChange}
          />
          <br />
          <Space>
            <Button type="primary" onClick={confirmUpload}>
              确定
            </Button>
            <Button onClick={cancelUpload}>取消</Button>
          </Space>
        </Modal>
      ) : null}
    </>
  );
};

export default List;
