import { AwardDurationUnitEnum } from '@/models/aiCreation/interface';
import { DeviceTypeEnum } from '@/models/common.interface';
import {
  MaterialsAwardAwardParam,
  MaterialsAwardDetail,
  MaterialsAwardListParam,
  MaterialsAwardParam,
} from '@/models/matter/interface';
import { initMaterialsAwardListParam } from '@/models/matter/util';
import { ProductSku } from '@/models/product/interface';
import dayjs from 'dayjs';
import {
  AwardForm,
  AwardTableForm,
  AwardTimeEnum,
  UrlParam,
} from './interface';

export const transferUrlParamToTableFormData = (urlParam: UrlParam) => {
  const formData: AwardTableForm = {};
  if (urlParam.deviceType) {
    formData.deviceType = urlParam.deviceType;
  }
  if (urlParam.disable) {
    formData.disable = urlParam.disable;
  }
  if (urlParam.startTime && urlParam.endTime) {
    if (+urlParam.startTime === -1 && +urlParam.endTime === -1) {
      formData.timeType = '-1';
    } else {
      formData.timeType = '1';
      formData.startTime = [
        dayjs(+urlParam.startTime),
        dayjs(+urlParam.endTime),
      ];
    }
  }
  if (urlParam.createStartTime && urlParam.createEndTime) {
    formData.createTime = [
      dayjs(+urlParam.createStartTime),
      dayjs(+urlParam.createEndTime),
    ];
  }
  return formData;
};

export const transferUrlParamToListParam = (urlParam: UrlParam) => {
  const param: MaterialsAwardListParam = {
    ...initMaterialsAwardListParam,
    deviceType: urlParam.deviceType,
    disable: urlParam.disable,
  };

  if (urlParam.id) {
    param.awardId = +urlParam.id;
  }

  if (urlParam.startTime && urlParam.endTime) {
    param.startTime = +urlParam.startTime;
    param.endTime = +urlParam.endTime;
  }

  if (urlParam.createStartTime && urlParam.createEndTime) {
    param.createStartTime = +urlParam.createStartTime;
    param.createEndTime = +urlParam.createEndTime;
  }
  return param;
};

export const initialAwardForm: AwardForm = {
  deviceType: null,
  awardTimeType: AwardTimeEnum.CUSTOM,
  awardTimes: [],
  threshold: null,
  rule: {},
  skuId: null,
  skuName: {},
  duration: null,
  unit: AwardDurationUnitEnum.MONTH,
};

// 将formData转换为param
export const transferFormDataToParam = (
  formData: AwardForm,
  associateSkuList: ProductSku[],
  detail?: MaterialsAwardDetail,
): MaterialsAwardParam => {
  const award: MaterialsAwardAwardParam = {
    planId: associateSkuList[0].id,
    planName: JSON.stringify(formData.skuName),
    duration: formData.duration || 0,
    unit: formData.unit,
  };
  const param: MaterialsAwardParam = {
    deviceType: formData.deviceType || DeviceTypeEnum.D4sh,
    startTime:
      formData.awardTimeType === AwardTimeEnum.FOREVER
        ? -1
        : formData.awardTimes[0].startOf('d').valueOf(),
    endTime:
      formData.awardTimeType === AwardTimeEnum.FOREVER
        ? -1
        : formData.awardTimes[1].endOf('d').valueOf(),
    threshold: formData.threshold || 0,
    rule: JSON.stringify(formData.rule),
    award: '',
  };
  if (detail) {
    param.id = detail.id;
    award.bsAwardId = detail.award.bsAwardId;
  }
  param.award = JSON.stringify(award);
  return param;
};

// 将接口返回的详情数据转换为formData
export const transferDetailToFormData = (
  detail: MaterialsAwardDetail,
): AwardForm => {
  const formData: AwardForm = {
    deviceType: detail.deviceType,
    awardTimeType:
      detail.startTime === -1 || detail.endTime === -1
        ? AwardTimeEnum.FOREVER
        : AwardTimeEnum.CUSTOM,
    awardTimes:
      detail.startTime !== -1 && detail.endTime !== -1
        ? [
            dayjs(detail.startTime).startOf('d'),
            dayjs(detail.endTime).endOf('d'),
          ]
        : [],
    threshold: detail.threshold,
    rule: JSON.parse(detail.rule || '{}'),
    skuId: detail.award.planId,
    skuName: JSON.parse(detail.award.planName || '{}'),
    duration: detail.award.duration,
    unit: detail.award.unit,
  };
  return formData;
};
