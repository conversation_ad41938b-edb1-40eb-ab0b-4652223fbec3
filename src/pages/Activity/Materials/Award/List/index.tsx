import {
  ApiSuccessEnum,
  DeviceTypeEnum,
  StatusEnum,
} from '@/models/common.interface';
import { postMessageFunction, spanConfig } from '@/models/common.util';
import {
  fetchMaterialsAwardList,
  fetchMaterialsAwardStatusUpdate,
} from '@/models/matter/fetch';
import {
  MaterialsAward,
  MaterialsAwardListParam,
} from '@/models/matter/interface';
import { initMaterialsAwardListParam } from '@/models/matter/util';
import { Pagination, initPagination } from '@/utils/request';
import useUrlState from '@ahooksjs/use-url-state';
import { PlusOutlined } from '@ant-design/icons';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { Button, Space, Switch, message } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useRef, useState } from 'react';
import { AwardTableForm, UrlParam } from '../interface';
import {
  transferUrlParamToListParam,
  transferUrlParamToTableFormData,
} from '../util';

const List: React.FC = () => {
  const [urlParam] = useUrlState<UrlParam>();
  const [dataList, setDataList] = useState<MaterialsAward[]>([]);
  const [listParam, setListParam] = useState<MaterialsAwardListParam>(
    initMaterialsAwardListParam,
  );
  const [pagination, setPagination] = useState<Pagination>(initPagination);
  const formRef = useRef<ProFormInstance<AwardTableForm>>();

  // 获取列表数据
  const requestAwardList = async (
    param: MaterialsAwardListParam = initMaterialsAwardListParam,
  ) => {
    const { items, ...rest } = await fetchMaterialsAwardList(param);
    setPagination(rest);
    setDataList(items);
  };

  // 切换状态
  const switchStatus = async (ev: boolean, id: number) => {
    console.log('switchStatus', ev, +!ev, id);
    try {
      const result = await fetchMaterialsAwardStatusUpdate(
        id,
        +ev as StatusEnum,
      );
      if (result === ApiSuccessEnum.success) {
        message.success(`${ev ? '上架' : '下架'}成功!`);
        setListParam({ ...initMaterialsAwardListParam, ...listParam });
      }
    } catch (error) {
      console.log(error);
    }
  };

  // 编辑
  const editAward = (id: number) => {
    history.push(`/activity/materials/award/edit/${id}`);
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          const formData: AwardTableForm = form?.getFieldsValue();
          const param: UrlParam = {};
          if (formData.disable !== undefined) {
            param.disable = +formData.disable;
          }
          if (formData.deviceType) {
            param.deviceType = formData.deviceType;
          }
          if (formData.timeType && +formData.timeType === -1) {
            param.startTime = -1;
            param.endTime = -1;
          } else {
            if (formData.startTime && formData.startTime.length) {
              param.startTime = formData.startTime[0].valueOf();
              param.endTime = formData.startTime[1].valueOf();
            }
          }
          if (formData.createTime && formData.createTime.length) {
            param.createStartTime = formData.createTime[0].valueOf();
            param.createEndTime = formData.createTime[1].valueOf();
          }
          console.log(formData, param);
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: '/activity/content/award',
              param: param as Record<string, string>,
            },
          });
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          form?.resetFields();
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: '/activity/content/award',
            },
          });
        }}
      >
        重置
      </Button>,
    ];
  };

  const onPaginationChanged = (page: number, pageSize: number) => {
    let index = page;
    if (pageSize !== listParam.limit) {
      index = 1;
    }
    setListParam({
      ...listParam,
      limit: pageSize,
      offset: (index - 1) * pageSize,
    });
  };

  const columns: Array<ProColumns<MaterialsAward>> = [
    {
      title: '适用设备',
      dataIndex: 'deviceType',
      valueType: 'select',
      valueEnum: {
        [DeviceTypeEnum.D4sh]: 'D4sh',
        [DeviceTypeEnum.D4H]: 'D4h',
        [DeviceTypeEnum.T5]: 'T5',
        [DeviceTypeEnum.T6]: 'T6',
      },
    },
    {
      title: '时间类型',
      dataIndex: 'timeType',
      hideInTable: true,
      valueType: 'select',
      valueEnum: { [-1]: '永久', 1: '时间段' },
    },
    {
      title: '活动时间',
      dataIndex: 'startTime',
      valueType: 'dateRange',
      render: (_, record) => {
        if (record.startTime === -1 || record.endTime === -1) {
          return '永久';
        }
        return `${dayjs(record.startTime)
          .startOf('d')
          .format('YYYY-MM-DD HH:mm:ss')} - ${dayjs(record.endTime)
          .endOf('d')
          .format('YYYY-MM-DD HH:mm:ss')}`;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      valueType: 'dateRange',
      render: (_, record) => {
        return dayjs(record.createTime).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '状态',
      dataIndex: 'disable',
      valueEnum: {
        [StatusEnum.ENABLE]: '上架',
        [StatusEnum.DISABLE]: '下架',
      },
      render: (_, record) => (
        <Switch
          checked={!!record.disable}
          checkedChildren="上架"
          unCheckedChildren="下架"
          onChange={(ev) => switchStatus(ev, record.id)}
        />
      ),
    },
    {
      title: '操作',
      dataIndex: 'action',
      search: false,
      render: (_, record) => (
        <Space>
          {record.disable === StatusEnum.DISABLE ? (
            <Button type="link" onClick={() => editAward(record.id)}>
              编辑
            </Button>
          ) : null}
        </Space>
      ),
    },
  ];

  useEffect(() => {
    const form = formRef.current;
    const formData = transferUrlParamToTableFormData(urlParam);
    form?.setFieldsValue(formData);
    const _listParam = transferUrlParamToListParam(urlParam);
    setListParam(_listParam);
  }, [urlParam]);

  useEffect(() => {
    requestAwardList(listParam);
  }, [listParam]);

  return (
    <ProTable<MaterialsAward>
      dataSource={dataList}
      columns={columns}
      defaultSize="small"
      rowKey="id"
      formRef={formRef}
      search={{
        defaultCollapsed: false,
        span: spanConfig,
        optionRender: searchOptionRender,
      }}
      scroll={{
        x: columns
          .filter((col) => col.dataIndex !== 'action')
          .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
      }}
      options={{ reload: () => setListParam({ ...listParam }) }}
      toolbar={{
        actions: [
          <Button
            key="button"
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => editAward(0)}
          >
            新增
          </Button>,
        ],
      }}
      pagination={{
        pageSize: pagination.limit,
        total: pagination.total,
        showQuickJumper: true,
        onChange: onPaginationChanged,
      }}
    />
  );
};

export default List;
