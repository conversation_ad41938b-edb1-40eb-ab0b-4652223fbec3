import { AwardDurationUnitEnum } from '@/models/aiCreation/interface';
import {
  DeviceTypeEnum,
  LocaleObject,
  StatusEnum,
} from '@/models/common.interface';
import { Dayjs } from 'dayjs';

export enum AwardTimeEnum {
  CUSTOM = 'CUSTOM',
  FOREVER = 'FOREVER',
}

export interface UrlParam {
  id?: number;
  deviceType?: DeviceTypeEnum;
  disable?: StatusEnum;
  startTime?: number;
  endTime?: number;
  createStartTime?: number;
  createEndTime?: number;
}

export interface AwardTableForm {
  deviceType?: DeviceTypeEnum;
  disable?: StatusEnum;
  startTime?: Dayjs[];
  createTime?: Dayjs[];
  timeType?: string;
}

export interface AwardForm {
  deviceType: DeviceTypeEnum | null;
  awardTimeType: AwardTimeEnum;
  awardTimes: Dayjs[];
  threshold: number | null;
  rule: LocaleObject;
  skuId: number | null;
  skuName: LocaleObject;
  duration: number | null;
  unit: AwardDurationUnitEnum;
}
