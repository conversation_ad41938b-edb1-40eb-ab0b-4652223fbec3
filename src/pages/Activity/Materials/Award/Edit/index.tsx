import LocaleInputGroup from '@/components/LocaleInputGroup';
import { LocaleInputList } from '@/components/LocaleInputGroup/interface';
import { validateLocaleInputGroup } from '@/components/LocaleInputGroup/util';
import SkuSelector from '@/components/SkuSelector';
import { associateSkuTableColumns } from '@/components/SkuSelector/util';
import { AwardDurationUnitEnum } from '@/models/aiCreation/interface';
import { ApiSuccessEnum } from '@/models/common.interface';
import {
  fetchMaterialsAwardCreation,
  fetchMaterialsAwardDetail,
  fetchMaterialsAwardUpdate,
} from '@/models/matter/fetch';
import { MaterialsAwardDetail } from '@/models/matter/interface';
import { fetchProductSkuDetail } from '@/models/product/fetch';
import {
  ProductSku,
  RelationProductSkuListParam,
  RelationProductSkuParam,
  SaleStatusEnum,
} from '@/models/product/interface';
import { initRelationProductSkuListParam } from '@/models/product/util';
import { suitableDeviceTypeOptions } from '@/pages/Activity/AiCollcaboartion/util';
import { initPaginatorParam } from '@/utils/request';
import { InfoCircleFilled, PlusOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { history, useDispatch, useParams } from '@umijs/max';
import {
  Button,
  Col,
  DatePicker,
  Form,
  InputNumber,
  InputProps,
  Radio,
  Row,
  Select,
  Table,
  message,
} from 'antd';
import { TextAreaProps } from 'antd/es/input';
import { ColumnsType } from 'antd/es/table';
import React, { useEffect, useMemo, useState } from 'react';
import { AwardForm, AwardTimeEnum } from '../interface';
import {
  initialAwardForm,
  transferDetailToFormData,
  transferFormDataToParam,
} from '../util';

const Edit: React.FC = () => {
  const dispatch = useDispatch();
  const param = useParams<{ id: string }>();
  const [form] = Form.useForm<AwardForm>();
  const [loading, setLoading] = useState(false);
  const [detail, setDetail] = useState<MaterialsAwardDetail>();
  const [showSkuSelector, setShowSkuSelector] = useState(false);
  const [associateSkuList, setAssociateSkuList] = useState<ProductSku[]>([]);
  const [associateSkuListParam, setAssociateSkuListParam] =
    useState<RelationProductSkuListParam>(initRelationProductSkuListParam);
  const layout = {
    labelCol: { span: 3 },
    wrapperCol: { span: 20 },
  };
  const awardTimeType = Form.useWatch('awardTimeType', form);

  const isEditing = useMemo(() => {
    return !!+(param.id || 0);
  }, [param.id]);

  const getRuleLocaleInputList = (
    _textareaProps?: TextAreaProps,
  ): LocaleInputList[] => [
    {
      locale: 'zh_CN',
      required: true,
      type: 'textarea',
      textareaProps: {
        ..._textareaProps,
        rows: 4,
        placeholder: '请输入活动规则',
      },
    },
    {
      locale: 'en_US',
      type: 'textarea',
      textareaProps: {
        ..._textareaProps,
        rows: 4,
        placeholder: '请输入活动规则',
      },
    },
  ];

  const getSkuNameLocaleInputList = (
    _inputProps?: InputProps,
  ): LocaleInputList[] => [
    {
      locale: 'zh_CN',
      required: true,
      inputProps: {
        ..._inputProps,
        placeholder: '请输入活动Plan名称',
      },
    },
    {
      locale: 'en_US',
      inputProps: {
        ..._inputProps,
        placeholder: '请输入活动Plan名称',
      },
    },
  ];

  useEffect(() => {
    // 额外dva接口请求
  }, [dispatch]);

  // 根据id获取详情数据
  const requestDetailById = async (id: number) => {
    const _detail = await fetchMaterialsAwardDetail(id);
    setDetail(_detail);

    const formData = transferDetailToFormData(_detail);
    form.setFieldsValue(formData);

    const relationDetail = await fetchProductSkuDetail(_detail.award.planId);
    setAssociateSkuList([relationDetail]);
  };

  const showProductSelector = () => {
    const formData = form.getFieldsValue();
    const { deviceType } = formData;
    const deviceInfo = suitableDeviceTypeOptions.find(
      (option) => option.value === deviceType,
    )?.label;

    if (!deviceType || !deviceInfo) {
      message.warning('请先选择适用设备');
      return;
    }
    const _param: RelationProductSkuParam = {
      // isReNew: ReNewEnum.NOT_RENEW,
      uniteDeviceTypes: [deviceInfo],
      uniteCapacities: [],
      saleStatus: SaleStatusEnum.ON_SALE,
    };
    setAssociateSkuListParam({
      ...initPaginatorParam,
      payload: _param,
    });
    setShowSkuSelector(true);
  };

  const addSkuInfo = (sku: ProductSku) => {
    // const skuName = sku.name;
    // form.setFieldValue('skuName', { zh_CN: skuName, en_US: skuName });
    setAssociateSkuList([sku]);
  };

  // 提交form表单
  const submit = async (formData: AwardForm) => {
    // setLoading(true);
    const _param = transferFormDataToParam(formData, associateSkuList, detail);
    let result = '';
    let state = '';
    console.log('submit', formData, associateSkuList, _param);
    try {
      if (param && +(param.id || 0)) {
        state = '更新';
        result = await fetchMaterialsAwardUpdate(_param);
      } else {
        state = '创建';
        result = await fetchMaterialsAwardCreation(_param);
      }

      if (result === ApiSuccessEnum.success) {
        message.success(`${state}成功！`);
        history.back();
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (param && param.id && +param.id) {
      requestDetailById(+param.id);
    }
  }, [param]);

  return (
    <ProCard title="活动奖励">
      <Form
        {...layout}
        form={form}
        onFinish={submit}
        initialValues={initialAwardForm}
      >
        <Form.Item
          name="deviceType"
          label="适用设备"
          rules={[{ required: true, message: '请选择适用设备' }]}
        >
          <Select
            placeholder="请选择适用设备"
            disabled={isEditing}
            options={suitableDeviceTypeOptions}
          />
        </Form.Item>
        <Form.Item
          name="awardTimeType"
          label="活动时间"
          rules={[{ required: true, message: '请选择活动的时间类型' }]}
          extra={
            awardTimeType === AwardTimeEnum.CUSTOM ? (
              <div style={{ marginTop: 16 }}>
                <Form.Item
                  name="awardTimes"
                  rules={[
                    {
                      required: true,
                      message: '请选择活动的开始结束时间',
                    },
                  ]}
                  style={{ marginBottom: 0 }}
                >
                  <DatePicker.RangePicker
                    placeholder={['开始时间', '结束时间']}
                  />
                </Form.Item>
              </div>
            ) : null
          }
        >
          <Radio.Group>
            <Radio value={AwardTimeEnum.CUSTOM}>自定义时间段</Radio>
            <Radio value={AwardTimeEnum.FOREVER}>永久有效</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item
          name="threshold"
          label="奖励门槛"
          rules={[{ required: true, message: '请输入奖励门槛' }]}
        >
          <InputNumber
            min={1}
            disabled={isEditing}
            placeholder="请输入提交多少次素材后，可以获得该奖励"
          />
        </Form.Item>
        <Form.Item
          name="rule"
          label="活动规则"
          rules={[
            { required: true },
            {
              validator: (_, value) =>
                validateLocaleInputGroup(
                  value,
                  getRuleLocaleInputList({
                    disabled: !!detail?.disable,
                  }),
                ),
            },
          ]}
        >
          <LocaleInputGroup
            localeInputList={getRuleLocaleInputList({
              disabled: !!detail?.disable,
            })}
          />
        </Form.Item>
        <Form.Item
          label={
            <>
              <span className="required-red" style={{ marginRight: 4 }}>
                *
              </span>
              活动Plan
            </>
          }
          rules={[
            {
              validator: async () => {
                if (!associateSkuList || !associateSkuList.length) {
                  return Promise.reject(new Error('请选择一个云存Plan'));
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          {!isEditing ? (
            <div>
              <Button
                icon={<PlusOutlined />}
                type="link"
                htmlType="button"
                onClick={showProductSelector}
                style={{ marginBottom: 16 }}
              >
                去选择Plan
              </Button>
              <br />
              <>
                <InfoCircleFilled
                  size={20}
                  style={{
                    color: '#518be3',
                    marginRight: 8,
                  }}
                />
                请选择能力最高的Plan
              </>
            </div>
          ) : null}
          {associateSkuList && associateSkuList.length ? (
            <Table<ProductSku>
              rowKey="id"
              columns={(
                associateSkuTableColumns as ColumnsType<ProductSku>
              ).concat([
                {
                  title: '状态',
                  dataIndex: 'saleStatus',
                  fixed: 'right',
                  width: 100,
                  render: (_, row) =>
                    row.saleStatus ? (
                      <p style={{ color: '#3199F5' }}>上架</p>
                    ) : (
                      '下架'
                    ),
                },
              ])}
              scroll={{
                x: associateSkuTableColumns
                  .filter((col) => col.dataIndex === 'action')
                  .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
              }}
              dataSource={associateSkuList}
              pagination={false}
            />
          ) : null}
        </Form.Item>
        <Form.Item
          label="活动Plan名称"
          name="skuName"
          rules={[
            { required: true },
            {
              validator: (_, value) =>
                validateLocaleInputGroup(
                  value,
                  getRuleLocaleInputList({
                    disabled: isEditing,
                  }),
                ),
            },
          ]}
        >
          <LocaleInputGroup localeInputList={getSkuNameLocaleInputList()} />
        </Form.Item>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="服务时长"
              name="duration"
              labelCol={{ span: 6 }}
              wrapperCol={{ span: 14 }}
              rules={[
                {
                  required: true,
                  message: '请输入服务时长(大于0的整数)',
                },
              ]}
            >
              <InputNumber
                min={1}
                disabled={isEditing}
                placeholder="请输入服务时长(大于0的整数)"
              />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              name="unit"
              rules={[{ required: true, message: '请选择时长单位' }]}
            >
              <Radio.Group disabled={isEditing}>
                <Radio
                  key={AwardDurationUnitEnum.MONTH}
                  value={AwardDurationUnitEnum.MONTH}
                >
                  月
                </Radio>
                <Radio
                  key={AwardDurationUnitEnum.DAY}
                  value={AwardDurationUnitEnum.DAY}
                >
                  天
                </Radio>
              </Radio.Group>
            </Form.Item>
          </Col>
        </Row>
        <Form.Item wrapperCol={{ span: 24 }} style={{ textAlign: 'center' }}>
          <Button
            type="primary"
            htmlType="submit"
            style={{ marginRight: 16 }}
            loading={loading}
            disabled={loading}
          >
            提交
          </Button>
          <Button type="default" onClick={history.back}>
            取消
          </Button>
        </Form.Item>
      </Form>
      {showSkuSelector ? (
        <SkuSelector
          param={associateSkuListParam}
          open
          onOk={addSkuInfo}
          onCancel={() => setShowSkuSelector(false)}
        />
      ) : null}
    </ProCard>
  );
};

export default Edit;
