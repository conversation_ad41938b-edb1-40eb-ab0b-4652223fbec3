import EncryptedImageViewer from '@/components/EncryptedImageViewer';
import RedirectLink from '@/components/RedirectLink';
import VideoPlayer, { VideoType } from '@/components/VideoPlayer';
import { DeviceTypeEnum } from '@/models/common.interface';
import { spanConfig } from '@/models/common.util';
import {
  fetchMaterialsRecordList,
  getM3u8FileUrl,
} from '@/models/matter/fetch';
import {
  MaterialsRecord,
  MaterialsRecordListParam,
  MaterialsRecordTypeEnum,
} from '@/models/matter/interface';
import { initMaterialsRecordListParam } from '@/models/matter/util';
import { Pagination, initPagination } from '@/utils/request';
import useUrlState from '@ahooksjs/use-url-state';
import { EyeOutlined, PlayCircleFilled } from '@ant-design/icons';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProTable,
} from '@ant-design/pro-components';
import { useParams } from '@umijs/max';
import { Button, Modal, message } from 'antd';
import { MD5 } from 'crypto-js';
import dayjs from 'dayjs';
import { omit } from 'lodash';
import React, { useEffect, useState } from 'react';
import { MaterialsRecordTableForm, RecordListUrlParam } from '../interface';

const RecordList: React.FC = () => {
  let hasDownloadM3u8Modal = false;
  const [urlParam] = useUrlState<RecordListUrlParam>();
  const urlRestParam = useParams<{ deviceType: string }>();
  const [dataList, setDataList] = useState<MaterialsRecord[]>([]);
  const [listParam, setListParam] = useState<MaterialsRecordListParam>();
  const [pagination, setPagination] = useState<Pagination>(initPagination);
  const [showM3u8ById, setShowM3u8ById] = useState<{ [key: string]: boolean }>(
    {},
  );
  const [paramError, setParamError] = useState<string>('');

  // 获取列表数据
  const requestRecordListList = async (param: MaterialsRecordListParam) => {
    const { items, ...rest } = await fetchMaterialsRecordList(param);
    setPagination(rest);
    setDataList(
      items.map((item) => ({
        ...item,
        picUrl: item.picUrl?.replace('http://', '//').replace('https://', '//'),
        aesKey: item.aesKey?.replace('\n', ''),
      })),
    );
  };

  // const exportRecordList = () => {
  //   const url = getMaterialsExportUrl();
  //   window.open(url);
  // };

  const playVideo = async (record: MaterialsRecord) => {
    if (!record.eventId) return;
    setShowM3u8ById({ [record.eventId]: true });
  };

  const closeM3u8VideoModal = (record: MaterialsRecord) => {
    if (!record.eventId) return;
    setShowM3u8ById({ [record.eventId]: false });
  };

  const downloadM3U8File = async (src: string) => {
    if (hasDownloadM3u8Modal) return;
    hasDownloadM3u8Modal = true;
    Modal.confirm({
      title: '视频播放失败',
      content: '当前视频无法播放，请下载后使用VLC进行播放',
      okText: '下载',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await fetch(src);
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          const content = await response.text();
          const filename = MD5(src).toString() + '.m3u8';

          const blob = new Blob([content], { type: 'application/x-mpegURL' });
          const link = document.createElement('a');
          link.href = URL.createObjectURL(blob);
          link.download = filename;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          URL.revokeObjectURL(link.href);

          message.success('文件下载成功');
        } catch (error) {
          console.error('下载失败:', error);
          message.error('文件下载失败');
        } finally {
          hasDownloadM3u8Modal = false;
        }
      },
      onCancel: () => {
        // 取消操作，不需要特殊处理
        hasDownloadM3u8Modal = false;
      },
    });
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          if (!listParam) return;

          const formData: MaterialsRecordTableForm = form?.getFieldsValue();
          const _listParam: MaterialsRecordListParam = {
            ...initMaterialsRecordListParam,
            ...omit(listParam, ['offset', 'limit']),
            userId: formData.userId || undefined,
            deviceId: formData.deviceId || undefined,
          };
          if (formData.publishTime) {
            _listParam.publishStart = formData.publishTime[0]
              .startOf('d')
              .valueOf();
            _listParam.publishEnd = formData.publishTime[1]
              .endOf('d')
              .valueOf();
          }

          setListParam({ ..._listParam });
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          if (!listParam) return;

          form?.resetFields();
          setListParam({
            ...initMaterialsRecordListParam,
            deviceType: listParam.deviceType,
            matterId: listParam.matterId,
          });
        }}
      >
        重置
      </Button>,
    ];
  };

  const onPaginationChanged = (page: number, pageSize: number) => {
    let index = page;
    if (pageSize !== listParam?.limit) {
      index = 1;
    }
    setListParam({
      ...listParam,
      limit: pageSize,
      offset: (index - 1) * pageSize,
      matterId: listParam?.matterId ?? 0, // Ensure matterId is always a number
    });
  };

  const columns: Array<ProColumns<MaterialsRecord>> = [
    {
      title: '用户ID',
      dataIndex: 'userId',
      render: (_, record) => (
        <RedirectLink
          text={record.userId}
          linkUrl={`/user/users`}
          params={{
            username: record.userId,
          }}
        />
      ),
    },
    { title: '设备ID', dataIndex: 'deviceId' },
    {
      title: '图片',
      dataIndex: 'images',
      search: false,
      render: (_, record) =>
        record.picUrl && record.aesKey ? (
          <>
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => playVideo(record)}
            >
              查看图片
            </Button>
            <Modal
              title="图片"
              open={record.eventId ? showM3u8ById[record.eventId] : false}
              onCancel={() => closeM3u8VideoModal(record)}
              footer={null}
              width={800}
              destroyOnHidden
            >
              <>
                <EncryptedImageViewer
                  images={[
                    {
                      url: record.picUrl,
                      aesKey: record.aesKey,
                    },
                  ]}
                  width={150}
                  height={150}
                />
              </>
            </Modal>
          </>
        ) : (
          '-'
        ),
    },
    {
      title: '视频',
      dataIndex: 'eventId',
      search: false,
      render: (_, record) => {
        const { eventId, combineKey, type } = record;
        if (!eventId || !combineKey || type === MaterialsRecordTypeEnum.Image)
          return '-';
        const m3u8Url = getM3u8FileUrl(
          { eventId, combineKey },
          DeviceTypeEnum[record.deviceType].toLowerCase(),
        );
        return (
          <>
            <Button
              type="link"
              icon={<PlayCircleFilled />}
              onClick={() => playVideo(record)}
            >
              观看视频
            </Button>
            <Modal
              title="视频"
              open={record.eventId ? showM3u8ById[record.eventId] : false}
              onCancel={() => closeM3u8VideoModal(record)}
              footer={null}
              width={800}
              destroyOnHidden
            >
              <>
                <VideoPlayer
                  videoType={VideoType.HLS}
                  height={800}
                  src={m3u8Url}
                  onError={() => {
                    closeM3u8VideoModal(record);
                    downloadM3U8File(m3u8Url);
                  }}
                />
              </>
            </Modal>
            {/* <Image
              width={200}
              style={{ display: 'none' }}
              src=""
              preview={{
                visible: record.eventId ? showM3u8ById[record.eventId] : false,
                destroyOnClose: true,
                mask: false,
                imageRender: () => {
                  const { eventId, combineKey } = record;
                  if (!eventId || !combineKey || !showM3u8ById) return null;
                  return (
                    <>
                      <VideoPlayer
                        videoType={VideoType.HLS}
                        width={1000}
                        height={800}
                        src={getM3u8FileUrl(
                          { eventId, combineKey },
                          DeviceTypeEnum[record.deviceType].toLowerCase(),
                        )}
                      />
                    </>
                  );
                },
                onVisibleChange: (value) => {
                  if (!record.eventId) return;
                  delete showM3u8ById[record.eventId];
                  setShowM3u8ById({ ...showM3u8ById });

                  // console.log('onVisibleChange', value);

                  // // 这里似乎是antd的bug，需要手动清除一下关闭遮罩后遗留的元素问题
                  // if (!value) {
                  //   try {
                  //     const wrapperList = document.querySelectorAll(
                  //       '.ant-image-preview-operations-wrapper',
                  //     );
                  //     wrapperList.forEach((node) => {
                  //       console.log('before remove - ', node, node.isConnected);
                  //       // 使用更现代的remove方法，避免parentNode操作
                  //       try {
                  //         if (node && node.isConnected) {
                  //           node.remove();
                  //         }
                  //       } catch (removeError) {
                  //         // 静默处理单个节点移除失败的情况
                  //         console.warn('单个节点移除失败:', removeError);
                  //       }
                  //     });
                  //   } catch (error) {
                  //     console.warn('DOM清理错误:', error);
                  //   }
                  // }
                },
              }}
            /> */}
          </>
        );
      },
    },
    {
      title: '提交时间',
      dataIndex: 'publishTime',
      valueType: 'dateRange',
      render: (_, record) =>
        dayjs(record.publishTime).format('YYYY-MM-DD HH:mm:ss'),
    },
  ];

  // URL参数验证和初始化
  useEffect(() => {
    setParamError('');

    const deviceType = urlRestParam.deviceType as unknown as DeviceTypeEnum;
    const matterId = +(urlParam.matterId || 0);

    if (matterId <= 0) {
      setParamError('事件ID参数无效');
      return;
    }

    setListParam({
      ...initMaterialsRecordListParam,
      deviceType,
      matterId,
    });
  }, [urlParam, urlRestParam]);

  useEffect(() => {
    if (!listParam || !listParam.deviceType) return;
    setDataList([]);
    setPagination(initPagination);
    requestRecordListList(listParam);
  }, [listParam]);

  if (paramError) {
    return (
      <div style={{ padding: '20px', textAlign: 'center' }}>
        <p style={{ color: 'red', fontSize: '16px' }}>参数错误：{paramError}</p>
        <p>请检查URL参数是否正确，需要包含deviceType和matterId参数</p>
      </div>
    );
  }

  return (
    <ProTable<MaterialsRecord>
      dataSource={dataList}
      columns={columns}
      defaultSize="small"
      rowKey="publishTime"
      search={{
        defaultCollapsed: false,
        span: spanConfig,
        optionRender: searchOptionRender,
      }}
      scroll={{
        x: columns
          .filter((col) => col.dataIndex !== 'action')
          .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
      }}
      pagination={{
        pageSize: pagination.limit,
        total: pagination.total,
        showQuickJumper: true,
        onChange: onPaginationChanged,
      }}
    />
  );
};

export default RecordList;
