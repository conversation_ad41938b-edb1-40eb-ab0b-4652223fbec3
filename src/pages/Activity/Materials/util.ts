import { MaterialsDetail, MaterialsParam } from '@/models/matter/interface';
import { MaterialsForm, MaterialsLabelForm } from './interface';

export const initialMaterialsForm: MaterialsForm = {
  labelList: [
    {
      name: { zh_CN: '', en_US: '' },
      tagList: [
        {
          tag: { zh_CN: '', en_US: '' },
        },
      ],
    },
  ],
};

export const initMaterialsLabelForm: MaterialsLabelForm = {
  name: {},
  tagList: [{ tag: {} }, { tag: {} }, { tag: {} }, { tag: {} }],
};

// 将formData转换为param
export const transferFormDataToParam = (
  formData: MaterialsForm,
  id: number,
): MaterialsParam => {
  const param: MaterialsParam = {
    id,
    info: formData.labelList.map((label) => ({
      title: JSON.stringify({
        zh_CN: label.name.zh_CN || '',
        en_US: label.name.en_US || '',
      }),
      tagList: label.tagList.map((tag) => ({
        tagName: JSON.stringify({
          zh_CN: tag.tag.zh_CN || '',
          en_US: tag.tag.en_US || '',
        }),
      })),
    })),
  };
  return param;
};

// 将接口返回的详情数据转换为formData
export const transferDetailToFormData = (
  detail: MaterialsDetail,
): MaterialsForm => {
  // 只取第一个标题的数据，因为现在只支持一个固定标题
  if (!detail.info || detail.info.length === 0) {
    return initialMaterialsForm;
  }
  
  const firstItem = detail.info[0];
  const formData: MaterialsForm = {
    labelList: [
      {
        name: JSON.parse(firstItem.title),
        tagList: firstItem.tagList.map((tag) => ({ tag: JSON.parse(tag.tagName) })),
      },
    ],
  };
  console.log(formData);
  return formData;
};
