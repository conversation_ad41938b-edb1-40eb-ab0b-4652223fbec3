import { BreadcrumbInfo } from '@/models/common.interface';
import global from '@/utils/global';
import { LeftOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { Outlet, useLocation } from '@umijs/max';
import React, { useEffect, useState } from 'react';

const breadcrumbInfo: BreadcrumbInfo = {
  path: '',
  breadcrumbName: '',
};

const Activity: React.FC = () => {
  const location = useLocation();
  const [title, setTitle] = useState(`上传素材`);
  const [showBackIcon, setShowBackIcon] = useState(false);
  const [breadcrumbInfoList, setBreadcrumbInfoList] = useState([
    breadcrumbInfo,
  ]);
  useEffect(() => {
    let _title = title;

    if (location?.pathname.includes('award')) {
      _title = '活动奖励';
    } else {
      _title = '上传素材';
    }
    setTitle(_title);

    let _breadcrumbInfoList = global.getBreadcrumbInfo(
      { ...breadcrumbInfo, breadcrumbName: _title },
      '',
      location,
    );
    if (location.pathname.includes('record-list')) {
      _breadcrumbInfoList = _breadcrumbInfoList.concat(
        global.getBreadcrumbInfo({
          ...breadcrumbInfo,
          breadcrumbName: '详情列表',
        }),
      );
    }
    setBreadcrumbInfoList(_breadcrumbInfoList);
    console.log('useEffect', _breadcrumbInfoList);

    if (_breadcrumbInfoList.length > 2) {
      setShowBackIcon(true);
    } else {
      setShowBackIcon(false);
    }
  }, [location]);

  return (
    <PageContainer
      header={{
        backIcon: showBackIcon ? <LeftOutlined /> : '',
        onBack: () => history.back(),
        title,
        ghost: true,
        breadcrumb: {
          itemRender: (route) => <span>{route.title}</span>,
          routes: breadcrumbInfoList,
        },
      }}
    >
      <Outlet />
    </PageContainer>
  );
};

export default Activity;
