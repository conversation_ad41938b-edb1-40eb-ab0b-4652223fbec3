import { LocaleInputGroupValue } from '@/components/LocaleInputGroup/interface';
import { UrlParam } from '@/models/common.interface';
import { Dayjs } from 'dayjs';

export interface MaterialsLabelForm {
  name: LocaleInputGroupValue;
  tagList: { tag: LocaleInputGroupValue }[];
}
export interface MaterialsForm {
  labelList: MaterialsLabelForm[];
}

export interface MaterialsRecordTableForm {
  userId: number;
  deviceId: number;
  publishTime: Dayjs[];
}

export interface RecordListUrlParam extends UrlParam {
  matterId: string;
  userId?: string;
  deviceId?: string;
}
