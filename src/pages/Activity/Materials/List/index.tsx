import { ApiSuccessEnum, DeviceTypeEnum } from '@/models/common.interface';
import {
  fetchMaterialDisable,
  fetchMaterialsList,
} from '@/models/matter/fetch';
import { MaterialDisableParam, Materials } from '@/models/matter/interface';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { Button, Space, Switch, message } from 'antd';
import React, { useEffect, useState } from 'react';

const List: React.FC = () => {
  const [dataList, setDataList] = useState<Materials[]>([]);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // 获取列表数据
  const requestMaterialsList = async () => {
    const list = await fetchMaterialsList();
    setDataList(list);
  };

  // 编辑
  const editMaterials = (id: number, isEditing: boolean) => {
    history.push(`/activity/materials/edit/${id}?isEditing=${+isEditing}`);
  };

  const gotoRecordList = (deviceType: DeviceTypeEnum, matterId: number) => {
    const url = `/activity/materials/record-list/${deviceType}?matterId=${matterId}`;
    history.push(url);
  };

  const switchStatus = async (ev: boolean, id: number) => {
    const param: MaterialDisableParam = {
      id,
      disable: +ev,
    };
    try {
      const result = await fetchMaterialDisable(param);
      if (result === ApiSuccessEnum.success) {
        message.success(`${ev ? '上架' : '下架'}成功`);
        requestMaterialsList();
      }
    } catch (error) {
      console.log(error);
    }
  };

  const handlePaginationChange = (page: number, size: number) => {
    setCurrent(page);
    setPageSize(size);
  };

  const columns: Array<ProColumns<Materials>> = [
    {
      title: '适用设备',
      dataIndex: 'deviceType',
      render: (_, record) => DeviceTypeEnum[record.deviceType],
    },
    {
      title: '收集的素材',
      dataIndex: 'name',
    },
    // {
    //   title: '参与人数',
    //   dataIndex: 'paticipateCount',
    //   render: (_, record) =>
    //     record.participateCount === -1 ? '--' : record.participateCount,
    // },
    // {
    //   title: '领取人数',
    //   dataIndex: 'reciveCount',
    //   render: (_, record) =>
    //     record.reciveCount === -1 ? '--' : record.reciveCount,
    // },
    {
      title: '视频数',
      dataIndex: 'videoCount',
      render: (_, record) =>
        record.videoCount === -1 ? '--' : record.videoCount,
    },
    // {
    //   title: '是否关联奖励',
    //   dataIndex: 'awardId',
    //   // render: (_, record) => (record.awardId > 0 ? record.awardId : '--'),
    //   render: (_, record) =>
    //     record.awardId > 0 ? (
    //       <RedirectLink
    //         linkUrl="/activity/content/award"
    //         params={{ id: record.awardId }}
    //         text="是"
    //       />
    //     ) : (
    //       '--'
    //     ),
    // },
    {
      title: '状态',
      dataIndex: 'disable',
      render: (_, record) => (
        <Switch
          checked={!!record.disable}
          checkedChildren="上架"
          unCheckedChildren="下架"
          onChange={(ev) => switchStatus(ev, record.id)}
        />
      ),
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <Space split="|">
          {!!record.disable ? (
            <Button type="link" onClick={() => editMaterials(record.id, false)}>
              查看
            </Button>
          ) : (
            <Button type="link" onClick={() => editMaterials(record.id, true)}>
              编辑
            </Button>
          )}
          {record.videoCount > 0 ? (
            <Button
              type="link"
              onClick={() => gotoRecordList(record.deviceType, record.matterId)}
            >
              详情
            </Button>
          ) : null}
        </Space>
      ),
    },
  ];

  useEffect(() => {
    requestMaterialsList();
  }, []);

  return (
    <>
      <ProTable<Materials>
        dataSource={dataList}
        columns={columns}
        defaultSize="small"
        rowKey="id"
        search={false}
        scroll={{
          x: columns
            .filter((col) => col.dataIndex !== 'action')
            .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
        }}
        pagination={{
          current,
          pageSize,
          showSizeChanger: true,
          pageSizeOptions: ['10', '20', '50', '100'],
          showQuickJumper: true,
          showTotal: (total, range) =>
            `共 ${total} 条数据，第 ${range[0]}-${range[1]} 条`,
          onChange: handlePaginationChange,
          onShowSizeChange: handlePaginationChange,
        }}
      />
    </>
  );
};

export default List;
