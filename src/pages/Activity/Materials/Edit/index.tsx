import LocaleInputGroup from '@/components/LocaleInputGroup';
import { LocaleInputList } from '@/components/LocaleInputGroup/interface';
import { ApiSuccessEnum } from '@/models/common.interface';
import {
  fetchMaterialsDetail,
  fetchMaterialsEdit,
} from '@/models/matter/fetch';
import { arabicToChinese } from '@/utils/arabicToChinese';
import useUrlState from '@ahooksjs/use-url-state';
import { DeleteOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { history, useDispatch, useParams } from '@umijs/max';
import {
  Button,
  Col,
  Form,
  FormListOperation,
  InputNumber,
  InputProps,
  Modal,
  Row,
  message,
} from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import { MaterialsForm } from '../interface';
import {
  initialMaterialsForm,
  transferDetailToFormData,
  transferFormDataToParam,
} from '../util';

const Edit: React.FC = () => {
  const [urlParam] = useUrlState<{ isEditing: number }>();
  const dispatch = useDispatch();
  const param = useParams<{ id: string }>();
  const [form] = Form.useForm<MaterialsForm>();
  const [loading, setLoading] = useState(false);
  const [isFormChanged, setIsFormChanged] = useState(false);
  const layout = {
    labelCol: { span: 3 },
    wrapperCol: { span: 20 },
  };

  const isEditing = useMemo(() => {
    const _isEditing = +urlParam?.isEditing;
    return !!+_isEditing;
  }, [urlParam?.isEditing]);

  // 普通多语言输入框配置
  const inputProps: InputProps = {
    placeholder: '请输入标题',
  };

  const labelInputPropList: LocaleInputList[] = [
    { locale: 'zh_CN', inputProps, required: true },
    { locale: 'en_US', inputProps, required: false },
  ];

  const tagInputPropList: LocaleInputList[] = [
    {
      locale: 'zh_CN',
      inputProps: { ...inputProps, placeholder: '请输入标签' },
      required: true,
    },
    {
      locale: 'en_US',
      inputProps: { ...inputProps, placeholder: '请输入标签' },
      required: false,
    },
  ];

  // 根据id获取详情数据
  const requestDetailById = async (id: number) => {
    let detail = await fetchMaterialsDetail(id);
    // detail = {
    //   id: detail.id,
    //   info: [
    //     {
    //       title: '{"en_US":"222","zh_CN":"111"}',
    //       tagList: [
    //         {
    //           tagName: '{"en_US":"444","zh_CN":"333"}',
    //         },
    //         {
    //           tagName: '{"en_US":"666","zh_CN":"555"}',
    //         },
    //       ],
    //     },
    //     {
    //       title: '{"en_US":"777","zh_CN":"777"}',
    //       tagList: [
    //         {
    //           tagName: '{"en_US":"888","zh_CN":"888"}',
    //         },
    //         {
    //           tagName: '{"en_US":"999","zh_CN":"999"}',
    //         },
    //       ],
    //     },
    //   ],
    // };
    const formData = transferDetailToFormData(detail);
    console.log('formData', formData);
    form.setFieldsValue(formData);
  };

  const addTagInfo = (add: FormListOperation['add'], index: number) => {
    const newTag = {
      tag: {},
    };
    add(newTag, index);
  };

  // 处理取消按钮的确认逻辑
  const handleCancel = () => {
    if (isFormChanged) {
      Modal.confirm({
        title: '确认取消',
        content: '您有未保存的修改，确定要取消吗？',
        onOk: () => {
          history.back();
        },
        onCancel: () => {},
      });
    } else {
      history.back();
    }
  };

  // 提交form表单
  const submit = async (formData: MaterialsForm) => {
    // 表单验证
    try {
      await form.validateFields();
    } catch (errorInfo) {
      message.error('请完善必填项后再提交');
      return;
    }

    setLoading(true);
    const _param = transferFormDataToParam(formData, +(param?.id || 0));
    console.log('submit', formData, _param);
    // return;
    let result = '';
    let state = '';
    try {
      if (param && param.id && +param.id) {
        state = '更新';
        result = await fetchMaterialsEdit(_param);
      } else {
        return;
      }

      if (result === ApiSuccessEnum.success) {
        message.success(`上传素材配置${state}成功！`);
        history.back();
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // 额外dva接口请求
  }, [dispatch]);

  useEffect(() => {
    if (param && param.id && +param.id) {
      requestDetailById(+param.id);
    }
  }, [param]);

  return (
    <Form
      {...layout}
      form={form}
      onFinish={submit}
      initialValues={initialMaterialsForm}
      onValuesChange={() => setIsFormChanged(true)}
    >
      <Form.Item style={{ display: 'none' }}>
        <InputNumber name="id" />
      </Form.Item>
      <ProCard title="基础设置" style={{ marginBottom: 16 }}>
        {/* 固定标题字段 */}
        <Form.Item
          name={['labelList', 0, 'name']}
          label="标题"
          rules={[
            {
              validator: async (_, value) => {
                if (!value || !value.zh_CN) {
                  throw new Error('请输入中文标题');
                }
              },
            },
          ]}
        >
          <LocaleInputGroup
            localeInputList={labelInputPropList}
            disabled={!isEditing}
          />
        </Form.Item>

        {/* 动态标签列表 */}
        <Form.List name={['labelList', 0, 'tagList']}>
          {(tagFields, { add: addTag, remove: removeTag }) => (
            <>
              {tagFields.map((tagField, tagIndex) => (
                <Row key={tagField.key} gutter={16}>
                  <Col span={20}>
                    <Form.Item
                      name={[tagField.name, 'tag']}
                      labelCol={{ ...layout.labelCol, offset: 2 }}
                      label={`标签${arabicToChinese(tagIndex + 1)}`}
                      rules={[
                        {
                          validator: async (_, value) => {
                            if (!value || !value.zh_CN) {
                              throw new Error('请输入中文标签');
                            }
                          },
                        },
                      ]}
                    >
                      <LocaleInputGroup
                        localeInputList={tagInputPropList}
                        disabled={!isEditing}
                      />
                    </Form.Item>
                  </Col>
                  {tagFields.length > 1 ? (
                    <Col span={4}>
                      {isEditing ? (
                        <Button
                          type="primary"
                          danger
                          icon={<DeleteOutlined />}
                          onClick={() => removeTag(tagIndex)}
                        >
                          删除标签
                        </Button>
                      ) : null}
                    </Col>
                  ) : null}
                </Row>
              ))}
              {tagFields.length < 20 ? (
                <Form.Item
                  labelCol={{ span: 0 }}
                  wrapperCol={{ offset: 4, span: 22 }}
                >
                  {isEditing ? (
                    <Button
                      size="large"
                      icon={<PlusCircleOutlined />}
                      style={{ width: '100%' }}
                      onClick={() => addTagInfo(addTag, tagFields.length)}
                      type="primary"
                    >
                      新增标签
                    </Button>
                  ) : null}
                </Form.Item>
              ) : null}
            </>
          )}
        </Form.List>
      </ProCard>
      <ProCard>
        <Form.Item wrapperCol={{ span: 24 }} style={{ textAlign: 'center' }}>
          {isEditing ? (
            <>
              <Button
                type="primary"
                htmlType="submit"
                style={{ marginRight: 16 }}
                loading={loading}
                disabled={loading}
              >
                提交
              </Button>
              <Button htmlType="reset" style={{ marginRight: 16 }} danger>
                重置
              </Button>
            </>
          ) : null}
          <Button type="default" onClick={handleCancel}>
            取消
          </Button>
        </Form.Item>
      </ProCard>
    </Form>
  );
};

export default Edit;
