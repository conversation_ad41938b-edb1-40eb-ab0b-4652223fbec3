import { BreadcrumbInfo } from '@/models/common.interface';
import global from '@/utils/global';
import { LeftOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { Outlet, useLocation } from '@umijs/max';
import React, { useEffect, useState } from 'react';

const breadcrumbInfo: BreadcrumbInfo = {
  path: '',
  breadcrumbName: '',
};

const Activity: React.FC = () => {
  const location = useLocation();
  const [title, setTitle] = useState(`AI共创`);
  const [showBackIcon, setShowBackIcon] = useState(false);
  const [breadcrumbInfoList, setBreadcrumbInfoList] = useState([
    breadcrumbInfo,
  ]);
  useEffect(() => {
    let _title = 'AI共创';

    if (location?.pathname.includes('ai-collaboration/approval')) {
      _title = '审核';
    } else {
      _title = 'AI共创';
    }
    setTitle(_title);

    const _breadcrumbInfoList = global.getBreadcrumbInfo(
      { ...breadcrumbInfo, breadcrumbName: _title },
      '',
      location,
    );
    setBreadcrumbInfoList(_breadcrumbInfoList);

    if (_breadcrumbInfoList.length > 2) {
      setShowBackIcon(true);
    } else {
      setShowBackIcon(false);
    }
  }, [location]);

  return (
    <PageContainer
      header={{
        backIcon: showBackIcon ? <LeftOutlined /> : '',
        onBack: () => history.back(),
        title,
        ghost: true,
        breadcrumb: {
          itemRender: (route) => (
            <span>{route.title || route.breadcrumbName}</span>
          ),
          items: breadcrumbInfoList.map((item) => ({
            title: item.breadcrumbName,
            path: item.path,
            key: item.path,
          })),
        },
      }}
    >
      <Outlet />
    </PageContainer>
  );
};

export default Activity;
