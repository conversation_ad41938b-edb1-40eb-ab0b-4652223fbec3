import {
  AiCreationDetail,
  AiCreationListParam,
  AiCreationParam,
  AwardDurationUnitEnum,
  TaskExampleTypeEnum,
  TaskPropertyEnum,
} from '@/models/aiCreation/interface';
import { initAiCreationListParam } from '@/models/aiCreation/util';
import { DeviceTypeEnum, SelectOption } from '@/models/common.interface';
import { ProductSku } from '@/models/product/interface';
import { uuid } from '@/utils/uuid';
import dayjs from 'dayjs';
import {} from '../../../models/common.util';
import {
  AiCollcaboartionForm,
  AiCollcaboartionTableForm,
  AiCollcaboartionTaskForm,
  AiCollcaboartionTaskOptionForm,
  UrlParam,
} from './interface';

export const suitableDeviceTypeOptions: SelectOption<DeviceTypeEnum>[] = [
  { label: 'D4sh', value: DeviceTypeEnum.D4sh },
  { label: 'D4h', value: DeviceTypeEnum.D4H },
  { label: 'T5', value: DeviceTypeEnum.T5 },
  { label: 'T6', value: DeviceTypeEnum.T6 },
];

// 将表格参数转换为url参数
export const transferTableFormDataToUrlParam = (
  formData: AiCollcaboartionTableForm,
): UrlParam => {
  const urlParam: UrlParam = {};
  if (formData.deviceType) urlParam.deviceType = +formData.deviceType;
  if (formData.title) urlParam.name = formData.title;
  if (formData.activityTime && formData.activityTime.length === 2) {
    urlParam.activityStartTime = formData.activityTime[0]
      .startOf('d')
      .valueOf();
    urlParam.activityEndTime = formData.activityTime[1].endOf('d').valueOf();
  }
  if (formData.createTime && formData.createTime.length === 2) {
    urlParam.createStartTime = formData.createTime[0].startOf('d').valueOf();
    urlParam.createEndTime = formData.createTime[1].endOf('d').valueOf();
  }
  return urlParam;
};

// 将url参数转换为表格参数
export const transferUrlParamToTableFormData = (
  urlParam: UrlParam,
): AiCollcaboartionTableForm => {
  const formData: AiCollcaboartionTableForm = {};
  if (urlParam.deviceType) formData.deviceType = urlParam.deviceType;
  if (urlParam.name) formData.title = urlParam.name;
  if (urlParam.activityStartTime && urlParam.activityEndTime) {
    formData.activityTime = [
      dayjs(+urlParam.activityStartTime),
      dayjs(+urlParam.activityEndTime),
    ];
  }
  if (urlParam.createStartTime && urlParam.createEndTime) {
    formData.createTime = [
      dayjs(+urlParam.createStartTime),
      dayjs(+urlParam.createEndTime),
    ];
  }
  return formData;
};

// 将url参数转换为列表接口参数
export const transferUrlParamToListParam = (
  urlParam: UrlParam,
): AiCreationListParam => {
  const param: AiCreationListParam = {
    ...initAiCreationListParam,
    deviceType: urlParam.deviceType ? +urlParam.deviceType : undefined,
    name: urlParam.name,
    activityStart: urlParam.activityStartTime
      ? +urlParam.activityStartTime
      : undefined,
    activityEnd: urlParam.activityEndTime
      ? +urlParam.activityEndTime
      : undefined,
    createStart: urlParam.createStartTime
      ? +urlParam.createStartTime
      : undefined,
    createEnd: urlParam.createEndTime ? +urlParam.createEndTime : undefined,
  };
  return param;
};

// 详情页面用
export const initAiCollcaboartionTaskOptionForm: AiCollcaboartionTaskOptionForm =
  {
    title: {},
    labelList: [{ label: {} }],
  };
export const initAiCollcaboartionTaskForm: AiCollcaboartionTaskForm = {
  name: '',
  title: {},
  description: {},
  property: TaskPropertyEnum.VIDEO_IMAGE,
  demoList: [],
  optionList: [initAiCollcaboartionTaskOptionForm],
};
export const initAiCollcaboartionForm: AiCollcaboartionForm = {
  acitivityTime: [],
  deviceType: null,
  title: {},
  description: {},
  rule: {},
  taskList: [initAiCollcaboartionTaskForm],
  planName: {},
  duration: 3,
  unit: AwardDurationUnitEnum.DAY,
};
// export const initAiCollcaboartionForm: AiCollcaboartionForm = {
//   acitivityTime: [],
//   deviceType: DeviceTypeEnum.D4sh,
//   title: {},
//   description: {},
//   rule: {},
//   taskList: [initAiCollcaboartionTaskForm],
//   planId: null,
//   planName: {},
//   duration: null,
//   unit: AwardDurationUnitEnum.DAY,
// };

// 将接口返回的详情数据转换为formData
export const transferDetailToFormData = (
  detail: AiCreationDetail,
): AiCollcaboartionForm => {
  const formData: AiCollcaboartionForm = {
    acitivityTime: [dayjs(detail.startTime), dayjs(detail.endTime)],
    deviceType: detail.deviceType,
    title: detail.title ? JSON.parse(detail.title) : {},
    description: detail.description ? JSON.parse(detail.description) : {},
    rule: detail.rule ? JSON.parse(detail.rule) : {},
    taskList: detail.taskList.map((task) => ({
      name: task.name,
      title: task.title ? JSON.parse(task.title) : {},
      description: task.desc ? JSON.parse(task.desc) : {},
      property: +task.property,
      demoList: [
        {
          uid: uuid(),
          name: task.example,
          url: task.example,
          type: `${TaskExampleTypeEnum[task.exampleType].toLowerCase()}/*`,
        },
      ],
      optionList: task.option.map((opt) => ({
        title: opt.title ? JSON.parse(opt.title) : {},
        labelList: opt.tagList.map((tag) => ({
          label: tag.name ? JSON.parse(tag.name) : {},
        })),
      })),
    })),
    planName: detail.planName ? JSON.parse(detail.planName) : {},
    duration: detail.duration,
    unit: detail.unit,
  };
  return formData;
};

// 将formData转换为param
export const transferFormDataToParam = (
  formData: AiCollcaboartionForm,
  associateSkuList: ProductSku[],
  detail?: AiCreationDetail,
): AiCreationParam => {
  const param: AiCreationParam = {
    startTime: formData.acitivityTime[0].startOf('d').valueOf(),
    endTime: formData.acitivityTime[1].endOf('d').valueOf(),
    deviceType: +(formData.deviceType || 0),
    title: JSON.stringify(formData.title),
    description: JSON.stringify(formData.description),
    rule: JSON.stringify(formData.rule),
    skuId: associateSkuList[0].id,
    planName: JSON.stringify(formData.planName),
    duration: formData.duration || 0,
    unit: formData.unit,
    taskList: JSON.stringify(
      formData.taskList.map((task) => ({
        name: task.name,
        title: JSON.stringify(task.title),
        desc: JSON.stringify(task.description),
        property: task.property,
        example: task.demoList[0].url || '',
        exampleType: task.demoList[0].type?.includes('video')
          ? TaskExampleTypeEnum.VIDEO
          : TaskExampleTypeEnum.IMAGE,
        option: task.optionList.map((option) => ({
          title: JSON.stringify(option.title),
          tagList: option.labelList.map((label) => ({
            name: JSON.stringify(label.label),
          })),
        })),
      })),
    ),
  };

  if (detail) {
    param.id = detail.id;
    param.planId = detail.planId;
  }
  return param;
};
