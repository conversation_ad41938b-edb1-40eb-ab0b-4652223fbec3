import {
  AwardDurationUnitEnum,
  TaskPropertyEnum,
} from '@/models/aiCreation/interface';
import { DeviceTypeEnum, LocaleObject } from '@/models/common.interface';
import { UploadFile } from 'antd';
import { Dayjs } from 'dayjs';

export interface UrlParam {
  deviceType?: DeviceTypeEnum;
  name?: string;
  activityStartTime?: number;
  activityEndTime?: number;
  createStartTime?: number;
  createEndTime?: number;
}

export interface AiCollcaboartionTableForm {
  deviceType?: DeviceTypeEnum;
  title?: string;
  activityTime?: Dayjs[];
  createTime?: Dayjs[];
}

export interface AiCollcaboartionTaskOptionForm {
  title: LocaleObject;
  labelList: { label: LocaleObject }[];
}
export interface AiCollcaboartionTaskForm {
  name: string;
  title: LocaleObject;
  description: LocaleObject;
  property: TaskPropertyEnum;
  demoList: UploadFile[];
  optionList: AiCollcaboartionTaskOptionForm[];
}
export type AiCollcaboartionForm = {
  acitivityTime: Dayjs[];
  deviceType: DeviceTypeEnum | null;
  title: LocaleObject;
  description: LocaleObject;
  rule: LocaleObject;
  planName: LocaleObject;
  duration: number | null;
  unit: AwardDurationUnitEnum;
  taskList: AiCollcaboartionTaskForm[];
};

export type RecordListUrlParam = {
  deviceType?: DeviceTypeEnum;
  name?: string;
  startTime?: number;
  endTime?: number;
  createStartTime?: number;
  createEndTime?: number;
};
