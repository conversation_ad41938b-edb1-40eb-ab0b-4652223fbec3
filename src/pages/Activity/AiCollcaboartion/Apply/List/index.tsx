import VideoPlayer from '@/components/VideoPlayer';
import {
  fetchAiCreationApplyCount,
  fetchAiCreationApplyList,
  fetchAiCreationNameQuery,
} from '@/models/aiCreation/fetch';
import {
  AiCreationApplyCountParam,
  AiCreationApplyInfo,
  AiCreationApplyListParam,
  AiCreationApplyStatusEnum,
  AiCreationTaskInfo,
} from '@/models/aiCreation/interface';
import { aiCreationApplyStatusNameMap } from '@/models/aiCreation/util';
import { MediaTypeEnum } from '@/models/common.enum';
import { DeviceTypeEnum, SelectOption } from '@/models/common.interface';
import { postMessageFunction, spanConfig } from '@/models/common.util';
import global from '@/utils/global';
import { Pagination, initPagination } from '@/utils/request';
import useUrlState from '@ahooksjs/use-url-state';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { Button, Form, Image, Select } from 'antd';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { AiCreationApplyTableForm, UrlParam } from '../interface';
import {
  transferTableFormDataToUrlParam,
  transferUrlParamToListParam,
  transferUrlParamToTableFormData,
} from '../util';

const List: React.FC = () => {
  const [urlParam] = useUrlState<UrlParam>();
  const [aiCreationNameList, setAiCreationNameList] = useState<
    AiCreationTaskInfo[]
  >([]);

  const [unCheckedApplyCount, setUnCheckedApplyCount] = useState(0);
  const [dataList, setDataList] = useState<AiCreationApplyInfo[]>([]);
  const [listParam, setListParam] = useState<AiCreationApplyListParam>();
  const [pagination, setPagination] = useState<Pagination>(initPagination);
  const formRef = useRef<ProFormInstance<AiCreationApplyTableForm>>();
  const actNameId = Form.useWatch('actName', formRef.current);

  const actOptions = useMemo(() => {
    return aiCreationNameList.map((item) => ({
      label: `${JSON.parse(item.title).zh_CN} - ${item.id}`,
      value: item.id,
    }));
  }, [aiCreationNameList]);

  const actTaskOptions = useMemo(() => {
    if (!actNameId || !aiCreationNameList || !aiCreationNameList.length)
      return [];
    const aiCreation = aiCreationNameList.find((item) => item.id === actNameId);
    if (!aiCreation) return [];

    const taskList = aiCreation.taskList;
    const taskOptions: SelectOption[] = taskList.map((task) => ({
      value: task.id,
      label: task.name,
    }));
    return taskOptions;
  }, [aiCreationNameList, actNameId]);

  // 获取列表数据
  const requestAiCreationApplyList = async (
    param: AiCreationApplyListParam,
  ) => {
    const { items, ...rest } = await fetchAiCreationApplyList(param);
    setPagination(rest);
    setDataList(items);
  };

  // 获取未审核数量
  const requestAiCreationApplyCount = async () => {
    const param: AiCreationApplyCountParam = {
      deviceType: listParam?.deviceType,
      id: listParam?.id,
      taskId: listParam?.taskId,
      userId: listParam?.userId,
      deviceId: listParam?.deviceId,
    };
    const result = await fetchAiCreationApplyCount(param);
    setUnCheckedApplyCount(result.unCheckCount);
  };

  // 模糊获取活动名称搜索
  const requestAiCreationNameList = async () => {
    const _aiCreationNameList = await fetchAiCreationNameQuery();
    const nameOptions: SelectOption[] = [];
    // const _taskValueEnum: Record<number, string> = {};
    _aiCreationNameList.forEach((item) => {
      const option: SelectOption = {
        label: JSON.parse(item.title).zh_CN,
        value: item.id,
      };
      nameOptions.push(option);
    });
    setAiCreationNameList(_aiCreationNameList);
  };

  // 编辑
  const editAiCreationApply = (record: AiCreationApplyInfo) => {
    const url = `/activity/ai-collaboration/approval/edit/${record.id}/${record.userId}/${record.actId}`;
    console.log(url);
    history.push(url);
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          const formData = form?.getFieldsValue();
          const param: UrlParam = transferTableFormDataToUrlParam(formData);
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: '/activity/aiCollaboration/reviewList',
              param: param as unknown as Record<string, string>,
            },
          });
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          form?.resetFields();
        }}
      >
        重置
      </Button>,
    ];
  };

  const onPaginationChanged = (page: number, pageSize: number) => {
    let index = page;
    if (pageSize !== listParam?.limit) {
      index = 1;
    }
    setListParam({
      ...listParam,
      limit: pageSize,
      offset: (index - 1) * pageSize,
    });
  };

  const columns: Array<ProColumns<AiCreationApplyInfo>> = [
    {
      title: '适用设备',
      dataIndex: 'deviceName',
      valueType: 'select',
      width: 100,
      valueEnum: {
        [DeviceTypeEnum.D4sh]: 'D4sh',
        [DeviceTypeEnum.D4H]: 'D4h',
        [DeviceTypeEnum.T5]: 'T5',
        [DeviceTypeEnum.T6]: 'T6',
        [DeviceTypeEnum.T7]: 'T7',
      },
    },
    {
      title: '活动名称',
      dataIndex: 'actName',
      valueType: 'select',
      width: 150,
      renderFormItem: (_, { defaultRender }) => (
        <Select
          {...defaultRender}
          showSearch
          allowClear
          placeholder="请选择活动"
          options={actOptions}
          filterOption={(input, option) =>
            (option?.label || '').includes(input)
          }
          onClear={() => {
            formRef.current?.setFieldsValue({ taskId: undefined });
          }}
        />
      ),
    },
    {
      title: '任务',
      dataIndex: 'taskId',
      hideInTable: true,
      valueType: 'select',
      renderFormItem: (_, { defaultRender }) => (
        <Select
          {...defaultRender}
          showSearch
          allowClear
          placeholder="请选择任务"
          options={actTaskOptions}
          disabled={!actNameId}
        />
      ),
    },
    { title: '用户ID', dataIndex: 'userId', width: 120 },
    { title: '设备ID', dataIndex: 'deviceId', width: 120 },
    { title: '任务', dataIndex: 'taskName', width: 120, search: false },
    {
      title: '视频/图片',
      dataIndex: 'material',
      width: 150,
      search: false,
      render: (_, record) => (
        <>
          {record.material.urlType === MediaTypeEnum.IMAGE ? (
            <Image
              width={150}
              style={{ cursor: 'pointer' }}
              alt="图片"
              src={`${record.material.url}`}
              preview={{
                toolbarRender: () => null,
              }}
            />
          ) : null}
          {record.material.urlType === MediaTypeEnum.VIDEO ? (
            <Image
              width={150}
              style={{ cursor: 'pointer' }}
              alt="视频"
              src={record.material.url}
              fallback={global.defaultFallbackImage}
              preview={{
                imageRender: () => (
                  <VideoPlayer
                    width={1000}
                    height={800}
                    src={`${record.material.url}`}
                  />
                ),
                toolbarRender: () => null,
              }}
            />
          ) : null}
        </>
      ),
    },
    {
      title: '审核状态',
      dataIndex: 'status',
      valueType: 'radioButton',
      width: 120,
      valueEnum: {
        [AiCreationApplyStatusEnum.UNCHECKED]: `未审核 (${unCheckedApplyCount})`,
        [AiCreationApplyStatusEnum.APPROVAL]: '审核通过',
        [AiCreationApplyStatusEnum.REJECT]: '审核失败',
      },
      render: (_, record) => aiCreationApplyStatusNameMap[record.status],
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 80,
      search: false,
      fixed: 'right',
      render: (_, record) => (
        <Button type="link" onClick={() => editAiCreationApply(record)}>
          查看
        </Button>
      ),
    },
  ];

  useEffect(() => {
    requestAiCreationNameList();
  }, []);

  useEffect(() => {
    if (!listParam) return;
    requestAiCreationApplyList(listParam);
    requestAiCreationApplyCount();
  }, [listParam]);

  useEffect(() => {
    const form = formRef.current;
    const formData = transferUrlParamToTableFormData(urlParam);
    const _listParam = transferUrlParamToListParam(urlParam);

    console.log(formData, _listParam);

    setListParam(_listParam);

    if (form) {
      form.setFieldsValue(formData);
    }
  }, [urlParam]);

  return (
    <ProTable<AiCreationApplyInfo>
      dataSource={dataList}
      columns={columns}
      formRef={formRef}
      defaultSize="small"
      rowKey="id"
      search={{
        defaultCollapsed: false,
        span: spanConfig,
        optionRender: searchOptionRender,
      }}
      scroll={{
        x: columns
          .filter((col) => col.dataIndex !== 'action')
          .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
      }}
      options={{ reload: () => listParam && setListParam({ ...listParam }) }}
      pagination={{
        pageSize: pagination.limit,
        total: pagination.total,
        showQuickJumper: true,
        onChange: onPaginationChanged,
      }}
    />
  );
};

export default List;
