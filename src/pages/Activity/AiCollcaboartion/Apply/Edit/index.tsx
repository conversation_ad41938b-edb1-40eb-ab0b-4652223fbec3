import VideoPlayer from '@/components/VideoPlayer';
import {
  fetchAiCreationApplyApprove,
  fetchAiCreationApplyList,
} from '@/models/aiCreation/fetch';
import {
  AiCreationApplyApproveParam,
  AiCreationApplyInfo,
  AiCreationApplyListParam,
  AiCreationApplyStatusEnum,
  TaskApproveStatusEnum,
} from '@/models/aiCreation/interface';
import { initAiCreationApplyListParam } from '@/models/aiCreation/util';
import { MediaTypeEnum } from '@/models/common.enum';
import { ApiSuccessEnum, SelectOption } from '@/models/common.interface';
import global from '@/utils/global';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { history, useDispatch, useParams } from '@umijs/max';
import {
  Badge,
  Button,
  Card,
  Col,
  Form,
  Image,
  Input,
  Row,
  Typography,
  message,
} from 'antd';
import React, { useEffect, useMemo, useState } from 'react';

const Edit: React.FC = () => {
  const dispatch = useDispatch();
  const param = useParams<{ id: string; userId: string; activityId: string }>();
  const [loading, setLoading] = useState(false);
  const [listParam, setListParam] = useState<AiCreationApplyListParam>();
  const [dataList, setDataList] = useState<AiCreationApplyInfo[]>([]);
  const [currentDetail, setCurrentDetail] = useState<AiCreationApplyInfo>();
  const [remark, setRemark] = useState('');
  const [selectedPassOption, setSelectedPassOption] =
    useState<SelectOption<TaskApproveStatusEnum>>();
  const [passOptions, setPassOptions] = useState<
    SelectOption<TaskApproveStatusEnum>[]
  >([
    { label: '合格', value: TaskApproveStatusEnum.APPROVED },
    { label: '不合格', value: TaskApproveStatusEnum.REJECT },
  ]);
  const [selectedReasonOption, setSelectedReasonOption] =
    useState<SelectOption>();
  const [reasonOptions, setReasonOptions] = useState<SelectOption[]>([
    { label: '内容不符合主题', value: '内容不符合主题' },
    { label: '内容不清晰', value: '内容不清晰' },
    { label: '素材打不开', value: '素材打不开' },
  ]);

  const actName = useMemo(() => {
    if (!currentDetail) return '';
    const { actName } = currentDetail;
    const nameInfo = JSON.parse(actName);
    return nameInfo.zh_CN;
  }, [currentDetail]);

  const optionInfo = useMemo(() => {
    if (
      !currentDetail ||
      !currentDetail.material ||
      !currentDetail.material.options ||
      !currentDetail.material.options.length
    )
      return '';

    let infoNode;
    try {
      infoNode = (
        <div>
          {currentDetail.material.options.map((item, index) => {
            const title = JSON.parse(currentDetail.material.options[0].title);
            const tagName = JSON.parse(
              currentDetail.material.options[0].tagName,
            );
            return (
              <div key={String(index)}>
                {title.zh_CN}：{tagName.zh_CN}
              </div>
            );
          })}
        </div>
      );
    } catch (error) {
      console.log(error);
    } finally {
      return infoNode;
    }
  }, [currentDetail]);

  const canSave = useMemo(() => {
    if (!currentDetail) return true;

    if (currentDetail.status === AiCreationApplyStatusEnum.UNCHECKED)
      return true;

    return false;
  }, [currentDetail]);

  const applyResultMap: {
    [key in AiCreationApplyStatusEnum]: { text: string; color: string };
  } = {
    [AiCreationApplyStatusEnum.APPROVAL]: { text: '审核通过', color: 'green' },
    [AiCreationApplyStatusEnum.REJECT]: { text: '未通过', color: 'red' },
    [AiCreationApplyStatusEnum.UNCHECKED]: { text: '', color: '' },
  };

  const getOtherApplyInfoNode = (item: AiCreationApplyInfo) => {
    const card = (
      <Card>
        <div
          style={{
            width: 200,
            height: 200,
            overflow: 'hidden',
            marginBottom: 16,
          }}
        >
          <Image
            src={item.material.url}
            preview={{ toolbarRender: () => null }}
          />
        </div>
        <div style={{ textAlign: 'center', marginBottom: 8 }}>
          {item.taskName}
        </div>
        <div style={{ textAlign: 'center' }}>
          类型：{JSON.parse(item.material.options[0].tagName).zh_CN}
        </div>
      </Card>
    );

    if (item.status === AiCreationApplyStatusEnum.UNCHECKED) return card;

    return (
      <Badge.Ribbon
        text={applyResultMap[item.status].text}
        color={applyResultMap[item.status].color}
      >
        {card}
      </Badge.Ribbon>
    );
  };

  const gotoApplyDetail = (type: 'left' | 'right') => {
    if (!currentDetail) return;
    const index = dataList.findIndex((item) => item.id === currentDetail?.id);
    let newIndex = index + (type === 'left' ? -1 : 1);
    if (newIndex < 0) {
      newIndex = dataList.length - 1;
    } else if (newIndex >= dataList.length) {
      newIndex = 0;
    }
    const { id, actId, userId } = dataList[newIndex];
    history.replace(
      `/activity/ai-collaboration/approval/edit/${id}/${userId}/${actId}`,
    );
  };

  // 根据id获取详情数据
  const requestAllCheckList = async (listParam: AiCreationApplyListParam) => {
    const { items } = await fetchAiCreationApplyList(listParam);
    setDataList(items);

    const _detail = items.find((item) => item.id === +(param.id || 0));
    // console.log(_detail, items, +(param.id || 0));
    setCurrentDetail(_detail);
  };

  // 提交form表单
  const submit = async () => {
    // setLoading(true);
    if (!currentDetail || !selectedPassOption) return;

    if (
      selectedPassOption.value === TaskApproveStatusEnum.REJECT &&
      !selectedReasonOption
    ) {
      message.error('请选择审核不通过的原因');
      return;
    }

    const _param: AiCreationApplyApproveParam = {
      checkId: currentDetail.id,
      disable: selectedPassOption.value,
      remark: remark,
    };
    if (
      selectedPassOption.value === TaskApproveStatusEnum.REJECT &&
      selectedReasonOption
    ) {
      _param.reason = selectedReasonOption.value as string;
    }
    console.log(_param);
    try {
      const result = await fetchAiCreationApplyApprove(_param);

      if (result === ApiSuccessEnum.success) {
        message.success(`审核完成!`);
        history.back();
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // 额外dva接口请求
  }, [dispatch]);

  useEffect(() => {
    if (!param || !param.id || !param.userId || !param.activityId) {
      history.back();
      return;
    }
    setListParam({
      ...initAiCreationApplyListParam,
      limit: 10000,
      id: +param.activityId,
      userId: +param.userId,
    });
  }, [param]);

  useEffect(() => {
    if (listParam) {
      requestAllCheckList(listParam);
    }
  }, [listParam]);

  useEffect(() => {
    if (!currentDetail) return;

    if (currentDetail.status === AiCreationApplyStatusEnum.UNCHECKED) return;

    if (currentDetail.status === AiCreationApplyStatusEnum.APPROVAL) {
      setSelectedPassOption(
        passOptions.find(
          (item) => item.value === TaskApproveStatusEnum.APPROVED,
        ),
      );
    } else {
      setSelectedPassOption(
        passOptions.find((item) => item.value === TaskApproveStatusEnum.REJECT),
      );

      console.log(currentDetail);
      if (currentDetail.reason) {
        setSelectedReasonOption({
          label: currentDetail.reason,
          value: currentDetail.reason,
        });
      }
    }

    setRemark(currentDetail.remark || '');
  }, [currentDetail]);

  if (!currentDetail) return <></>;

  return (
    <ProCard
      title={
        <Typography.Text>
          适用设备：{currentDetail?.deviceName}｜用户：
          {currentDetail?.userId} ｜活动名称：{actName} ｜ 任务：
          {currentDetail?.taskName}
        </Typography.Text>
      }
    >
      <div>
        <Row style={{ marginTop: 16 }}>
          <Col
            span={24}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <LeftOutlined
              style={{ fontSize: 40, marginRight: 50, cursor: 'pointer' }}
              onClick={() => gotoApplyDetail('left')}
            />
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                width: 300,
              }}
            >
              <div style={{ marginBottom: 16 }}>
                <Image
                  src={currentDetail.material.url}
                  width={300}
                  height={300}
                  preview={{
                    imageRender: (originalNode) => {
                      if (
                        currentDetail.material.urlType === MediaTypeEnum.VIDEO
                      )
                        return (
                          <VideoPlayer
                            width={1000}
                            height={800}
                            src={`${currentDetail.material.url}`}
                          />
                        );

                      return originalNode;
                    },
                    toolbarRender: () => null,
                  }}
                  fallback={global.defaultFallbackImage}
                />
              </div>
            </div>
            <RightOutlined
              style={{ fontSize: 40, marginLeft: 50, cursor: 'pointer' }}
              onClick={() => gotoApplyDetail('right')}
            />
          </Col>
        </Row>
        <Row style={{ marginBottom: 32 }}>
          <Col
            span={24}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <div style={{ width: 300, textAlign: 'center' }}>
              {/* <div style={{ marginBottom: 8 }}>宠物叫声</div> */}
              <div>{optionInfo}</div>
            </div>
          </Col>
        </Row>
        <Row style={{ marginBottom: 32 }}>
          <Col
            span={24}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            {passOptions.map((option, index) => (
              <Button
                key={String(index)}
                type="default"
                size="large"
                style={{
                  width: 200,
                  height: 50,
                  marginRight: passOptions.length - 1 === index ? 0 : 50,
                  color:
                    selectedPassOption?.value === option.value
                      ? '#4096ff'
                      : '#000',
                  borderColor:
                    selectedPassOption?.value === option.value
                      ? '#4096ff'
                      : '#d9d9d9',
                }}
                disabled={!canSave}
                onClick={() => {
                  setSelectedPassOption(option);
                  setSelectedReasonOption(undefined);
                }}
              >
                {option.label}
              </Button>
            ))}
          </Col>
        </Row>
        {selectedPassOption &&
        selectedPassOption?.value === TaskApproveStatusEnum.REJECT ? (
          <Row style={{ marginBottom: 32 }}>
            <Col
              span={24}
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              {reasonOptions.map((option, index) => (
                <Button
                  key={String(index)}
                  type="default"
                  size="large"
                  shape="round"
                  style={{
                    width: 200,
                    height: 50,
                    marginRight: reasonOptions.length - 1 === index ? 0 : 50,
                    color:
                      selectedReasonOption?.value === option.value
                        ? '#4096ff'
                        : '#000',
                    borderColor:
                      selectedReasonOption?.value === option.value
                        ? '#4096ff'
                        : '#d9d9d9',
                  }}
                  disabled={!canSave}
                  onClick={() => setSelectedReasonOption(option)}
                >
                  {option.label}
                </Button>
              ))}
            </Col>
          </Row>
        ) : null}
        <Row style={{ marginBottom: 32 }}>
          <Col
            span={24}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <div style={{ width: 500 }}>
              <label>备注：</label>
              {canSave ? (
                <Input.TextArea
                  rows={4}
                  placeholder="请输入备注"
                  value={remark}
                  onChange={(ev) => setRemark(ev.target.value)}
                  showCount
                  maxLength={200}
                />
              ) : (
                <Typography.Text>{currentDetail.remark}</Typography.Text>
              )}
            </div>
          </Col>
        </Row>
        {canSave ? (
          <Form.Item style={{ textAlign: 'center' }}>
            <Button
              type="primary"
              style={{ marginRight: 16 }}
              loading={loading}
              disabled={loading}
              size="large"
              onClick={submit}
            >
              保存
            </Button>
            <Button size="large" type="default" onClick={history.back}>
              取消
            </Button>
          </Form.Item>
        ) : null}
      </div>

      <hr />

      <div>
        <ProCard
          title="该用户其它视频"
          bodyStyle={{ display: 'flex', flexWrap: 'wrap' }}
        >
          {dataList
            .filter((item) => item.id !== +(param.id || 0))
            .map((item, index) => (
              <ProCard
                colSpan={{
                  xs: 24,
                  sm: 24,
                  md: 12,
                  lg: 12,
                  xl: 8,
                  xxl: 6,
                }}
                key={String(index)}
                bordered={false}
                style={{ minWidth: 300, maxWidth: 300, overflow: 'hidden' }}
              >
                {getOtherApplyInfoNode(item)}
              </ProCard>
            ))}
        </ProCard>
      </div>
    </ProCard>
  );
};

export default Edit;
