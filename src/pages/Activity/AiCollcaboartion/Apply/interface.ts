import { AiCreationApplyStatusEnum } from '@/models/aiCreation/interface';
import { DeviceTypeEnum } from '@/models/common.interface';

export interface UrlParam {
  deviceType?: DeviceTypeEnum;
  id?: number;
  name?: string;
  taskId?: number;
  userId?: number;
  deviceId?: number;
  status?: AiCreationApplyStatusEnum;
}

export interface AiCreationApplyTableForm {
  deviceName?: DeviceTypeEnum;
  actName?: number;
  taskId?: number;
  userId?: number;
  deviceId?: number;
  status?: AiCreationApplyStatusEnum;
}

export interface AiCreationApprovalForm {}
