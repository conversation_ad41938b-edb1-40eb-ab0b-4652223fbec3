import { AiCreationApplyListParam } from '@/models/aiCreation/interface';
import { initAiCreationApplyListParam } from '@/models/aiCreation/util';
import {
  AiCreationApplyTableForm,
  AiCreationApprovalForm,
  UrlParam,
} from './interface';

export const transferTableFormDataToUrlParam = (
  formData: AiCreationApplyTableForm,
): UrlParam => {
  const urlParam: UrlParam = {};

  if (formData.deviceName) {
    urlParam.deviceType = formData.deviceName;
  }

  if (formData.actName) {
    urlParam.id = +formData.actName;
  }

  if (formData.taskId) {
    urlParam.taskId = +formData.taskId;
  }

  if (formData.userId) {
    urlParam.userId = +formData.userId;
  }

  if (formData.deviceId) {
    urlParam.deviceId = +formData.deviceId;
  }

  if (formData.status) {
    urlParam.status = formData.status;
  }

  return urlParam;
};

export const transferUrlParamToTableFormData = (
  urlParam: UrlParam,
): AiCreationApplyTableForm => {
  const formData: AiCreationApplyTableForm = {};

  if (urlParam.deviceType) {
    formData.deviceName = urlParam.deviceType;
  }

  if (urlParam.id) {
    formData.actName = +urlParam.id;
  }

  if (urlParam.taskId) {
    formData.taskId = +urlParam.taskId;
  }

  if (urlParam.userId) {
    formData.userId = urlParam.userId;
  }

  if (urlParam.deviceId) {
    formData.deviceId = +urlParam.deviceId;
  }

  if (urlParam.status) {
    formData.status = urlParam.status;
  }

  return formData;
};

export const transferUrlParamToListParam = (
  urlParam: UrlParam,
): AiCreationApplyListParam => {
  const param: AiCreationApplyListParam = {
    ...initAiCreationApplyListParam,
    deviceType: urlParam.deviceType,
    id: urlParam.id,
    taskId: urlParam.taskId,
    userId: urlParam.userId,
    deviceId: urlParam.deviceId,
    status: urlParam.status,
  };
  return param;
};

export const initialAiCreationApprovalForm: AiCreationApprovalForm = {};

// 将formData转换为param
// export const transferFormDataToParam = (formData: AiCreationApprovalForm, id?: number): AiCreationApprovalParam => {
//   const param: AiCreationApprovalParam = {
//   };
//   id && (param.id = id);
//   return param;
// };

// // 将接口返回的详情数据转换为formData
// export const transferDetailToFormData = (detail: AiCreationApprovalDetail): AiCreationApprovalForm => {
//   const formData: AiCreationApprovalForm = {
//   };
//   return formData;
// };
