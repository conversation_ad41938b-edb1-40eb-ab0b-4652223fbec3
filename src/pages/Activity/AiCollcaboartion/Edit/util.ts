import { LocaleInputList } from '@/components/LocaleInputGroup/interface';
import { InputProps } from 'antd';
import { TextAreaProps } from 'antd/es/input';

// 普通多语言输入框配置
const inputProps: InputProps = {
  placeholder: '',
};
const textareaProps: TextAreaProps = {
  rows: 2,
  placeholder: '',
};

// 活动标题配置
export const titleInputPropList: LocaleInputList[] = [
  {
    locale: 'zh_CN',
    required: true,
    inputProps: { ...inputProps, placeholder: '请输入活动标题' },
  },
  {
    locale: 'en_US',
    inputProps: { ...inputProps, placeholder: '请输入活动标题' },
  },
];
// 活动描述配置
export const descriptionPropList: LocaleInputList[] = [
  {
    locale: 'zh_CN',
    required: true,
    textareaProps: { ...textareaProps, placeholder: '请输入活动描述' },
    type: 'textarea',
  },
  {
    locale: 'en_US',
    textareaProps: { ...textareaProps, placeholder: '请输入活动描述' },
    type: 'textarea',
  },
];

// 活动规则配置
export const ruleInputPropList: LocaleInputList[] = [
  {
    locale: 'zh_CN',
    type: 'textarea',
    required: true,
    textareaProps: {
      ...textareaProps,
      placeholder: '请输入活动规则',
    },
  },
  {
    locale: 'en_US',
    type: 'textarea',
    textareaProps: {
      ...textareaProps,
      placeholder: '请输入活动规则',
    },
  },
];

// 任务标题配置
export const getTaskTitleInputPropList = (
  _inputProps: InputProps,
): LocaleInputList[] => [
  {
    locale: 'zh_CN',
    required: true,
    inputProps: {
      ...inputProps,
      ..._inputProps,
      placeholder: '请输入任务标题',
    },
  },
  {
    locale: 'en_US',
    inputProps: {
      ...inputProps,
      ..._inputProps,
      placeholder: '请输入任务标题',
    },
  },
];

// 任务描述配置
export const getTaskDescriptionInputPropList = (
  _textareaProps: TextAreaProps,
): LocaleInputList[] => [
  {
    locale: 'zh_CN',
    type: 'textarea',
    required: true,
    textareaProps: {
      ...textareaProps,
      ..._textareaProps,
      placeholder: '请输入任务描述',
    },
  },
  {
    locale: 'en_US',
    type: 'textarea',
    textareaProps: {
      ...textareaProps,
      ..._textareaProps,
      placeholder: '请输入任务描述',
    },
  },
];

// 任务选项标题配置
export const getTaskOptionTitlePropList = (
  _inputProps: InputProps,
): LocaleInputList[] => [
  {
    locale: 'zh_CN',
    type: 'input',
    required: true,
    inputProps: {
      ...inputProps,
      ..._inputProps,
      placeholder: '请输入任务选项标题',
    },
  },
  {
    locale: 'en_US',
    type: 'input',
    inputProps: {
      ...inputProps,
      ..._inputProps,
      placeholder: '请输入任务选项标题',
    },
  },
];

// 任务选项标签配置
export const getTaskOptionLabelPropList = (
  _inputProps: InputProps,
): LocaleInputList[] => [
  {
    locale: 'zh_CN',
    type: 'input',
    required: true,
    inputProps: {
      ...inputProps,
      ..._inputProps,
      placeholder: '请输入任务选项标签',
    },
  },
  {
    locale: 'en_US',
    type: 'input',
    inputProps: {
      ...inputProps,
      ..._inputProps,
      placeholder: '请输入任务选项标签',
    },
  },
];

// 套餐名称配置
export const getPlanNameInputPropList = (
  _inputProps: InputProps,
): LocaleInputList[] => [
  {
    locale: 'zh_CN',
    required: true,
    inputProps: {
      ...inputProps,
      ..._inputProps,
      placeholder: '请输入活动Plan名称',
    },
  },
  {
    locale: 'en_US',
    inputProps: {
      ...inputProps,
      ..._inputProps,
      placeholder: '请输入活动Plan名称',
    },
  },
];
