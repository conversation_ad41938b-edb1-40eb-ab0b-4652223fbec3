import LocaleInputGroup from '@/components/LocaleInputGroup';
import { validateLocaleInputGroup } from '@/components/LocaleInputGroup/util';
import SkuSelector from '@/components/SkuSelector';
import { associateSkuTableColumns } from '@/components/SkuSelector/util';
import Uploader from '@/components/Uploader';
import {
  fetchAiCreationCreation,
  fetchAiCreationDetail,
  fetchAiCreationUpdate,
} from '@/models/aiCreation/fetch';
import {
  AiCreationDetail,
  AwardDurationUnitEnum,
  TaskPropertyEnum,
} from '@/models/aiCreation/interface';
import { ApiSuccessEnum, SelectOption } from '@/models/common.interface';
import { fetchProductSkuDetail } from '@/models/product/fetch';
import {
  ProductSku,
  RelationProductSkuListParam,
  RelationProductSkuParam,
  SaleStatusEnum,
} from '@/models/product/interface';
import { initRelationProductSkuListParam } from '@/models/product/util';
import { arabicToChinese } from '@/utils/arabicToChinese';
import normFile from '@/utils/normFile';
import { initPaginatorParam } from '@/utils/request';
import {
  DeleteOutlined,
  InfoCircleFilled,
  PlusOutlined,
} from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { history, useDispatch, useParams } from '@umijs/max';
import {
  Button,
  Col,
  DatePicker,
  Form,
  FormListOperation,
  Input,
  InputNumber,
  Radio,
  Row,
  Select,
  Table,
  message,
} from 'antd';
import { ColumnsType } from 'antd/es/table';
import React, { useEffect, useMemo, useState } from 'react';
import styles from '../index.less';
import { AiCollcaboartionForm } from '../interface';
import {
  initAiCollcaboartionForm,
  initAiCollcaboartionTaskForm,
  initAiCollcaboartionTaskOptionForm,
  suitableDeviceTypeOptions,
  transferDetailToFormData,
  transferFormDataToParam,
} from '../util';
import {
  descriptionPropList,
  getPlanNameInputPropList,
  getTaskDescriptionInputPropList,
  getTaskOptionLabelPropList,
  getTaskTitleInputPropList,
  ruleInputPropList,
  titleInputPropList,
} from './util';

const Edit: React.FC = () => {
  const dispatch = useDispatch();
  const param = useParams<{ id: string }>();
  const [form] = Form.useForm<AiCollcaboartionForm>();
  const [detail, setDetail] = useState<AiCreationDetail>();
  const [loading, setLoading] = useState(false);
  const [showSkuSelector, setShowSkuSelector] = useState(false);
  const [associateSkuList, setAssociateSkuList] = useState<ProductSku[]>([]);
  const [uploadAccecpt, setUploadAccecpt] = useState<Record<number, string>>(
    {},
  );
  const [associateSkuListParam, setAssociateSkuListParam] =
    useState<RelationProductSkuListParam>(initRelationProductSkuListParam);
  const layout = {
    labelCol: { span: 3 },
    wrapperCol: { span: 20 },
  };
  const taskPropertyOptions: SelectOption[] = [
    { label: '仅视频', value: TaskPropertyEnum.VIDEO },
    { label: '仅图片', value: TaskPropertyEnum.IMAGE },
    { label: '视频和图片', value: TaskPropertyEnum.VIDEO_IMAGE },
  ];

  const taskList = Form.useWatch(['taskList'], form);

  const isEditing = useMemo(() => {
    return !!+(param.id || 0);
  }, [param.id]);

  // 根据id获取详情数据
  const requestDetailById = async (id: number) => {
    const _detail = await fetchAiCreationDetail(id);
    const formData = transferDetailToFormData(_detail);
    form.setFieldsValue(formData);
    setDetail(_detail);

    const relationDetail = await fetchProductSkuDetail(_detail.skuId);
    setAssociateSkuList([relationDetail]);
  };

  const showProductSelector = () => {
    const formData = form.getFieldsValue();
    const { deviceType } = formData;
    const deviceInfo = suitableDeviceTypeOptions.find(
      (option) => option.value === deviceType,
    )?.label;

    if (!deviceType || !deviceInfo) {
      message.warning('请先选择适用设备');
      return;
    }
    const _param: RelationProductSkuParam = {
      // isReNew: ReNewEnum.NOT_RENEW,
      uniteDeviceTypes: [deviceInfo],
      uniteCapacities: [],
      saleStatus: SaleStatusEnum.ON_SALE,
    };
    setAssociateSkuListParam({
      ...initPaginatorParam,
      payload: _param,
    });
    setShowSkuSelector(true);
  };

  // 新增任务
  const addTaskInfo = (add: FormListOperation['add']) => {
    add(initAiCollcaboartionTaskForm);
  };

  // 新增任务选项
  const addTaskOptionInfo = (add: FormListOperation['add']) => {
    add(initAiCollcaboartionTaskOptionForm);
  };

  // 新增任务选项标签
  const addTaskOptionLabelInfo = (add: FormListOperation['add']) => {
    add({ label: {} });
  };

  const addSkuInfo = (sku: ProductSku) => {
    // const skuName = sku.name;
    // form.setFieldValue('planName', { zh_CN: skuName, en_US: skuName });
    setAssociateSkuList([sku]);
  };

  // 提交form表单
  const submit = async (formData: AiCollcaboartionForm) => {
    if (!associateSkuList || !associateSkuList.length) {
      message.warning('请先选择要关联的活动Plan');
      return;
    }
    setLoading(true);
    const _param = transferFormDataToParam(formData, associateSkuList, detail);
    console.log('submit', formData, associateSkuList, _param);
    // return;
    let result = '';
    let state = '';
    try {
      if (param && +(param.id || 0)) {
        state = '更新';
        result = await fetchAiCreationUpdate(_param);
      } else {
        state = '创建';
        result = await fetchAiCreationCreation(_param);
      }

      if (result === ApiSuccessEnum.success) {
        message.success(`${state}成功！`);
        history.back();
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // 额外dva接口请求
  }, [dispatch]);

  useEffect(() => {
    if (param && param.id && +param.id) {
      requestDetailById(+param.id);
    }
  }, [param]);

  useEffect(() => {
    if (!taskList || !taskList.length) return;
    const imageAccept = 'image/*';
    const videoAccept = 'video/*';
    // console.log('taskList', taskList);
    taskList.forEach((task, index) => {
      if (
        (!task.demoList || !task.demoList.length) &&
        task.property === TaskPropertyEnum.VIDEO_IMAGE
      ) {
        return;
      }
      if (task.property === TaskPropertyEnum.VIDEO_IMAGE) {
        setUploadAccecpt({
          ...uploadAccecpt,
          [index]: `${imageAccept},${videoAccept}`,
        });
        return;
      }

      if (task.property === TaskPropertyEnum.IMAGE) {
        if (task.demoList.some((demo) => demo.type?.includes('video'))) {
          form.setFieldValue(['taskList', index, 'demoList'], []);
        }
        setUploadAccecpt({ ...uploadAccecpt, [index]: `${imageAccept}` });
        return;
      }

      if (task.property === TaskPropertyEnum.VIDEO) {
        if (task.demoList.some((demo) => demo.type?.includes('image'))) {
          form.setFieldValue(['taskList', index, 'demoList'], []);
        }
        setUploadAccecpt({ ...uploadAccecpt, [index]: `${videoAccept}` });
        return;
      }
    });
  }, [taskList]);

  // useEffect(() => {
  //   console.log('uploadAccecpt', { ...uploadAccecpt });
  // }, [uploadAccecpt]);

  return (
    <>
      <Form
        {...layout}
        form={form}
        onFinish={submit}
        initialValues={initAiCollcaboartionForm}
      >
        <ProCard title="活动设置" style={{ marginBottom: 16 }}>
          <Form.Item
            label="活动时间"
            name="acitivityTime"
            rules={[{ required: true, message: '请选择活动时间' }]}
          >
            <DatePicker.RangePicker
              // style={{ width: '100%' }}
              placeholder={['开始时间', '结束时间']}
            />
          </Form.Item>
          <Form.Item
            label="适用设备"
            name="deviceType"
            rules={[{ required: true, message: '请选择适用设备' }]}
          >
            <Select
              placeholder="请选择适用设备"
              options={suitableDeviceTypeOptions}
              disabled={isEditing}
            />
          </Form.Item>
          {/* <Form.Item label="活动标题" name="title"></Form.Item> */}
          <Form.Item
            label="活动标题"
            name="title"
            className={styles.formItem}
            shouldUpdate
            rules={[
              { required: true },
              {
                validator: (_, value) =>
                  validateLocaleInputGroup(value, titleInputPropList),
              },
            ]}
          >
            <LocaleInputGroup localeInputList={titleInputPropList} />
          </Form.Item>
          <Form.Item
            label="活动描述"
            name="description"
            className={styles.formItem}
            // rules={[{ required: true, message: '请输入活动描述' }]}
            rules={[
              { required: true },
              {
                validator: (_, value) =>
                  validateLocaleInputGroup(value, descriptionPropList),
              },
            ]}
          >
            <LocaleInputGroup localeInputList={descriptionPropList} />
          </Form.Item>
          <Form.Item
            label="活动规则"
            name="rule"
            className={styles.formItem}
            rules={[
              { required: true },
              {
                validator: (_, value) =>
                  validateLocaleInputGroup(value, ruleInputPropList),
              },
            ]}
          >
            <LocaleInputGroup localeInputList={ruleInputPropList} />
          </Form.Item>
        </ProCard>

        <ProCard title="任务配置" style={{ marginBottom: 16 }}>
          <Form.List name="taskList">
            {(fields, { add: addTask, remove: removeTask }) => (
              <>
                {fields.map((field, index) => (
                  <ProCard
                    key={field.key}
                    className={styles.taskCard}
                    headStyle={{ width: '100%' }}
                    title={
                      <div
                        style={{
                          width: '100%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                        }}
                      >
                        <div>创建任务{arabicToChinese(index + 1)} </div>
                        <div>
                          {fields.length > 1 && !isEditing ? (
                            <Button
                              type="primary"
                              danger
                              icon={<DeleteOutlined />}
                              onClick={() => {
                                removeTask(index);
                                uploadAccecpt[index] = '';
                              }}
                            >
                              删除任务
                            </Button>
                          ) : null}
                        </div>
                      </div>
                    }
                  >
                    <Form.Item
                      name={[field.name, 'name']}
                      label="任务名称"
                      rules={[{ required: true, message: '请输入任务名称' }]}
                    >
                      <Input
                        placeholder="请输入任务名称"
                        disabled={isEditing}
                      />
                    </Form.Item>
                    <Form.Item
                      name={[field.name, 'title']}
                      label="任务标题"
                      rules={[
                        { required: true },
                        {
                          validator: (_, value) =>
                            validateLocaleInputGroup(
                              value,
                              getTaskTitleInputPropList({
                                disabled: isEditing,
                              }),
                            ),
                        },
                      ]}
                    >
                      <LocaleInputGroup
                        localeInputList={getTaskTitleInputPropList({})}
                      ></LocaleInputGroup>
                    </Form.Item>
                    <Form.Item
                      name={[field.name, 'description']}
                      label="任务描述"
                      rules={[
                        { required: true },
                        {
                          validator: (_, value) =>
                            validateLocaleInputGroup(
                              value,
                              getTaskDescriptionInputPropList({}),
                            ),
                        },
                      ]}
                    >
                      <LocaleInputGroup
                        localeInputList={getTaskDescriptionInputPropList({})}
                      ></LocaleInputGroup>
                    </Form.Item>
                    <Form.Item
                      name={[field.name, 'property']}
                      label="任务属性"
                      rules={[{ required: true, message: '请选择任务属性' }]}
                    >
                      <Select
                        placeholder="请选择任务属性"
                        options={taskPropertyOptions}
                        disabled={isEditing}
                      ></Select>
                    </Form.Item>
                    <Form.Item
                      name={[field.name, 'demoList']}
                      label="任务示例"
                      rules={[{ required: true, message: '请上传任务示例' }]}
                      extra={
                        <>
                          <InfoCircleFilled
                            size={20}
                            style={{
                              color: '#518be3',
                              marginRight: 8,
                            }}
                          />
                          图片尺寸为311px*175px；视频尺寸为310px*174px
                        </>
                      }
                      getValueFromEvent={normFile}
                      valuePropName="fileList"
                    >
                      <Uploader
                        sizeList={[
                          [311, 175],
                          [310, 174],
                        ]}
                        accept={uploadAccecpt[index]}
                      />
                    </Form.Item>
                    <Form.List name={[field.name, 'optionList']}>
                      {(
                        optionFields,
                        { add: addOption, remove: removeOption },
                      ) => (
                        <>
                          {optionFields.map((optionField, optionIndex) => (
                            <ProCard
                              key={optionField.key}
                              title={
                                <div
                                  style={{
                                    width: '100%',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'space-between',
                                  }}
                                >
                                  <div>
                                    任务选项{arabicToChinese(optionIndex + 1)}{' '}
                                  </div>
                                  <div>
                                    {optionFields.length > 1 && !isEditing ? (
                                      <Button
                                        type="primary"
                                        danger
                                        icon={<DeleteOutlined />}
                                        onClick={() =>
                                          removeOption(optionIndex)
                                        }
                                      >
                                        删除任务选项
                                      </Button>
                                    ) : null}
                                  </div>
                                </div>
                              }
                            >
                              <Row gutter={16}>
                                <Col span={22}>
                                  <Form.Item
                                    name={[optionField.name, 'title']}
                                    label="标题"
                                    rules={[
                                      { required: true },
                                      {
                                        validator: (_, value) =>
                                          validateLocaleInputGroup(
                                            value,
                                            getTaskTitleInputPropList({
                                              disabled: isEditing,
                                            }),
                                          ),
                                      },
                                    ]}
                                  >
                                    <LocaleInputGroup
                                      localeInputList={getTaskTitleInputPropList(
                                        {
                                          disabled: isEditing,
                                        },
                                      )}
                                    />
                                  </Form.Item>
                                  <Form.List
                                    {...field}
                                    name={[optionField.name, 'labelList']}
                                  >
                                    {(
                                      labelFields,
                                      { add: addLabel, remove: removeLabel },
                                    ) => (
                                      <>
                                        {labelFields.map(
                                          (labelField, labelIndex) => (
                                            <Row
                                              key={labelField.key}
                                              gutter={16}
                                            >
                                              <Col span={20}>
                                                <Form.Item
                                                  name={[
                                                    labelField.name,
                                                    'label',
                                                  ]}
                                                  labelCol={{
                                                    ...layout.labelCol,
                                                    offset: 2,
                                                  }}
                                                  label={`标签${arabicToChinese(
                                                    labelIndex + 1,
                                                  )}`}
                                                  rules={[
                                                    {
                                                      required: true,
                                                    },
                                                    {
                                                      validator: (_, value) =>
                                                        validateLocaleInputGroup(
                                                          value,
                                                          getTaskOptionLabelPropList(
                                                            {
                                                              disabled:
                                                                isEditing,
                                                            },
                                                          ),
                                                        ),
                                                    },
                                                  ]}
                                                >
                                                  <LocaleInputGroup
                                                    localeInputList={getTaskOptionLabelPropList(
                                                      { disabled: isEditing },
                                                    )}
                                                  />
                                                </Form.Item>
                                              </Col>
                                              {labelFields.length > 1 &&
                                              !isEditing ? (
                                                <Col span={4}>
                                                  <Button
                                                    type="primary"
                                                    danger
                                                    icon={<DeleteOutlined />}
                                                    onClick={() =>
                                                      removeLabel(labelIndex)
                                                    }
                                                  >
                                                    删除
                                                  </Button>
                                                </Col>
                                              ) : null}
                                            </Row>
                                          ),
                                        )}
                                        {labelFields.length < 20 &&
                                        !isEditing ? (
                                          <Form.Item
                                            labelCol={{ span: 0 }}
                                            wrapperCol={{ offset: 4, span: 22 }}
                                          >
                                            <Button
                                              size="large"
                                              icon={<PlusOutlined />}
                                              style={{ width: '100%' }}
                                              onClick={() => {
                                                addTaskOptionLabelInfo(
                                                  addLabel,
                                                );
                                              }}
                                              type="primary"
                                            >
                                              新增选项标签
                                            </Button>
                                          </Form.Item>
                                        ) : null}
                                      </>
                                    )}
                                  </Form.List>
                                </Col>
                              </Row>
                            </ProCard>
                          ))}
                          {optionFields.length < 20 && !isEditing ? (
                            <Form.Item wrapperCol={{ span: 24 }}>
                              <Button
                                type="primary"
                                size="large"
                                style={{ width: '100%' }}
                                icon={<PlusOutlined />}
                                onClick={() => addTaskOptionInfo(addOption)}
                              >
                                创建任务选项
                              </Button>
                            </Form.Item>
                          ) : null}
                        </>
                      )}
                    </Form.List>
                  </ProCard>
                ))}
                {fields.length < 20 && !isEditing ? (
                  <Button
                    style={{ width: '100%' }}
                    type="primary"
                    size="large"
                    icon={<PlusOutlined />}
                    onClick={() => addTaskInfo(addTask)}
                  >
                    创建任务
                  </Button>
                ) : null}
              </>
            )}
          </Form.List>
        </ProCard>

        <ProCard title="活动奖励" style={{ marginBottom: 16 }}>
          <Form.Item
            label={
              <>
                <span className="required-red" style={{ marginRight: 4 }}>
                  *
                </span>
                活动Plan
              </>
            }
            rules={[
              {
                validator: async () => {
                  if (!associateSkuList || !associateSkuList.length) {
                    return Promise.reject(new Error('请选择一个云存Plan'));
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            {!isEditing ? (
              <div>
                <Button
                  icon={<PlusOutlined />}
                  type="link"
                  htmlType="button"
                  onClick={showProductSelector}
                  style={{ marginBottom: 16 }}
                >
                  去选择Plan
                </Button>
                <br />
                <>
                  <InfoCircleFilled
                    size={20}
                    style={{
                      color: '#518be3',
                      marginRight: 8,
                    }}
                  />
                  请选择能力最高的Plan
                </>
              </div>
            ) : null}
            {associateSkuList && associateSkuList.length ? (
              <Table<ProductSku>
                rowKey="id"
                columns={(
                  associateSkuTableColumns as ColumnsType<ProductSku>
                ).concat([
                  {
                    title: '状态',
                    dataIndex: 'saleStatus',
                    fixed: 'right',
                    width: 100,
                    render: (_, row) =>
                      row.saleStatus ? (
                        <p style={{ color: '#3199F5' }}>上架</p>
                      ) : (
                        '下架'
                      ),
                  },
                ])}
                scroll={{
                  x: associateSkuTableColumns
                    .filter((col) => col.dataIndex === 'action')
                    .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
                }}
                dataSource={associateSkuList}
                pagination={false}
              />
            ) : null}
          </Form.Item>
          <Form.Item
            label="活动Plan名称"
            name="planName"
            rules={[
              {
                validator: (_, value) =>
                  validateLocaleInputGroup(value, getPlanNameInputPropList({})),
              },
            ]}
          >
            <LocaleInputGroup localeInputList={getPlanNameInputPropList({})} />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="服务时长"
                name="duration"
                labelCol={{ span: 6 }}
                wrapperCol={{ span: 14 }}
                rules={[
                  {
                    required: true,
                    message: '请输入服务时长(大于0的整数)',
                  },
                ]}
              >
                <InputNumber
                  min={1}
                  placeholder="请输入服务时长(大于0的整数)"
                  disabled={isEditing}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name="unit"
                rules={[{ required: true, message: '请选择时长单位' }]}
              >
                <Radio.Group disabled={isEditing}>
                  <Radio
                    key={AwardDurationUnitEnum.MONTH}
                    value={AwardDurationUnitEnum.MONTH}
                  >
                    月
                  </Radio>
                  <Radio
                    key={AwardDurationUnitEnum.DAY}
                    value={AwardDurationUnitEnum.DAY}
                  >
                    天
                  </Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
          </Row>
        </ProCard>
        <ProCard>
          <Form.Item wrapperCol={{ span: 24 }} style={{ textAlign: 'center' }}>
            <Button
              type="primary"
              htmlType="submit"
              style={{ marginRight: 16 }}
              loading={loading}
              disabled={loading}
            >
              提交
            </Button>
            <Button type="default" onClick={history.back}>
              取消
            </Button>
          </Form.Item>
        </ProCard>
      </Form>
      {showSkuSelector ? (
        <SkuSelector
          param={associateSkuListParam}
          open
          onOk={addSkuInfo}
          onCancel={() => setShowSkuSelector(false)}
        />
      ) : null}
    </>
  );
};

export default Edit;
