import {
  fetchAiCreationDeletion,
  fetchAiCreationList,
  fetchAiCreeationStatusUpdate,
} from '@/models/aiCreation/fetch';
import { AiCreation, AiCreationListParam } from '@/models/aiCreation/interface';
import { initAiCreationListParam } from '@/models/aiCreation/util';
import {
  ApiSuccessEnum,
  DeviceTypeEnum,
  StatusEnum,
} from '@/models/common.interface';
import { postMessageFunction, spanConfig } from '@/models/common.util';
import { Pagination, initPagination } from '@/utils/request';
import useUrlState from '@ahooksjs/use-url-state';
import { PlusOutlined } from '@ant-design/icons';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { Button, Popconfirm, Space, Switch, message } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useRef, useState } from 'react';
import { AiCollcaboartionTableForm, UrlParam } from '../interface';
import {
  transferTableFormDataToUrlParam,
  transferUrlParamToListParam,
  transferUrlParamToTableFormData,
} from '../util';

const List: React.FC = () => {
  const [urlParam] = useUrlState<UrlParam>();
  const [dataList, setDataList] = useState<AiCreation[]>([]);
  const [listParam, setListParam] = useState<AiCreationListParam>();
  const [pagination, setPagination] = useState<Pagination>(initPagination);
  const formRef = useRef<ProFormInstance<AiCollcaboartionTableForm>>();

  // 获取列表数据
  const requestAiCreationList = async (
    param: AiCreationListParam = initAiCreationListParam,
  ) => {
    const { items, ...rest } = await fetchAiCreationList(param);
    setPagination(rest);
    setDataList(items);
  };

  // 编辑
  const editAiCollcaboartion = (id: number) => {
    history.push(`/activity/ai-collaboration/edit/${id}`);
  };

  // 切换状态
  const switchStatus = async (ev: boolean, record: AiCreation) => {
    try {
      const result = await fetchAiCreeationStatusUpdate(
        record.id,
        +ev as StatusEnum,
      );
      if (result === ApiSuccessEnum.success) {
        message.success(`${ev ? '上架' : '下架'}成功!`);
        setListParam({ ...initAiCreationListParam, ...listParam });
      }
    } catch (error) {
      console.log(error);
    }
  };

  // 跳转审核页面
  const gotoApplyList = (record: AiCreation) => {
    postMessageFunction({
      type: 'redirect',
      content: {
        redirectUrl: '/activity/aiCollaboration/reviewList',
        param: {
          deviceType: record.deviceType,
          id: record.id,
        } as unknown as Record<string, string>,
      },
    });
  };

  // 删除
  const deleteAiCollcaboartion = async (id: number) => {
    const result = await fetchAiCreationDeletion(id);
    if (result === ApiSuccessEnum.success) {
      message.success('删除成功!');
      setListParam({ ...initAiCreationListParam, ...listParam });
    }
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          if (!form) return;
          const formData: AiCollcaboartionTableForm = form.getFieldsValue();
          const param = transferTableFormDataToUrlParam(formData);
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: `/activity/aiCollaboration/list`,
              param: param as unknown as Record<string, string>,
            },
          });
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          form?.resetFields();
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: `/activity/aiCollaboration/list`,
            },
          });
        }}
      >
        重置
      </Button>,
    ];
  };

  const onPaginationChanged = (page: number, pageSize: number) => {
    let index = page;
    if (pageSize !== listParam?.limit) {
      index = 1;
    }
    setListParam({
      ...listParam,
      limit: pageSize,
      offset: (index - 1) * pageSize,
    });
  };

  const columns: Array<ProColumns<AiCreation>> = [
    {
      title: '适用设备',
      dataIndex: 'deviceType',
      valueType: 'select',
      width: 100,
      valueEnum: {
        [DeviceTypeEnum.D4sh]: 'D4sh',
        [DeviceTypeEnum.D4H]: 'D4h',
        [DeviceTypeEnum.T5]: 'T5',
        [DeviceTypeEnum.T6]: 'T6',
        [DeviceTypeEnum.T7]: 'T7',
      },
    },
    {
      title: '活动名称',
      dataIndex: 'title',
      width: 200,
      render: (_, record) => {
        let title = '';
        try {
          const titleInfo = JSON.parse(record.title);
          title = titleInfo.zh_CN;
          return title;
        } catch (error) {
          console.log(error);
        }
      },
    },
    { title: '任务数', width: 80, dataIndex: 'taskCount', search: false },
    {
      title: '参与人数',
      width: 100,
      dataIndex: 'participateCount',
      search: false,
    },
    { title: '领取人数', width: 100, dataIndex: 'receiveCount', search: false },
    {
      title: '活动时间',
      dataIndex: 'activityTime',
      valueType: 'dateRange',
      width: 150,
      render: (_, record) => {
        if (record.startTime === -1) {
          return '永久';
        }
        return `${dayjs(record.startTime).format(
          'YYYY-MM-DD HH:mm:ss',
        )} ~ ${dayjs(record.endTime).format('YYYY-MM-DD HH:mm:ss')}`;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      valueType: 'dateRange',
      width: 150,
      render: (_, record) =>
        dayjs(record.createTime).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      search: false,
      render: (_, record) => (
        <Switch
          checked={!!record.status}
          checkedChildren="启用"
          unCheckedChildren="禁用"
          onChange={(ev) => switchStatus(ev, record)}
        />
      ),
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 150,
      search: false,
      fixed: 'right',
      render: (_, record) => (
        <Space split="|">
          {record.status === StatusEnum.DISABLE ? (
            <>
              <Button
                type="link"
                onClick={() => editAiCollcaboartion(record.id)}
                style={{ paddingInline: 0 }}
              >
                编辑
              </Button>
              <Popconfirm
                title="确认删除吗？"
                onConfirm={() => deleteAiCollcaboartion(record.id)}
              >
                <Button type="link" style={{ paddingInline: 0 }}>
                  删除
                </Button>
              </Popconfirm>
            </>
          ) : null}
          <Button
            type="link"
            onClick={() => gotoApplyList(record)}
            style={{ paddingInline: 0 }}
          >
            详情
          </Button>
        </Space>
      ),
    },
  ];

  useEffect(() => {
    if (!listParam) return;
    requestAiCreationList(listParam);
  }, [listParam]);

  useEffect(() => {
    const form = formRef.current;
    const formData = transferUrlParamToTableFormData(urlParam);
    form?.setFieldsValue(formData);

    const listParam = transferUrlParamToListParam(urlParam);
    setListParam(listParam);
  }, [urlParam]);

  return (
    <ProTable<AiCreation>
      dataSource={dataList}
      columns={columns}
      defaultSize="small"
      rowKey="id"
      formRef={formRef}
      search={{
        defaultCollapsed: false,
        span: spanConfig,
        optionRender: searchOptionRender,
      }}
      options={{ reload: () => listParam && setListParam({ ...listParam }) }}
      scroll={{
        x: columns
          .filter((col) => col.dataIndex !== 'action')
          .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
      }}
      toolbar={{
        actions: [
          <Button
            key="button"
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => editAiCollcaboartion(0)}
          >
            新增
          </Button>,
        ],
      }}
      pagination={{
        pageSize: pagination.limit,
        total: pagination.total,
        showQuickJumper: true,
        onChange: onPaginationChanged,
      }}
    />
  );
};

export default List;
