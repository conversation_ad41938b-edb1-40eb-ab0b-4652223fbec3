import RedirectLink from '@/components/RedirectLink';
import UserIdLink from '@/components/TableRowLink/UserIdLink';
import { postMessageFunction } from '@/models/common.util';
import {
  fetchFamilyGroupListByUserId,
  fetchFamilyList,
} from '@/models/family/fetch';
import { FamilyGroup, FamilyGroupListParam } from '@/models/family/interface';
import { antdUtils } from '@/utils/antd.util';
import global from '@/utils/global';
import { Pagination, initPagination } from '@/utils/request';
import useUrlState from '@ahooksjs/use-url-state';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProTable,
} from '@ant-design/pro-components';
import { Button, FormInstance } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { FamilyListForm, FamilyListUrlParam } from '../interface';
import { initialFamilyListParam } from '../util';

const List: React.FC = () => {
  const [urlParam] = useUrlState<FamilyListUrlParam>({});
  const formRef = useRef<FormInstance<FamilyListForm>>();
  const [dataList, setDataList] = useState<FamilyGroup[]>([]);
  const [listParam, setListParam] = useState<FamilyGroupListParam>();
  const [pagination, setPagination] = useState<Pagination>(initPagination);

  // 获取列表数据
  const requestFamilyList = async (_listParam: FamilyGroupListParam) => {
    const { items, ...rest } = await fetchFamilyList(_listParam);
    setDataList(items);
    setPagination(rest);
  };

  const requestFamilyListByUserId = async (userId: string) => {
    const list = await fetchFamilyGroupListByUserId(userId);
    setDataList(list);
    setPagination({
      limit: 10,
      offset: 0,
      total: list.length,
    });
  };

  // 编辑
  // const editFamily = (id: number) => {
  //   history.push(`/edit/${id}`);
  // };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          const formData = form?.getFieldsValue();
          const _urlParam: FamilyListUrlParam = {};
          if (formData.name) _urlParam.name = formData.name;
          if (formData.groupId) _urlParam.groupId = formData.groupId;

          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: `/user/family${global.getUrlParams(_urlParam)}`,
            },
          });
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: '/user/family',
            },
          });
        }}
      >
        重置
      </Button>,
    ];
  };

  const onPaginationChange = (page: number, pageSize: number) => {
    if (!listParam || urlParam.userId) return;

    setListParam({
      ...listParam,
      ...antdUtils.getPaginatorParamByTablePaginationChange(
        listParam,
        page,
        pageSize,
      ),
    });
  };

  const columns: Array<ProColumns<FamilyGroup>> = [
    {
      title: '家庭ID',
      dataIndex: 'groupId',
    },
    {
      title: '家庭名称',
      dataIndex: 'name',
    },
    {
      title: '创建者',
      dataIndex: 'owner',
      search: false,
      render: (_, row) => <UserIdLink userId={`${row.owner}`} />,
    },
    {
      title: '家庭成员（人）',
      dataIndex: 'userCount',
      search: false,
      render: (_, row) => (
        <RedirectLink
          text={row.userCount}
          linkUrl="/user/users"
          params={{
            groupId: row.groupId,
          }}
        />
      ),
    },
    {
      title: '家庭设备（台）',
      dataIndex: 'deviceCount',
      search: false,
      render: (_, row) => (
        <RedirectLink
          text={row.deviceCount}
          linkUrl="/user/linked_devices"
          params={{
            groupId: row.groupId,
          }}
        />
      ),
    },
    {
      title: '家庭宠物（只）',
      dataIndex: 'petCount',
      search: false,
      render: (_, row) => (
        <RedirectLink
          text={row.petCount}
          linkUrl="/pet/pets"
          params={{
            groupId: row.groupId,
          }}
        />
      ),
    },
  ];

  useEffect(() => {
    if (urlParam.userId) {
      requestFamilyListByUserId(urlParam.userId);
      return;
    }
    const form = formRef.current;
    console.log(urlParam, initialFamilyListParam(urlParam));
    form?.setFieldsValue(initialFamilyListParam(urlParam));
    setListParam({ ...initialFamilyListParam(urlParam) });
  }, [urlParam]);

  useEffect(() => {
    if (listParam) requestFamilyList(listParam);
  }, [listParam]);

  return (
    <ProTable<FamilyGroup>
      dataSource={dataList}
      columns={columns}
      defaultSize="small"
      rowKey="groupId"
      formRef={formRef}
      search={{
        defaultCollapsed: false,
        span: 6,
        optionRender: searchOptionRender,
      }}
      scroll={{
        x: columns
          .filter((col) => col.dataIndex === 'action')
          .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
      }}
      pagination={{
        ...antdUtils.transferPaginationToTablePagination(pagination),
        onChange: onPaginationChange,
      }}
    />
  );
};

export default List;
