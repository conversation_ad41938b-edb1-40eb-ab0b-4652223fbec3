import { FamilyGroupListParam } from '@/models/family/interface';
import { initFamilyGroupListParam } from '@/models/family/util';
import { FamilyListUrlParam } from './interface';
// import { FamilyForm } from './interface';

// export const initialFamilyForm: FamilyForm = {};
export const initialFamilyListParam = (urlParam: FamilyListUrlParam): FamilyGroupListParam => {
  const param: FamilyGroupListParam = {
    ...initFamilyGroupListParam,
  };
  urlParam.groupId && (param.groupId = +(urlParam.groupId || 0));
  urlParam.name && (param.name = urlParam.name);
  return param;
};

// 将formData转换为param
// export const transferFormDataToParam = (formData: FamilyForm, id?: number): FamilyParam => {
//   const param: FamilyParam = {};
//   id && (param.id = id);
//   return param;
// };

// 将接口返回的详情数据转换为formData
// export const transferDetailToFormData = (detail: FamilyDetail): FamilyForm => {
//   const formData: FamilyForm = {};
//   return formData;
// };
