import { ApiSuccessEnum } from '@/models/common.interface';
import ProCard from '@ant-design/pro-components';
import { history, useDispatch, useParams } from '@umijs/max';
import { Button, Form, Input, message } from 'antd';
import React, { useEffect, useState } from 'react';
import { FamilyForm } from '../interface';
import {
  initialFamilyForm,
  transferDetailToFormData,
  transferFormDataToParam,
} from '../util';

const Edit: React.FC = () => {
  const dispatch = useDispatch();
  const param = useParams<{ id: string }>();
  const [form] = Form.useForm<FamilyForm>();
  const [loading, setLoading] = useState(false);
  const layout = {
    labelCol: { span: 3 },
    wrapperCol: { span: 20 },
  };

  useEffect(() => {
    if (param && +param.id) {
      requestDetailById(+param.id);
    }
  }, [param]);

  useEffect(() => {
    // 额外dva接口请求
  }, [dispatch]);

  // 根据id获取详情数据
  const requestDetailById = async (id: number) => {
    const detail = await fetchFamilyDetail(id);
    const formData = transferDetailToFormData(detail);
    form.setFieldsValue(formData);
  };

  // 提交form表单
  const submit = async (formData: FamilyForm) => {
    setLoading(true);
    const _param = transferFormDataToParam(formData, +param?.id || 0);
    let result = '';
    let state = '';
    try {
      if (param && +param.id) {
        state = '更新';
        result = await fetchUpdateFamily(_param);
      } else {
        state = '创建';
        result = await fetchCreateFamily(_param);
      }

      if (result === ApiSuccessEnum.success) {
        message.success(`${state}成功！`);
        history.back();
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <ProCard>
      <Form
        {...layout}
        form={form}
        onFinish={submit}
        initialValues={initialFamilyForm}
      >
        <Form.Item
          name="name"
          label="名称"
          rules={[{ required: true, message: '请输入名称' }]}
        >
          <Input showCount maxLength={40} placeholder="请输入名称" />
        </Form.Item>
        <Form.Item style={{ textAlign: 'right' }}>
          <Button
            type="primary"
            htmlType="submit"
            style={{ marginRight: 16 }}
            loading={loading}
            disabled={loading}
          >
            提交
          </Button>
          <Button htmlType="reset" style={{ marginRight: 16 }} danger>
            重置
          </Button>
          <Button type="default" onClick={history.back}>
            取消
          </Button>
        </Form.Item>
      </Form>
    </ProCard>
  );
};

export default Edit;
