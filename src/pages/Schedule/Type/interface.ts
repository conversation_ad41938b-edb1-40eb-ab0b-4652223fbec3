import { LocaleContentForm } from '@/components/LocaleContentModal/interface';
import { TimeUnitEnum } from '@/models/schedule/interface';
import { UploadFile } from 'antd';

export interface ScheduleTypeForm {
  images: Array<UploadFile<string>>;
  withDevice: string;
  withPet: boolean;
  scheduleAppoint: string;
  priority: number;
  isCustom: boolean;
  enable: boolean;
  // 重复提醒单位
  repeatRemindUnitList: TimeUnitEnum[];
  hasDefaultRepeatRemind: boolean;
  defaultRepeatRemindUnit: TimeUnitEnum;
  repeatRemindDuration: number | null;
}

export interface ScheduleTypeLocalePropForm extends LocaleContentForm {
  name: string;
  // 提醒消息
  msg0: string;
  // 过期消息 第一天
  msg1: string;
  // 过期消息 第二天
  msg2: string;
  // 过期消息 第三天
  msg3: string;
}
