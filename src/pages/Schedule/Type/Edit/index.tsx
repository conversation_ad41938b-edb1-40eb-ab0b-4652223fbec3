import LocaleContentModal from '@/components/LocaleContentModal';
import Uploader from '@/components/Uploader';
import { ApiSuccessEnum, SelectOption } from '@/models/common.interface';
import { ConnectState } from '@/models/connect';
import {
  fetchScheduleTypeDetail,
  fetchScheduleTypeSaving,
} from '@/models/schedule/fetch';
import { TimeUnitEnum } from '@/models/schedule/interface';
import { timeUnitEnumObj } from '@/models/schedule/util';
import { PlusOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import {
  Dispatch,
  history,
  useDispatch,
  useParams,
  useSelector,
} from '@umijs/max';
import {
  Button,
  Checkbox,
  Col,
  Form,
  Input,
  InputNumber,
  List,
  message,
  Radio,
  Row,
  Switch,
  Typography,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { ScheduleTypeForm, ScheduleTypeLocalePropForm } from '../interface';
import {
  initScheduleTypeForm,
  initScheduleTypeLocalePropForm,
  transferDetailToFormData,
  transferFormDataToParam,
  transferLocaleContent,
} from '../util';

const defaultRepeatDurationErrorMessage: { [key in TimeUnitEnum]: string } = {
  [TimeUnitEnum.DAY]: '请输入1-30之间的整数',
  [TimeUnitEnum.WEEK]: '请输入1-4之间的整数',
  [TimeUnitEnum.MONTH]: '请输入1-12之间的整数',
  [TimeUnitEnum.YEAR]: '请输入任意数字',
};

const defaultRepeatDurationMax: { [key in TimeUnitEnum]: number } = {
  [TimeUnitEnum.DAY]: 30,
  [TimeUnitEnum.WEEK]: 4,
  [TimeUnitEnum.MONTH]: 12,
  [TimeUnitEnum.YEAR]: Number.MAX_SAFE_INTEGER,
};

const Edit: React.FC = () => {
  const dispatch: Dispatch = useDispatch();
  const param = useParams<{ id: string }>();
  const [form] = Form.useForm<ScheduleTypeForm>();
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [selectedLocaleContent, setSelectedLocaleContent] =
    useState<ScheduleTypeLocalePropForm>();
  const [localeContentList, setLocaleContentList] = useState<
    ScheduleTypeLocalePropForm[]
  >([]);
  const [maxValue, setMaxValue] = useState(
    defaultRepeatDurationMax[initScheduleTypeForm.defaultRepeatRemindUnit],
  );
  const localeOptions: SelectOption[] = useSelector(
    ({ app }: ConnectState) => app.localeOptions,
  );
  const layout = {
    labelCol: { span: 20, offset: 2 },
    wrapperCol: { span: 20, offset: 2 },
  };
  const repeatRemindUnitList = Form.useWatch('repeatRemindUnitList', form);
  const hasDefaultRepeatRemind = Form.useWatch('hasDefaultRepeatRemind', form);
  const defaultRepeatRemindUnit = Form.useWatch(
    'defaultRepeatRemindUnit',
    form,
  );

  // 根据id获取详情数据
  const requestDetailById = async (id: string) => {
    const detail = await fetchScheduleTypeDetail(id);
    const formData = transferDetailToFormData(detail);
    form.setFieldsValue(formData);
    const _localeContentList = transferLocaleContent(detail);
    setLocaleContentList(_localeContentList);
  };

  // 提交form表单
  const submit = async (formData: ScheduleTypeForm) => {
    setLoading(true);
    const _param = transferFormDataToParam(
      formData,
      localeContentList,
      param?.id,
    );
    let result = '';
    let state = '';
    try {
      if (param && param.id && +param.id) {
        state = '更新';
      } else {
        state = '创建';
      }
      result = await fetchScheduleTypeSaving(_param);

      if (result === ApiSuccessEnum.success) {
        message.success(`${state}成功！`);
        history.back();
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  // 处理上传文件格式
  const normFile = (e: any) => {
    // console.log('Upload event:', e);
    if (Array.isArray(e)) {
      return e;
    }
    return e && e.fileList;
  };

  // 编辑本地化内容
  const editLocaleContent = (localeContent?: ScheduleTypeLocalePropForm) => {
    setSelectedLocaleContent(localeContent);
    setVisible(true);
  };

  // 删除本地化内容
  const deleteLocaleContent = (index: number) => {
    localeContentList.splice(index, 1);
    setLocaleContentList([...localeContentList]);
  };

  const hideUserModal = () => {
    setVisible(false);
    setSelectedLocaleContent(undefined);
  };

  const setLocaleContent = (formValue: ScheduleTypeLocalePropForm) => {
    const existedLocaleContent: ScheduleTypeLocalePropForm | undefined =
      localeContentList.find((item) => item.language === formValue.language);
    if (existedLocaleContent) {
      existedLocaleContent.name = formValue.name;
      existedLocaleContent.msg0 = formValue.msg0;
      existedLocaleContent.msg1 = formValue.msg1;
      existedLocaleContent.msg2 = formValue.msg2;
      existedLocaleContent.msg3 = formValue.msg3;
      setLocaleContentList([...localeContentList]);
    } else {
      setLocaleContentList([...localeContentList, formValue]);
    }
  };

  // 重置
  const resetForm = () => {
    if (!param || !param.id || !+param.id) {
      form.resetFields(['pageType']);
    }
    form.resetFields(['priority']);
    setLocaleContentList([]);
  };

  useEffect(() => {
    if (+(param.id || 0)) requestDetailById(param.id || '0');
  }, [param]);

  useEffect(() => {
    // 额外dva接口请求
    dispatch({ type: 'app/requestLocaleList' });
  }, [dispatch]);

  useEffect(() => {
    setMaxValue(defaultRepeatDurationMax[defaultRepeatRemindUnit]);
  }, [defaultRepeatRemindUnit]);

  return (
    <>
      <ProCard>
        <Form
          {...layout}
          layout="vertical"
          form={form}
          onFinish={submit}
          onFinishFailed={({ errorFields }) =>
            errorFields.length && form.scrollToField(errorFields[0].name)
          }
          initialValues={initScheduleTypeForm}
        >
          <Form.Item
            name="images"
            label="图片"
            getValueFromEvent={normFile}
            shouldUpdate
            valuePropName="fileList"
            rules={[{ required: true, message: '请选择图片' }]}
          >
            <Uploader />
          </Form.Item>
          <Form.Item
            label="重复提醒"
            name="repeatRemained"
            required
            style={{ marginBottom: 0 }}
          >
            <Form.Item style={{ marginBottom: 4 }}>
              <Checkbox
                indeterminate={
                  (repeatRemindUnitList || []).length > 0 &&
                  (repeatRemindUnitList || []).length < 4
                }
                checked={(repeatRemindUnitList || []).length >= 4}
                onChange={({ target: { checked } }) =>
                  checked
                    ? form.setFieldValue(
                        ['repeatRemindUnitList'],
                        [
                          TimeUnitEnum.DAY,
                          TimeUnitEnum.WEEK,
                          TimeUnitEnum.MONTH,
                          TimeUnitEnum.YEAR,
                        ],
                      )
                    : form.resetFields(['repeatRemindUnitList'])
                }
              >
                全选
              </Checkbox>
            </Form.Item>
            <Form.Item
              name="repeatRemindUnitList"
              rules={[{ required: true, message: '请选择至少一个提醒周期' }]}
            >
              <Checkbox.Group>
                <Checkbox value={TimeUnitEnum.DAY}>
                  {timeUnitEnumObj[TimeUnitEnum.DAY]}
                </Checkbox>
                <Checkbox value={TimeUnitEnum.WEEK}>
                  {timeUnitEnumObj[TimeUnitEnum.WEEK]}
                </Checkbox>
                <Checkbox value={TimeUnitEnum.MONTH}>
                  {timeUnitEnumObj[TimeUnitEnum.MONTH]}
                </Checkbox>
                <Checkbox value={TimeUnitEnum.YEAR}>
                  {timeUnitEnumObj[TimeUnitEnum.YEAR]}
                </Checkbox>
              </Checkbox.Group>
            </Form.Item>
          </Form.Item>
          <Form.Item label="默认重复周期" style={{ marginBottom: 0 }}>
            <Form.Item name="hasDefaultRepeatRemind" valuePropName="checked">
              <Switch checkedChildren="开启" unCheckedChildren="关闭" />
            </Form.Item>
            {hasDefaultRepeatRemind ? (
              <Row gutter={16}>
                <Col span={16}>
                  <Form.Item
                    name="repeatRemindDuration"
                    rules={[
                      {
                        required: true,
                        message:
                          defaultRepeatDurationErrorMessage[
                            defaultRepeatRemindUnit
                          ],
                      },
                    ]}
                  >
                    <InputNumber
                      min={1}
                      max={maxValue}
                      placeholder={
                        defaultRepeatDurationErrorMessage[
                          defaultRepeatRemindUnit
                        ]
                      }
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name="defaultRepeatRemindUnit">
                    <Radio.Group
                      onChange={({ target: { value } }) =>
                        value === TimeUnitEnum.YEAR
                          ? form.setFieldValue(['repeatRemindDuration'], 1)
                          : form.setFieldValue(['repeatRemindDuration'], null)
                      }
                    >
                      <Radio value={TimeUnitEnum.DAY}>天</Radio>
                      <Radio value={TimeUnitEnum.WEEK}>周</Radio>
                      <Radio value={TimeUnitEnum.MONTH}>月</Radio>
                      <Radio value={TimeUnitEnum.YEAR}>年</Radio>
                    </Radio.Group>
                  </Form.Item>
                </Col>
              </Row>
            ) : (
              ''
            )}
          </Form.Item>
          <Form.Item
            name="withDevice"
            label="关联设备类型(-1:不绑定 0:所有类型 设备类型id逗号隔开)"
          >
            <Input placeholder="请输入关联设备类型" />
          </Form.Item>
          <Form.Item label="关联宠物" name="withPet" valuePropName="checked">
            <Switch checkedChildren="是" unCheckedChildren="否" />
          </Form.Item>
          <Form.Item name="scheduleAppoint" label="约定提醒">
            <Input placeholder="请输入约定提醒" />
          </Form.Item>
          <Form.Item
            name="priority"
            label="优先级(0-99999，越高表示排在越前面)"
          >
            <InputNumber placeholder="请输入优先级" />
          </Form.Item>
          <Form.Item
            label="是否是自定义"
            name="isCustom"
            valuePropName="checked"
          >
            <Switch checkedChildren="是" unCheckedChildren="否" />
          </Form.Item>
          <Form.Item label="是否启用" name="enable" valuePropName="checked">
            <Switch checkedChildren="是" unCheckedChildren="否" />
          </Form.Item>
          <Form.Item wrapperCol={{ offset: 2 }}>
            <Button icon={<PlusOutlined />} onClick={() => editLocaleContent()}>
              本地化内容
            </Button>
          </Form.Item>
          <Form.Item wrapperCol={{ offset: 2 }}>
            <List<ScheduleTypeLocalePropForm>
              size="small"
              bordered={false}
              dataSource={localeContentList}
              renderItem={(item, index) => (
                <List.Item>
                  <div>
                    <p>
                      <Typography.Text strong>语言：</Typography.Text>
                      {localeOptions.find((opt) => opt.value === item.language)
                        ?.label || item.language}
                      <Button
                        type="link"
                        onClick={() => editLocaleContent(item)}
                      >
                        编辑
                      </Button>{' '}
                      <Button
                        type="link"
                        onClick={() => deleteLocaleContent(index)}
                      >
                        删除
                      </Button>
                    </p>
                    <p>
                      <Typography.Text strong>名称：</Typography.Text>
                      {item.name}
                    </p>
                    <p>
                      <Typography.Text strong>提醒消息：</Typography.Text>
                      {item.msg0}
                    </p>
                    <p>
                      <Typography.Text strong>
                        过期消息(第1天)：
                      </Typography.Text>
                      {item.msg1}
                    </p>
                    <p>
                      <Typography.Text strong>
                        过期消息(第2天)：
                      </Typography.Text>
                      {item.msg2}
                    </p>
                    <p>
                      <Typography.Text strong>
                        过期消息(第3天)：
                      </Typography.Text>
                      {item.msg3}
                    </p>
                  </div>
                </List.Item>
              )}
            />
          </Form.Item>
          <Form.Item style={{ textAlign: 'right' }}>
            <Button
              type="primary"
              htmlType="submit"
              style={{ marginRight: 16 }}
              loading={loading}
              disabled={loading}
            >
              提交
            </Button>
            <Button
              htmlType="reset"
              style={{ marginRight: 16 }}
              danger
              onClick={resetForm}
            >
              重置
            </Button>
            <Button type="default" onClick={history.back}>
              取消
            </Button>
          </Form.Item>
        </Form>
      </ProCard>
      <LocaleContentModal<ScheduleTypeLocalePropForm>
        visible={visible}
        localeOptionList={localeOptions}
        initialFormValues={initScheduleTypeLocalePropForm}
        detail={selectedLocaleContent}
        onCancel={hideUserModal}
        onConfirm={(fd) => {
          setLocaleContent(fd);
          hideUserModal();
        }}
      >
        <Form.Item
          name="name"
          label="名称"
          rules={[{ required: true, message: '请设置名称' }]}
        >
          <Input placeholder="请设置名称" />
        </Form.Item>
        <Form.Item
          name="msg0"
          label="提醒消息"
          rules={[{ required: true, message: '请设置提醒消息' }]}
        >
          <Input placeholder="请设置提醒消息" />
        </Form.Item>
        <Form.Item
          name="msg1"
          label="过期消息(第1天)"
          rules={[{ required: true, message: '请设置过期消息(第1天)' }]}
        >
          <Input placeholder="请设置过期消息(第1天)" />
        </Form.Item>
        <Form.Item
          name="msg2"
          label="过期消息(第2天)"
          rules={[{ required: true, message: '请设置过期消息(第2天)' }]}
        >
          <Input placeholder="请设置过期消息(第2天)" />
        </Form.Item>
        <Form.Item
          name="msg3"
          label="过期消息(第3天)"
          rules={[{ required: true, message: '请设置过期消息(第3天)' }]}
        >
          <Input placeholder="请设置过期消息(第3天)" />
        </Form.Item>
      </LocaleContentModal>
    </>
  );
};

export default Edit;
