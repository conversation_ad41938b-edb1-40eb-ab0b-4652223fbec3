import { ApiSuccessEnum, StatusEnum } from '@/models/common.interface';
import { getStatusMap, postMessageFunction } from '@/models/common.util';
import {
  fetchScheduleTypeEnable,
  fetchScheduleTypeList,
} from '@/models/schedule/fetch';
import {
  ScheduleType,
  ScheduleTypeListParam,
  TimeUnitEnum,
} from '@/models/schedule/interface';
import { timeUnitEnumObj } from '@/models/schedule/util';
import { getCurrentDomain } from '@/utils/currentDomain';
import { PlusOutlined } from '@ant-design/icons';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProTable,
} from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { Button, Image, Switch, message } from 'antd';
import React, { useEffect, useState } from 'react';

interface SearchForm {
  enable: StatusEnum;
}

const List: React.FC = () => {
  const [dataList, setDataList] = useState<ScheduleType[]>([]);
  const [listParam, setListParam] = useState<ScheduleTypeListParam>();

  // 获取列表数据
  const requestTypeList = async (param?: ScheduleTypeListParam) => {
    const list = await fetchScheduleTypeList(param);
    setDataList(list);
  };

  // 切换状态
  const switchStatus = async (
    ev: boolean,
    id: string,
    isCustom: StatusEnum,
  ) => {
    if (isCustom === StatusEnum.ENABLE && ev === false) {
      message.warning('自定义类型不允许禁用！');
      return;
    }
    try {
      const result = await fetchScheduleTypeEnable(id, +ev as StatusEnum);
      if (result === ApiSuccessEnum.success) {
        message.success(`${ev ? '启用' : '禁用'}成功!`);
        const index = [...dataList].findIndex((item) => item.id === id);
        dataList[index].enable = +ev as StatusEnum;
        setDataList([...dataList]);
      }
    } catch (error) {
      console.log(error);
    }
  };

  // 编辑
  const editType = (id: string) => {
    history.push(`/schedule/type/edit/${id}`);
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          const formData: SearchForm = form?.getFieldsValue();
          // const _param = transferFormDataToParam<any, TypeListParam>(form?.getFieldsValue());
          setListParam({
            enable: formData.enable,
          });
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          form?.resetFields();
          setListParam(undefined);
        }}
      >
        重置
      </Button>,
    ];
  };

  const columns: Array<ProColumns<ScheduleType>> = [
    {
      title: '名称',
      dataIndex: 'name',
      search: false,
      width: 100,
    },
    {
      title: '图片',
      dataIndex: 'img',
      width: 120,
      search: false,
      render: (_, row) => <Image src={row.img} width={80} height={80} />,
    },
    {
      title: '重复提醒',
      width: 120,
      dataIndex: 'repeatOption',
      search: false,
      render: (_, row) =>
        row.repeatOption
          ? row.repeatOption
              .split(',')
              .map((item) => timeUnitEnumObj[item as TimeUnitEnum])
              .join()
          : '-',
    },
    {
      title: '默认重复周期',
      width: 150,
      dataIndex: 'rpt',
      search: false,
      render: (_, row) =>
        row.rpt
          ? `${parseInt(row.rpt, 10)}${
              timeUnitEnumObj[row.rpt.replace(/[0-9]/gi, '') as TimeUnitEnum]
            }`
          : '无',
    },
    {
      title: '关联设备类型',
      dataIndex: 'widthDevice',
      width: 120,
      search: false,
      render: (_, row) => {
        if (
          row.withDeviceType &&
          row.withDeviceType !== '0' &&
          row.withDeviceType !== '-1'
        )
          return row.withDeviceType;
        else if (row.withDeviceType === '0') return '所有类型';
        else return '否';
      },
    },
    {
      title: '关联宠物',
      dataIndex: 'withPet',
      width: 100,
      search: false,
      render: (_, row) => (row.withPet ? '是' : '否'),
    },
    {
      title: '约定提醒',
      dataIndex: 'scheduleAppoint',
      width: 100,
      search: false,
      render: (_, row) => row.scheduleAppoint || '无',
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      width: 100,
      search: false,
    },
    {
      title: '是否启用',
      width: 100,
      dataIndex: 'enable',
      valueType: 'select',
      valueEnum: getStatusMap(),
      render: (_, row) => (
        <Switch
          checked={!!row.enable}
          checkedChildren="启用"
          unCheckedChildren="禁用"
          onChange={(ev) => switchStatus(ev, row.id, row.isCustom)}
        />
      ),
    },
    {
      title: '操作',
      dataIndex: 'action',
      search: false,
      width: 80,
      fixed: 'right',
      render: (_, row) => (
        <Button type="link" onClick={() => editType(row.id)}>
          编辑
        </Button>
      ),
    },
  ];

  useEffect(() => {
    requestTypeList(listParam);
  }, [listParam]);

  return (
    <ProTable<ScheduleType>
      dataSource={dataList}
      columns={columns}
      defaultSize="small"
      rowKey="id"
      search={{
        defaultCollapsed: false,
        span: 6,
        optionRender: searchOptionRender,
      }}
      scroll={{
        x: columns
          .filter((col) => col.dataIndex !== 'action')
          .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
      }}
      toolbar={{
        actions: [
          <Button
            key="button"
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => editType('0')}
          >
            创建
          </Button>,
          <Button key="button" type="default">
            <a
              target="_blank"
              href={`${
                location.protocol
              }//${getCurrentDomain()}/adm/schedule/type_string_out?X-Admin-Session=${localStorage.getItem(
                'sessionToken',
              )}`}
              rel="noreferrer"
            >
              导出字串表
            </a>
          </Button>,
          <Button
            key="button"
            type="default"
            onClick={() =>
              postMessageFunction({
                type: 'redirect',
                content: { redirectUrl: '/schedule/type_string_in' },
              })
            }
          >
            导入字串表
          </Button>,
        ],
      }}
    />
  );
};

export default List;
