import { ScheduleTypeParam } from './../../../models/schedule/interface';
import { initLocaleContentForm } from '@/components/LocaleContentModal/util';
import { ScheduleTypeForm, ScheduleTypeLocalePropForm } from './interface';
import { ScheduleTypeDetail, TimeUnitEnum } from '@/models/schedule/interface';
import { uuid } from '@/utils/uuid';
import { StatusEnum } from '@/models/common.interface';

export const initScheduleTypeForm: ScheduleTypeForm = {
  images: [],
  withDevice: '',
  withPet: false,
  scheduleAppoint: '',
  priority: 0,
  isCustom: false,
  enable: false,
  repeatRemindUnitList: [],
  hasDefaultRepeatRemind: false,
  defaultRepeatRemindUnit: TimeUnitEnum.DAY,
  repeatRemindDuration: null,
};

export const initScheduleTypeLocalePropForm: ScheduleTypeLocalePropForm = {
  ...initLocaleContentForm,
  name: '',
  msg0: '',
  msg1: '',
  msg2: '',
  msg3: '',
};

// 将formData转换为param
export const transferFormDataToParam = (
  formData: ScheduleTypeForm,
  localeContentList: ScheduleTypeLocalePropForm[],
  id = '',
): ScheduleTypeParam => {
  const param: ScheduleTypeParam = {
    img: formData.images[0].url || '',
    withDeviceType: formData.withDevice,
    withPet: +formData.withPet as StatusEnum,
    scheduleAppoint: formData.scheduleAppoint,
    priority: formData.priority,
    isCustom: +formData.isCustom as StatusEnum,
    enable: +formData.enable as StatusEnum,
    _localizedProps: { name: {}, msg0: {}, msg1: {}, msg2: {}, msg3: {} },
    rpt: '',
    repeatOption: formData.repeatRemindUnitList.join(','),
  };
  +id && (param.id = id);

  // 判断是否开启了默认重复提醒
  if (formData.hasDefaultRepeatRemind) {
    param.rpt = `${formData.repeatRemindDuration}${formData.defaultRepeatRemindUnit}`;
  }

  localeContentList.forEach((localeContent) => {
    param._localizedProps = {
      name: { ...param._localizedProps.name, [localeContent.language]: localeContent.name },
      msg0: { ...param._localizedProps.msg0, [localeContent.language]: localeContent.msg0 },
      msg1: { ...param._localizedProps.msg1, [localeContent.language]: localeContent.msg1 },
      msg2: { ...param._localizedProps.msg2, [localeContent.language]: localeContent.msg2 },
      msg3: { ...param._localizedProps.msg3, [localeContent.language]: localeContent.msg3 },
    };
  });

  return param;
};

// 将接口返回的详情数据转换为formData
export const transferDetailToFormData = (detail: ScheduleTypeDetail): ScheduleTypeForm => {
  const formData: ScheduleTypeForm = {
    images: [
      {
        uid: uuid(),
        name: '图片',
        url: detail.img,
      },
    ],
    withDevice: detail.withDeviceType,
    withPet: !!detail.withPet,
    scheduleAppoint: detail.scheduleAppoint,
    priority: detail.priority,
    isCustom: !!detail.isCustom,
    enable: !!detail.enable,
    repeatRemindUnitList: detail.repeatOption
      ? (detail.repeatOption.split(',') as TimeUnitEnum[])
      : [],
    hasDefaultRepeatRemind: !!detail.rpt,
    defaultRepeatRemindUnit: TimeUnitEnum.DAY,
    repeatRemindDuration: null,
  };
  if (formData.hasDefaultRepeatRemind) {
    formData.repeatRemindDuration = parseInt(detail.rpt, 10);
    formData.defaultRepeatRemindUnit = detail.rpt.replace(/[0-9]/gi, '') as TimeUnitEnum;
  }
  return formData;
};

export const transferLocaleContent = (detail: ScheduleTypeDetail): ScheduleTypeLocalePropForm[] => {
  const { name, msg0, msg1, msg2, msg3 } = detail._localizedProps;
  const formData: ScheduleTypeLocalePropForm[] = [];
  for (const lang in name) {
    if (name[lang] !== undefined) {
      const langFormData: ScheduleTypeLocalePropForm = {
        language: lang,
        name: name[lang],
        msg0: msg0[lang],
        msg1: msg1[lang],
        msg2: msg2[lang],
        msg3: msg3[lang],
      };
      formData.push(langFormData);
    }
  }
  return formData;
};
