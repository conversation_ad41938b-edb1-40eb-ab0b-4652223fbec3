import {
  PackageBatchListParam,
  PackageRegionEnum,
  PackageTypeEnum,
} from '@/models/package/interface';
import { initPackageBatchListParam } from '@/models/package/util';
import dayjs from 'dayjs';
import {
  RubbishBoxNFCForm,
  RubbishBoxNFCTableForm,
  RubbishBoxNFCUrlParam,
} from './interface';

export const initialRubbishBoxNFCForm: RubbishBoxNFCForm = {
  number: null,
  orderNo: '',
  // 默认国内
  region: PackageRegionEnum.CHINA,
  type: PackageTypeEnum.TYPE1,
};

// 将url中的参数转为表格搜索表单数据
export const transferUrlParamToTableFormData = (
  urlParam: RubbishBoxNFCUrlParam,
) => {
  const formData: RubbishBoxNFCTableForm = {};
  if (urlParam.batchNo) {
    formData.batchNo = urlParam.batchNo;
  }
  if (urlParam.orderNo) {
    formData.orderNo = urlParam.orderNo;
  }
  if (urlParam.operator) {
    formData.operator = urlParam.operator;
  }
  if (urlParam.startTime && urlParam.endTime) {
    formData.timestamp = [];
    formData.timestamp[0] = dayjs.unix(+urlParam.startTime);
    formData.timestamp[1] = dayjs.unix(+urlParam.endTime);
  }
  return formData;
};

export const transferUrlParamToListParam = (
  urlParam: RubbishBoxNFCUrlParam,
): PackageBatchListParam => {
  const listParam: PackageBatchListParam = {
    ...initPackageBatchListParam,
    batchNo: urlParam.batchNo,
    orderNo: urlParam.orderNo,
    operator: urlParam.operator,
  };
  if (urlParam.startTime && urlParam.endTime) {
    listParam.startTime = +urlParam.startTime;
    listParam.endTime = +urlParam.endTime;
  }
  return listParam;
};

// 将formData转换为param
// export const transferFormDataToParam = (formData: RubbishBoxNFCForm, id?: number): RubbishBoxNFCParam => {
//   const param: RubbishBoxNFCParam = {
//   };
//   id && (param.id = id);
//   return param;
// };

// 将接口返回的详情数据转换为formData
// export const transferDetailToFormData = (detail: RubbishBoxNFCDetail): RubbishBoxNFCForm => {
//   const formData: RubbishBoxNFCForm = {
//   };
//   return formData;
// };
