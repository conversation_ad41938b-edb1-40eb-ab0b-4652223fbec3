import { postMessageFunction, spanConfig } from '@/models/common.util';
import { fetchPackageBatchList } from '@/models/package/fetch';
import {
  PackageBatch,
  PackageBatchListParam,
} from '@/models/package/interface';
import { initPackageBatchListParam } from '@/models/package/util';
import Edit from '@/pages/Device/RubbishBoxNFC/Edit';
import {
  RubbishBoxNFCTableForm,
  RubbishBoxNFCUrlParam,
} from '@/pages/Device/RubbishBoxNFC/interface';
import {
  transferUrlParamToListParam,
  transferUrlParamToTableFormData,
} from '@/pages/Device/RubbishBoxNFC/util';
import { packageApiOption } from '@/services/api/package';
import { getCurrentPrefix } from '@/utils/currentPrefix';
import { Pagination, initPagination } from '@/utils/request';
import { useParams } from '@@/exports';
import useUrlState from '@ahooksjs/use-url-state';
import { PlusOutlined } from '@ant-design/icons';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProTable,
} from '@ant-design/pro-components';
import { Button, Drawer, FormInstance, Space } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useRef, useState } from 'react';

const List: React.FC = () => {
  const urlRestParam = useParams<{ device: string }>();
  const [urlParam] = useUrlState<RubbishBoxNFCUrlParam>({});
  const formRef = useRef<FormInstance<RubbishBoxNFCTableForm>>();
  const [showEdit, setShowEdit] = useState(false);
  const [dataList, setDataList] = useState<PackageBatch[]>([]);
  const [listParam, setListParam] = useState<PackageBatchListParam>();
  const [pagination, setPagination] = useState<Pagination>(initPagination);

  // 获取列表数据
  const requestRubbishBoxNFCList = async (
    param: PackageBatchListParam = initPackageBatchListParam,
  ) => {
    if (!urlRestParam.device) return;
    const { items, ...rest } = await fetchPackageBatchList(
      param,
      urlRestParam.device,
    );
    setPagination(rest);
    setDataList(items);
  };

  // 编辑
  const editRubbishBoxNFC = () => {
    setShowEdit(true);
  };

  // 关闭编辑框
  const onEditClosed = () => {
    setShowEdit(false);
  };

  // 编辑成功后的回调
  const onEditSuccess = () => {
    onEditClosed();
    setListParam({ ...(listParam as PackageBatchListParam) });
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          const formData: RubbishBoxNFCTableForm = form?.getFieldsValue();
          const params: string[] = [];
          if (formData.batchNo) {
            params.push(`batchNo=${formData.batchNo}`);
          }
          if (formData.orderNo) {
            params.push(`orderNo=${formData.orderNo}`);
          }
          if (formData.operator) {
            params.push(`operator=${formData.operator}`);
          }
          if (formData.timestamp) {
            params.push(`startTime=${formData.timestamp[0].unix()}`);
            params.push(`endTime=${formData.timestamp[1].unix()}`);
          }
          if (!params || !params.length) {
            postMessageFunction({
              type: 'redirect',
              content: {
                redirectUrl: `/t6/rubbish_box_nfc_list`,
              },
            });
            return;
          }
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: `/t6/rubbish_box_nfc_list?${params.join('&')}`,
            },
          });
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: `/t6/rubbish_box_nfc_list`,
            },
          });
        }}
      >
        重置
      </Button>,
    ];
  };

  const onPaginationChanged = (page: number, pageSize: number) => {
    let index = page;
    if (pageSize !== listParam?.limit) {
      index = 1;
    }
    setListParam({
      ...listParam,
      limit: pageSize,
      offset: (index - 1) * pageSize,
    });
  };

  const gotoNFCAccountList = (record: PackageBatch) => {
    postMessageFunction({
      type: 'redirect',
      content: {
        redirectUrl: `/t6/nfc_account_list`,
        param: { batchNo: record.batchNo },
      },
    });
  };

  const exportRecord = (record: PackageBatch) => {
    if (!urlRestParam.device) return;
    const url = `${
      window.origin
    }${getCurrentPrefix()}${packageApiOption.packageExport.url.replace(
      ':device',
      urlRestParam.device,
    )}?X-Admin-Session=${localStorage.getItem('sessionToken')}&batchNo=${
      record.batchNo
    }&orderNo=${record.orderNo}`;
    // console.log(url);
    window.open(url);
  };

  const columns: Array<ProColumns<PackageBatch>> = [
    {
      title: '注册批号',
      dataIndex: 'batchNo',
      width: 150,
    },
    {
      title: '金蝶订单号',
      dataIndex: 'orderNo',
      width: 150,
    },
    {
      title: '生成数量',
      dataIndex: 'number',
      width: 80,
      search: false,
    },
    {
      title: '操作者',
      dataIndex: 'operator',
      width: 100,
    },
    {
      title: '注册时间',
      dataIndex: 'timestamp',
      width: 150,
      valueType: 'dateRange',
      render: (_, record) =>
        dayjs.unix(record.timestamp).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      width: 80,
      dataIndex: 'action',
      search: false,
      render: (_, record) => (
        <Space>
          <Button type="link" onClick={() => gotoNFCAccountList(record)}>
            查看NFC信息
          </Button>
          <Button type="link" onClick={() => exportRecord(record)}>
            导出
          </Button>
        </Space>
      ),
    },
  ];

  useEffect(() => {
    if (!listParam) return;
    requestRubbishBoxNFCList(listParam);
  }, [listParam]);

  useEffect(() => {
    const formData = transferUrlParamToTableFormData(urlParam);
    const listParam = transferUrlParamToListParam(urlParam);
    const form = formRef.current;
    form?.setFieldsValue(formData);
    setListParam(listParam);
  }, [formRef, urlParam]);

  return (
    <>
      <ProTable<PackageBatch>
        dataSource={dataList}
        formRef={formRef}
        columns={columns}
        defaultSize="small"
        rowKey="batchNo"
        search={{
          defaultCollapsed: false,
          span: spanConfig,
          optionRender: searchOptionRender,
        }}
        scroll={{
          x: columns
            .filter((col) => col.dataIndex !== 'action')
            .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
        }}
        toolbar={{
          actions: [
            <Button
              key="button"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => editRubbishBoxNFC()}
            >
              新增注册
            </Button>,
          ],
        }}
        pagination={{
          pageSize: pagination.limit,
          total: pagination.total,
          showQuickJumper: true,
          onChange: onPaginationChanged,
        }}
      />
      <Drawer
        open={showEdit}
        destroyOnClose
        maskClosable
        onClose={onEditClosed}
      >
        <Edit onSubmitSuccess={onEditSuccess} onClose={onEditClosed} />
      </Drawer>
    </>
  );
};

export default List;
