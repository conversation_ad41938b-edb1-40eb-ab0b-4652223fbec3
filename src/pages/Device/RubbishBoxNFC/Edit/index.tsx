import { fetchPackageBatchCreate } from '@/models/package/fetch';
import {
  PackageBatchParam,
  PackageRegionEnum,
  PackageTypeEnum,
} from '@/models/package/interface';
import { useParams } from '@@/exports';
import { ProCard } from '@ant-design/pro-components';
import { Button, Form, Input, InputNumber, Radio, message } from 'antd';
import React, { useState } from 'react';
import { RubbishBoxNFCForm } from '../interface';
import { initialRubbishBoxNFCForm } from '../util';

interface Props {
  onSubmitSuccess: () => void;
  onClose: () => void;
}

const Edit: React.FC<Props> = ({ onClose, onSubmitSuccess }: Props) => {
  const urlParam = useParams<{ device: string }>();
  const [form] = Form.useForm<RubbishBoxNFCForm>();
  const [loading, setLoading] = useState(false);

  // 提交form表单
  const submit = async (formData: RubbishBoxNFCForm) => {
    if (!formData.number) {
      message.warning('请输入大于0的整数!');
      return;
    }
    if (!urlParam.device) {
      message.warning('请提供设备类型');
      return;
    }

    setLoading(true);
    const _param: PackageBatchParam = {
      orderNo: formData.orderNo,
      num: formData.number,
      region: formData.region,
      type: formData.type,
    };
    // console.log(_param);
    try {
      await fetchPackageBatchCreate(_param, urlParam.device);
      message.success(`创建成功！`);
      onSubmitSuccess();
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <ProCard>
      <Form
        form={form}
        layout="vertical"
        onFinish={submit}
        initialValues={initialRubbishBoxNFCForm}
      >
        <Form.Item
          name="orderNo"
          label="金蝶订单号"
          rules={[{ required: true, message: '请输入金蝶订单号' }]}
        >
          <Input placeholder="请输入金蝶订单号" />
        </Form.Item>
        <Form.Item
          name="number"
          label="指定数量"
          rules={[{ required: true, message: '请输入大于0的整数' }]}
        >
          <InputNumber
            controls={false}
            min={1}
            max={99999}
            placeholder="请输入大于0的整数"
          />
        </Form.Item>
        <Form.Item
          name="region"
          label="锁定区域"
          rules={[{ required: true, message: '请选择锁定区域' }]}
        >
          <Radio.Group
            style={{ width: '100%' }}
            options={[
              { label: '国内版', value: PackageRegionEnum.CHINA },
              { label: '海外版', value: PackageRegionEnum.OVERSEA },
            ]}
            optionType="button"
          ></Radio.Group>
        </Form.Item>
        <Form.Item
          name="type"
          label="垃圾袋分类"
          rules={[{ required: true, message: '请选择垃圾袋分类' }]}
        >
          <Radio.Group
            style={{ width: '100%' }}
            options={[
              { label: '型号1', value: PackageTypeEnum.TYPE1 },
              { label: '型号2', value: PackageTypeEnum.TYPE2 },
              { label: '型号3', value: PackageTypeEnum.TYPE3 },
            ]}
            optionType="button"
          ></Radio.Group>
        </Form.Item>
        <Form.Item wrapperCol={{ span: 24 }} style={{ textAlign: 'center' }}>
          <Button
            type="primary"
            htmlType="submit"
            style={{ marginRight: 16 }}
            loading={loading}
            disabled={loading}
          >
            提交
          </Button>
          <Button type="default" onClick={() => onClose()}>
            取消
          </Button>
        </Form.Item>
      </Form>
    </ProCard>
  );
};

export default Edit;
