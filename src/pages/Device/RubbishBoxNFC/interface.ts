import { PackageRegionEnum, PackageTypeEnum } from '@/models/package/interface';
import { Dayjs } from 'dayjs';

export interface RubbishBoxNFCUrlParam {
  batchNo?: string;
  orderNo?: string;
  operator?: string;
  // 10位时间戳
  startTime?: string;
  // 10位时间戳
  endTime?: string;
}

export interface RubbishBoxNFCTableForm {
  batchNo?: string;
  orderNo?: string;
  operator?: string;
  timestamp?: Dayjs[];
}

// 注册时用
export interface RubbishBoxNFCForm {
  orderNo: string;
  number: number | null;
  region: PackageRegionEnum;
  type: PackageTypeEnum;
}
