import Prompt from '@/components/Prompt';
import { ApiSuccessEnum } from '@/models/common.interface';
import { postMessageFunction } from '@/models/common.util';
import {
  fetchFirmwareDeletion,
  fetchFirmwareHardwareList,
  fetchFirmwareNotifyingUpgrade,
  fetchFirmwareVersionList,
  fetchFirmwareVersionSaving,
  fetchMiniVersion,
  fetchMiniVersionSave,
} from '@/models/device/fetch';
import {
  FirmwareVersion,
  FirmwareVersionListParam,
  FirmwareVersionParam,
  OpenEnum,
} from '@/models/device/interface';
import {
  forceUpgradeEnumName,
  initFirmwareVersionListParam,
  openEnumName,
} from '@/models/device/util';
import global from '@/utils/global';
import { Pagination, initPagination } from '@/utils/request';
import useUrlState from '@ahooksjs/use-url-state';
import { DownOutlined, PlusOutlined } from '@ant-design/icons';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProTable,
} from '@ant-design/pro-components';
import { getLocale, history, useParams } from '@umijs/max';
import {
  Button,
  Dropdown,
  FormInstance,
  MenuProps,
  Modal,
  Select,
  message,
} from 'antd';
import dayjs from 'dayjs';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { hasDependentHardwareDeviceList } from '../util';
import { FirmwareVersionTableForm } from './interface';
import { initFirmwareVersionTableForm } from './util';

const createdInfoDeviceTypeList = ['t5', 't6', 't7'];

const List: React.FC = () => {
  const formRef = useRef<FormInstance<FirmwareVersionTableForm>>();
  const urlRestParam = useParams<{ device: string }>();
  const locale = getLocale().replace('-', '_');
  const [urlParam] = useUrlState<{ hardware?: number }>();
  const [dataList, setDataList] = useState<FirmwareVersion[]>([]);
  const [listParam, setListParam] = useState<FirmwareVersionListParam>({
    ...initFirmwareVersionListParam,
    _t: urlRestParam.device || '',
  });
  const [pagination, setPagination] = useState<Pagination>(initPagination);
  const [firmwareHardwareObj, setFirmwareHardwareObj] = useState<{
    [key: number]: number;
  }>();
  const [firmwareHardwareList, setFirmwareHardwareList] = useState<number[]>(
    [],
  );
  const [showPrompt, setShowPrompt] = useState(false);
  const [lowestVersion, setLowestVersion] = useState<string>();

  const initTableSearchForm = useCallback(
    (hardware: number) => {
      const form = formRef.current;
      form?.setFieldsValue({
        ...initFirmwareVersionTableForm,
        hardware: `${hardware}`,
      });
    },
    [formRef],
  );

  const onPromptClosed = () => {
    setShowPrompt(false);
  };

  // 编辑
  const editFirmwareVersion = (id?: string) => {
    const hardware = formRef.current?.getFieldValue('hardware');
    history.push(
      `/device/${urlRestParam.device}/firmware/version/edit/${
        id || 0
      }/${hardware}`,
    );
  };

  // 获取固件大版本
  const requestFirmwareHardwareList = async () => {
    const _firmwareHardwareList = await fetchFirmwareHardwareList(
      urlRestParam.device || '',
    );
    return _firmwareHardwareList;
  };

  // 获取最小版本
  const requestLowestVersion = async (hardware: string) => {
    const result = await fetchMiniVersion(hardware, urlRestParam.device || '');
    if (result && typeof result === 'string') {
      setLowestVersion(result);
    } else {
      setLowestVersion(undefined);
    }
  };

  // 获取列表数据
  const requestFirmwareVersionList = async (
    param: FirmwareVersionListParam = initFirmwareVersionListParam,
  ) => {
    if (!urlRestParam.device) return;
    const { items, ...rest } = await fetchFirmwareVersionList(
      param,
      urlRestParam.device,
    );
    setDataList(items);
    setPagination(rest);
  };

  const requestDeleteFirmwareVersion = async (firmwareId: string) => {
    const operateValue = 'delete';
    const text = window.prompt(`请输入：${operateValue} 命令来执行此操作`);
    if (text !== operateValue) return;
    await fetchFirmwareDeletion(firmwareId, urlRestParam.device || '');
    message.success('删除成功');
    setListParam({ ...listParam });
  };

  const requestFirmwareVersionNotifyingUpgrade = async (firmwareId: string) => {
    const deviceId = window.prompt(`请输入设备ID`);
    if (!deviceId) return;
    await fetchFirmwareNotifyingUpgrade(
      firmwareId,
      +deviceId,
      urlRestParam.device || '',
    );
  };

  // 更新为公开状态
  const updatePublic = async (firmwareVersion: FirmwareVersion) => {
    if (!urlRestParam.device) return;

    const param: FirmwareVersionParam = {
      id: firmwareVersion.id,
      file: JSON.stringify({ size: '', url: '' }),
      hardware: firmwareVersion.hardware,
      version: firmwareVersion.version || '',
      forceUpgrade: firmwareVersion.forceUpgrade,
      appMinVersion: firmwareVersion.appMinVersion || '',
      // LocaleObject
      releaseNotes: JSON.stringify(firmwareVersion.releaseNotes),
      // FirmwareVersionDetailParam
      details: JSON.stringify(firmwareVersion.details),
      remark: firmwareVersion.remark || '',
      open: OpenEnum.PUBLIC,
    };
    const result = await fetchFirmwareVersionSaving(param, urlRestParam.device);
    if (result === ApiSuccessEnum.success) {
      message.success('保存成功');
      setListParam({ ...listParam });
    }
  };

  // 二次确认是否要公开此设备
  const onConfirmToPublic = (firmwareVersion: FirmwareVersion) => {
    Modal.confirm({
      title: '确认要公开此版本吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        await updatePublic(firmwareVersion);
        setListParam({ ...listParam });
      },
    });
  };

  const getActionMenuItems = (
    firmwareVersion: FirmwareVersion,
  ): MenuProps['items'] => {
    let menuItems: MenuProps['items'] = [];
    if (firmwareVersion.file && firmwareVersion.file.url) {
      menuItems.push({
        key: 'download',
        label: (
          <a target="_blank" href={firmwareVersion.file.url} rel="noreferrer">
            下载
          </a>
        ),
      });
    }
    menuItems = menuItems.concat([
      {
        key: 'edit',
        label: (
          <a onClick={() => editFirmwareVersion(firmwareVersion.id)}>编辑</a>
        ),
      },
    ]);
    // 不是公开状态时，可以手动调整为公开
    if (firmwareVersion.open !== OpenEnum.PUBLIC) {
      menuItems = menuItems.concat([
        {
          key: 'public',
          label: <a onClick={() => onConfirmToPublic(firmwareVersion)}>公开</a>,
        },
      ]);
    }

    if (firmwareVersion.open !== OpenEnum.PUBLIC) {
      menuItems = menuItems.concat([
        {
          key: 'betaDevice',
          label: (
            <a
              onClick={() =>
                postMessageFunction({
                  type: 'redirect',
                  content: {
                    redirectUrl: '/dev/betadevices',
                    param: {
                      _t: urlRestParam.device || '',
                      firmwareId: firmwareVersion.id,
                    },
                  },
                })
              }
            >
              内测设备
            </a>
          ),
        },
      ]);
    }

    if (!firmwareVersion.open) {
      menuItems = menuItems.concat([
        {
          key: 'notifyUpgrade',
          label: (
            <a
              onClick={() =>
                requestFirmwareVersionNotifyingUpgrade(firmwareVersion.id)
              }
            >
              通知升级
            </a>
          ),
        },
      ]);
    }
    menuItems.push({
      key: 'delete',
      label: (
        <a onClick={() => requestDeleteFirmwareVersion(firmwareVersion.id)}>
          删除
        </a>
      ),
    });
    return menuItems;
  };

  const getHardwareList = async () => {
    if (!urlRestParam.device) return;
    let _firmwareHardwareList: number[] = [];
    if (
      hasDependentHardwareDeviceList.includes(
        urlRestParam.device.toLocaleLowerCase(),
      ) &&
      urlParam.hardware
    ) {
      _firmwareHardwareList = [+urlParam.hardware];
    } else if (global.remoteHardwareDevice.includes(urlRestParam.device)) {
      _firmwareHardwareList = await requestFirmwareHardwareList();
    } else if (global.localOneHardwareDevice.includes(urlRestParam.device)) {
      _firmwareHardwareList = [1];
    } else if (global.localTwoHardwareDevice.includes(urlRestParam.device)) {
      _firmwareHardwareList = [1, 2];
    } else if (global.localThereHardwareDevice.includes(urlRestParam.device)) {
      _firmwareHardwareList = [1, 2, 3];
    }
    if (!_firmwareHardwareList.length) return;

    const _firmwareHardwareObj: { [key: number]: number } = {};
    _firmwareHardwareList.forEach((item) => {
      _firmwareHardwareObj[item] = item;
    });
    setFirmwareHardwareList(_firmwareHardwareList);
    setFirmwareHardwareObj(_firmwareHardwareObj);
    setListParam({ ...listParam, hardware: _firmwareHardwareList[0] });
    initTableSearchForm(_firmwareHardwareList[0]);
    // 获取最小版本
    if (global.appMinVersionDeviceList.includes(urlRestParam.device || '')) {
      console.log(_firmwareHardwareList[0]);
      requestLowestVersion(`${_firmwareHardwareList[0]}`);
    }
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          const formData: FirmwareVersionTableForm = form?.getFieldsValue();
          const _param: FirmwareVersionListParam = {
            ...initFirmwareVersionListParam,
            hardware: +(formData.hardware || listParam.hardware || 1),
          };
          if (formData.version) _param.version = `${formData.version}`;
          // console.log(formData, _param);
          setListParam(_param);

          if (_param.hardware !== listParam.hardware) {
            requestLowestVersion(`${_param.hardware}`);
          }
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          form?.resetFields();
          setListParam({
            ...initFirmwareVersionListParam,
            hardware: firmwareHardwareList[0],
          });
        }}
      >
        重置
      </Button>,
    ];
  };

  const onPaginatorChanged = (page: number, pageSize: number) => {
    let index = page;
    if (listParam.limit !== pageSize) {
      index = 1;
    }
    setListParam({
      ...listParam,
      limit: pageSize,
      offset: (index - 1) * pageSize,
    });
  };

  const changeLowestVersion = () => {
    setShowPrompt(true);
  };

  const requestLowestVersionSaving = async (version: string) => {
    const hardware = formRef.current?.getFieldValue('hardware');
    const result = await fetchMiniVersionSave(
      { hardware, version: `${version}` },
      urlRestParam.device || '',
    );
    if (result === ApiSuccessEnum.success) {
      message.success('修改最小版本成功');
      setLowestVersion(version);
    }
    onPromptClosed();
  };

  const columns: Array<ProColumns<FirmwareVersion>> = [
    {
      title: '硬件',
      dataIndex: 'hardware',
      valueEnum: firmwareHardwareObj,
      valueType: 'select',
      renderFormItem: (_, { defaultRender, ...rest }) => (
        <Select {...rest} allowClear={false} />
      ),
      width: 80,
    },
    {
      title: '版本号',
      dataIndex: 'version',
      width: 100,
    },
    {
      title: '固件',
      dataIndex: 'details',
      search: false,
      width: 180,
      render: (_, row) => (
        <div>
          {(row.details || []).map((item) => (
            <p key={item.id}>
              {item.module}: {item.version}
            </p>
          ))}
        </div>
      ),
    },
    {
      title: '开发状态',
      dataIndex: 'open',
      search: false,
      valueEnum: openEnumName,
      width: 100,
    },
    {
      title: 'releaseNotes',
      dataIndex: 'releaseNotes',
      search: false,
      width: 250,
      render: (_, row) =>
        row.releaseNotes
          ? row.releaseNotes[locale] || row.releaseNotes['zh-CN'] || '-'
          : '-',
    },
    {
      title: '升级方式',
      dataIndex: 'hardware',
      search: false,
      valueEnum: forceUpgradeEnumName,
      width: 100,
      render: (_, row) => forceUpgradeEnumName[row.forceUpgrade],
    },
    {
      title: 'APP最低版本',
      dataIndex: 'appMinVersion',
      search: false,
      width: 120,
    },
    {
      title: 'App提示',
      dataIndex: 'appAlert',
      search: false,
      width: 100,
    },
    {
      title: '操作',
      dataIndex: 'action',
      search: false,
      fixed: 'right',
      width: 100,
      render: (_, row) => (
        <Dropdown
          menu={{
            items: getActionMenuItems(row),
          }}
          trigger={['click']}
        >
          <a>
            操作 <DownOutlined />
          </a>
        </Dropdown>
      ),
    },
  ];

  const getColumns = (): Array<ProColumns<FirmwareVersion>> => {
    const _columns = columns;
    if (
      urlRestParam.device &&
      global.firmwareInternalRemarkDeviceList.includes(
        urlRestParam.device.toLowerCase(),
      )
    ) {
      const index = columns.findIndex((col) => col.title === 'releaseNotes');
      _columns.splice(index, 0, {
        title: '内部备注',
        dataIndex: 'remark',
        search: false,
        width: 120,
      });
    }

    // 创建信息：创建人，创建时间
    if (
      urlRestParam.device &&
      createdInfoDeviceTypeList.includes(urlRestParam.device.toLowerCase())
    ) {
      const index = columns.findIndex((col) => col.title === '操作');
      _columns.splice(index, 0, {
        title: '创建时间',
        dataIndex: 'createdAt',
        search: false,
        width: 180,
        render: (_, row) => dayjs(row.createdAt).format('YYYY-MM-DD HH:mm:ss'),
      });
      _columns.splice(index, 0, {
        title: '创建人',
        dataIndex: 'creator',
        search: false,
        width: 120,
      });
    }

    return _columns;
  };

  useEffect(() => {
    getHardwareList();
  }, []);

  useEffect(() => {
    if (listParam.hardware) {
      requestFirmwareVersionList(listParam);
    }
  }, [listParam]);

  return (
    <>
      <ProTable<FirmwareVersion>
        dataSource={dataList}
        columns={getColumns()}
        defaultSize="small"
        rowKey="id"
        formRef={formRef}
        search={{
          defaultCollapsed: false,
          span: 6,
          optionRender: searchOptionRender,
        }}
        scroll={{
          x: columns
            .filter((col) => col.dataIndex === 'action')
            .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
        }}
        toolbar={{
          subTitle:
            urlRestParam.device &&
            global.appMinVersionDeviceList.includes(urlRestParam.device) ? (
              <>
                固件要求最低版本号：
                <Button
                  type="link"
                  style={{ cursor: 'pointer' }}
                  onClick={() => changeLowestVersion()}
                >
                  {lowestVersion || '请配置'}
                </Button>
              </>
            ) : (
              ''
            ),
          actions: [
            <Button
              key="button"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => editFirmwareVersion()}
            >
              新增
            </Button>,
          ],
        }}
        pagination={{
          pageSize: pagination.limit,
          total: pagination.total,
          showQuickJumper: true,
          onChange: onPaginatorChanged,
        }}
      />
      <Prompt
        visible={showPrompt}
        title="编辑"
        label="固件要求最低版本号"
        defaultValue={lowestVersion}
        onOk={(ev) => ev && requestLowestVersionSaving(`${ev}`)}
        onCancel={onPromptClosed}
      />
    </>
  );
};

export default List;
