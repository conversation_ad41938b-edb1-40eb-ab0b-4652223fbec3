import { SearchTypeEnum, WifiSearchTypeEnum } from '@/models/device/interface';

export interface DeviceSearchForm {
  type?: SearchTypeEnum | WifiSearchTypeEnum;
  s?: string;
}

export interface DeviceSnForm {
  sn: string;
  mac: string;
  chipId: string;
}

export interface DeviceModuleForm {
  module: string;
  sort: number | null;
  minVersion: number | null;
  logLevel: number | null;
  updateTime: number | null;
}

// 日志相关
export interface LogsTableForm {
  sn: string;
}
