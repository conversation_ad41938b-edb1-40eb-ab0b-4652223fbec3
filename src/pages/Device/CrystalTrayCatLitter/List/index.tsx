import RedirectLink from '@/components/RedirectLink';
import SelectInputGroup from '@/components/SelectInputGroup';
import { ApiSuccessEnum, SelectOption } from '@/models/common.interface';
import { postMessageFunction, spanConfig } from '@/models/common.util';
import { WifiSearchTypeEnum } from '@/models/device/interface';
import {
  fetchSandTrayList,
  fetchSandTrayResetTime,
} from '@/models/sandTray/fetch';
import {
  SandTray,
  SandTrayListParam,
  SandTrayResetTimeParam,
} from '@/models/sandTray/interface';
import { initSandTrayListParam } from '@/models/sandTray/util';
import useUrlState from '@ahooksjs/use-url-state';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProTable,
} from '@ant-design/pro-components';
import { useParams } from '@umijs/max';
import {
  Button,
  Form,
  FormInstance,
  Input,
  Modal,
  Popconfirm,
  Space,
  message,
} from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { CrystalTrayCatLitterTableForm, UrlParam } from '../interface';
import OperationDetail from './OperationDetail';

const searchTypeOptions: SelectOption[] = [
  { value: WifiSearchTypeEnum.ID, label: '设备ID' },
  { value: WifiSearchTypeEnum.SN, label: '设备SN' },
];

const List: React.FC = () => {
  const [urlParam] = useUrlState<UrlParam>();
  const urlRestParam = useParams<{ device: string }>();
  const [dataList, setDataList] = useState<SandTray[]>([]);
  const [listParam, setListParam] = useState<SandTrayListParam>();
  // const [pagination, setPagination] = useState<Pagination>(initPagination);
  const [selectedSandTray, setSelectedSandTray] = useState<SandTray>();
  const [showResetOperationTimesModal, setShowResetOperationTimesModal] =
    useState(false);
  const [showOperationDetailModal, setShowOperationDetailModal] =
    useState(false);
  const [resetReasonLoading, setResetReasonLoading] = useState(false);
  const [resetReason, setResetReason] = useState('');

  const tableFormRef = useRef<FormInstance<CrystalTrayCatLitterTableForm>>();

  // 获取列表数据
  const requestSandTrayList = async (param: SandTrayListParam) => {
    if (!urlRestParam.device || !param || (!param.sandTraySn && !param.s))
      return;
    const list = await fetchSandTrayList(param, urlRestParam.device);
    // setPagination(rest);
    setDataList(list);
  };

  const closeAllModal = () => {
    setShowResetOperationTimesModal(false);
    setShowOperationDetailModal(false);
    setSelectedSandTray(undefined);
  };

  const openResetOperationTimesModal = (record: SandTray) => {
    setShowResetOperationTimesModal(true);
    setSelectedSandTray(record);
  };

  const openOperationDetailModal = (record: SandTray) => {
    setShowOperationDetailModal(true);
    setSelectedSandTray(record);
  };

  const submitResetReason = async () => {
    try {
      if (!selectedSandTray || !urlRestParam || !urlRestParam.device) return;
      const param: SandTrayResetTimeParam = {
        operateReason: resetReason,
        sandTray: selectedSandTray.sandTraySn,
        deviceSn: selectedSandTray.deviceSn,
      };
      // console.log(param);
      setResetReasonLoading(true);
      const result = await fetchSandTrayResetTime(param, urlRestParam.device);
      if (result === ApiSuccessEnum.success) {
        message.success('重置成功');
        setResetReason('');
        closeAllModal();
        setListParam(listParam ? { ...listParam } : initSandTrayListParam);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setResetReasonLoading(false);
    }
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          const formData: CrystalTrayCatLitterTableForm =
            form?.getFieldsValue();
          // console.log(formData);
          if (
            (!formData.selectInputValue || !formData.selectInputValue.input) &&
            !formData.sandTraySn
          )
            return;

          const param: UrlParam = {};

          if (formData.selectInputValue && formData.selectInputValue.input) {
            param.s = formData.selectInputValue.input;
            param.type = formData.selectInputValue.select as WifiSearchTypeEnum;
          }

          if (formData.sandTraySn) {
            param.sandTraySn = formData.sandTraySn;
          }
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: `/${urlRestParam.device}/crystal_tray_cat_litter`,
              param: param as Record<string, string>,
            },
          });
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: `/${urlRestParam.device}/crystal_tray_cat_litter`,
            },
          });
        }}
      >
        重置
      </Button>,
    ];
  };

  // const onPaginationChanged = (page: number, pageSize: number) => {
  //   let index = page;
  //   if (pageSize !== listParam.limit) {
  //     index = 1;
  //   }
  //   setListParam({
  //     ...listParam,
  //     limit: pageSize,
  //     offset: (index - 1) * pageSize,
  //   });
  // };

  const columns: Array<ProColumns<SandTray>> = [
    { title: '用户ID', dataIndex: 'userId', width: 100, search: false },
    {
      title: '设备SN',
      dataIndex: 'deviceSn',
      width: 160,
      search: false,
      render: (_, record) => (
        <RedirectLink
          text={record.deviceSn}
          linkUrl={`/${urlRestParam.device}/devices`}
          params={{ type: WifiSearchTypeEnum.SN, s: record.deviceSn }}
        />
      ),
    },
    {
      title: '',
      dataIndex: 'selectInputValue',
      hideInTable: true,
      renderFormItem: () => (
        <SelectInputGroup
          inputProps={{
            placeholder: `请输入设备ID或设备SN`,
          }}
          selectProps={{
            defaultValue: WifiSearchTypeEnum.ID,
            options: searchTypeOptions,
          }}
        />
      ),
    },
    { title: '设备MAC', dataIndex: 'deviceMac', width: 120, search: false },
    {
      title: '水晶猫砂盘SN',
      dataIndex: 'sandTraySn',
      width: 160,
      formItemProps: {
        labelCol: {
          span: 10,
        },
      },
    },
    {
      title: '剩余次数',
      dataIndex: 'remainingTimes',
      width: 100,
      search: false,
    },
    {
      title: '累计操作重置',
      dataIndex: 'opetateTimes',
      width: 120,
      search: false,
      render: (_, record) => (record.opetateTimes ? '是' : '/'),
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      width: 180,
      search: false,
      render: (_, record) => {
        return (
          <Space>
            <Button
              type="link"
              onClick={() => {
                if (!record.deviceSn) {
                  message.warning('当前没有水晶猫砂盘所关联的SN信息');
                  return;
                }
                openResetOperationTimesModal(record);
              }}
            >
              重置使用次数
            </Button>
            <Button
              type="link"
              onClick={() => openOperationDetailModal(record)}
            >
              操作详情
            </Button>
          </Space>
        );
      },
    },
  ];

  useEffect(() => {
    const form = tableFormRef.current;
    const formData: CrystalTrayCatLitterTableForm = {};
    if (urlParam.s && urlParam.type) {
      formData.selectInputValue = {
        input: urlParam.s,
        select: urlParam.type,
      };
    }
    if (urlParam.sandTraySn) formData.sandTraySn = urlParam.sandTraySn;
    form?.setFieldsValue(formData);

    const param: SandTrayListParam = { ...initSandTrayListParam };
    if (urlParam.s && urlParam.type) {
      param.s = urlParam.s;
      param.type = urlParam.type;
    }
    if (urlParam.sandTraySn) {
      param.sandTraySn = urlParam.sandTraySn;
    }
    setListParam(param);
  }, [urlParam, urlRestParam]);

  useEffect(() => {
    if (!listParam) return;
    requestSandTrayList(listParam);
  }, [listParam]);

  return (
    <>
      <ProTable<SandTray>
        dataSource={dataList}
        columns={columns}
        defaultSize="small"
        rowKey="sandTraySn"
        formRef={tableFormRef}
        search={{
          defaultCollapsed: false,
          span: spanConfig,
          optionRender: searchOptionRender,
        }}
        scroll={{
          x: columns
            .filter((col) => col.dataIndex !== 'action')
            .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
        }}
        options={{
          reload: () =>
            setListParam(listParam ? { ...listParam } : initSandTrayListParam),
        }}
      />
      {showResetOperationTimesModal && selectedSandTray ? (
        <Modal
          width={600}
          title="重置水晶猫砂盘使用次数"
          open={showResetOperationTimesModal}
          footer={null}
          onCancel={closeAllModal}
          destroyOnClose
        >
          <Form.Item label="重置原因">
            <Input
              value={resetReason}
              onChange={(e) => setResetReason(e.target.value)}
            />
          </Form.Item>
          <Space
            style={{ width: '100%', display: 'flex', justifyContent: 'center' }}
          >
            <Popconfirm
              style={{ marginRight: 24 }}
              title="提交后，水晶猫砂盘使用次数将更新，是否确认？"
              onConfirm={submitResetReason}
              disabled={resetReasonLoading || !resetReason}
            >
              <Button type="primary" loading={resetReasonLoading}>
                提交
              </Button>
            </Popconfirm>
            <Button onClick={closeAllModal}>取消</Button>
          </Space>
        </Modal>
      ) : null}
      {showOperationDetailModal && selectedSandTray ? (
        <OperationDetail
          onClose={closeAllModal}
          sandTraySn={selectedSandTray.sandTraySn}
        />
      ) : null}
    </>
  );
};

export default List;
