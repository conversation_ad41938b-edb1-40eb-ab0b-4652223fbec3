import { fetchSandTrayOperationDetails } from '@/models/sandTray/fetch';
import { SandTrayOperationDetail } from '@/models/sandTray/interface';
import { useParams } from '@umijs/max';
import { Modal, Space, Table, Typography } from 'antd';
import { ColumnType } from 'antd/es/table';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';

interface Props {
  sandTraySn: string;
  onClose: () => void;
}

const OperationDetail: React.FC<Props> = ({ sandTraySn, onClose }: Props) => {
  const urlRestParam = useParams<{ device: string }>();
  const [dataList, setDataList] = useState<SandTrayOperationDetail[]>([]);

  const requestDetailList = async (sn: string) => {
    if (!urlRestParam.device) return;
    const list = await fetchSandTrayOperationDetails(sn, urlRestParam.device);
    setDataList(list);
  };

  const columns: ColumnType<SandTrayOperationDetail>[] = [
    {
      title: '操作时间',
      dataIndex: 'operateTime',
      render: (_, record) =>
        dayjs(record.operateTime).format('YYYY-MM-DD HH:mm:ss'),
    },
    { title: '操作方式', dataIndex: 'operateWay' },
    { title: '操作原因', dataIndex: 'operateReason' },
    { title: '操作者', dataIndex: 'operater' },
  ];

  useEffect(() => {
    requestDetailList(sandTraySn);
  }, [sandTraySn]);

  return (
    <Modal
      width={900}
      open={true}
      destroyOnClose
      footer={null}
      onCancel={onClose}
    >
      <Table<SandTrayOperationDetail>
        title={() => (
          <Space>
            <Typography.Title level={3}>操作详情</Typography.Title>
            <Typography.Text type="secondary">
              操作次数： {dataList.length}次
            </Typography.Text>
          </Space>
        )}
        columns={columns}
        dataSource={dataList}
      />
    </Modal>
  );
};

export default OperationDetail;
