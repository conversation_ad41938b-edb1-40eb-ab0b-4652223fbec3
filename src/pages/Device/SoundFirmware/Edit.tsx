import Uploader from '@/components/Uploader';
import { ApiSuccessEnum } from '@/models/common.interface';
import {
  fetchFirmwareSoundMd5,
  fetchSoundFirmWareDetail,
  fetchSoundFirmWareSaving,
} from '@/models/device/fetch';
import { UploadTokenTypeEnum } from '@/services/qiniuOss/interface';
import { ProCard } from '@ant-design/pro-components';
import { history, useParams } from '@umijs/max';
import { useUnmountedRef } from 'ahooks';
import { Button, Form, Input, InputNumber, message, UploadFile } from 'antd';
import React, { useEffect, useState } from 'react';
import { Digest, SoundFirmwareForm } from './interface';
import {
  initialSoundFirmwareForm,
  transferDetailToFormData,
  transferFormDataToParam,
} from './util';

const Edit: React.FC = () => {
  const urlRestParam = useParams<{ id: string; device: string }>();
  const unmountedRef = useUnmountedRef();
  const [form] = Form.useForm<SoundFirmwareForm>();
  const [loading, setLoading] = useState(false);
  const [digestList, setDigestList] = useState<Digest[]>([]);
  const layout = {
    labelCol: { span: 3 },
    wrapperCol: { span: 20 },
  };

  // 根据id获取详情数据
  const requestDetailById = async (id: number) => {
    if (unmountedRef.current) return;
    const detail = await fetchSoundFirmWareDetail(
      id,
      urlRestParam.device || '',
    );
    const formData = transferDetailToFormData(detail);
    form.setFieldsValue(formData);
    setDigestList([{ md5: detail.digest, fileUid: formData.fileList[0].uid }]);
  };

  // 获取文件的MD5值
  const requestFirmwareMd5 = async (file: UploadFile<string>) => {
    if (unmountedRef.current) return;
    if (!file.url) return;
    const soundMd5 = await fetchFirmwareSoundMd5(file.url);
    setDigestList([...digestList, { md5: soundMd5.md5, fileUid: file.uid }]);
  };

  // 提交form表单
  const submit = async (formData: SoundFirmwareForm) => {
    if (unmountedRef.current) return;
    setLoading(true);
    const _param = transferFormDataToParam(
      formData,
      digestList,
      +(urlRestParam?.id || 0),
    );
    let result = '';
    let state = '';
    try {
      if (urlRestParam && urlRestParam.id && +urlRestParam.id) {
        state = '更新';
      } else {
        state = '创建';
      }
      result = await fetchSoundFirmWareSaving(
        _param,
        urlRestParam.device || '',
      );
      if (result === ApiSuccessEnum.success) {
        message.success(`${state}成功！`);
        history.back();
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  // 处理上传文件格式
  const normFile = (e: any) => {
    // console.log('Upload event:', { ...e });
    if (Array.isArray(e)) {
      return e;
    }
    return e && e.fileList;
  };

  const onFileUploadChanged = (ev: { file: UploadFile<string> }) => {
    const { file } = ev;
    if (file.status !== 'done') return;

    requestFirmwareMd5(file);
  };

  useEffect(() => {
    if (urlRestParam && urlRestParam.id && +urlRestParam.id) {
      requestDetailById(+urlRestParam.id);
    }
  }, [urlRestParam]);

  useEffect(() => {
    console.log(unmountedRef);
  }, [unmountedRef]);

  return (
    <ProCard>
      <Form
        {...layout}
        form={form}
        onFinish={submit}
        initialValues={initialSoundFirmwareForm}
      >
        <Form.Item
          name="version"
          label="版本号"
          rules={[{ required: true, message: '请输入版本号' }]}
        >
          <InputNumber placeholder="版本号" />
        </Form.Item>
        <Form.Item
          name="fileList"
          label="文件"
          getValueFromEvent={normFile}
          shouldUpdate
          valuePropName="fileList"
          rules={[{ required: true, message: '请选择文件' }]}
        >
          <Uploader
            type={UploadTokenTypeEnum.FIRMWARE}
            uploadData={{ namespace: 'd4sh-sound' }}
            onChange={onFileUploadChanged}
          />
        </Form.Item>
        <Form.Item name="note" label="详细">
          <Input.TextArea />
        </Form.Item>
        <Form.Item style={{ textAlign: 'right' }}>
          <Button
            type="primary"
            htmlType="submit"
            style={{ marginRight: 16 }}
            loading={loading}
            disabled={loading}
          >
            提交
          </Button>
          {/* <Button htmlType="reset" style={{ marginRight: 16 }} danger>
            重置
          </Button> */}
          <Button type="default" onClick={history.back}>
            取消
          </Button>
        </Form.Item>
      </Form>
    </ProCard>
  );
};

export default Edit;
