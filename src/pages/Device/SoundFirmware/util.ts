import { SoundFirmware, SoundFirmwareParam } from '@/models/device/interface';
import { SoundFirmwareForm, Digest } from './interface';
import { uuid } from '@/utils/uuid';

export const initialSoundFirmwareForm: SoundFirmwareForm = {
  version: null,
  fileList: [],
  note: '',
};

// 将formData转换为param
export const transferFormDataToParam = (
  formData: SoundFirmwareForm,
  digestList: Digest[],
  id?: number,
): SoundFirmwareParam => {
  const param: SoundFirmwareParam = {
    url: formData.fileList[0].url || '',
    size: formData.fileList[0].size || 0,
    digest: digestList.find((digest) => digest.fileUid === formData.fileList[0].uid)?.md5 || '',
    version: formData.version || 0,
    note: formData.note,
  };
  id && (param.id = id);
  return param;
};

// 将接口返回的详情数据转换为formData
export const transferDetailToFormData = (detail: SoundFirmware): SoundFirmwareForm => {
  const formData: SoundFirmwareForm = {
    version: detail.version,
    fileList: [
      {
        uid: uuid(),
        url: detail.url,
        name: '固件包',
        size: detail.size,
      },
    ],
    note: detail.note,
  };
  return formData;
};
