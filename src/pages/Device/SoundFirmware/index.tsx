import { ApiSuccessEnum } from '@/models/common.interface';
import {
  fetchSoundFirmWareDeletion,
  fetchSoundFirmWareList,
} from '@/models/device/fetch';
import {
  SoundFirmware,
  SoundFirmwareListParam,
} from '@/models/device/interface';
import { initSoundFirmwareListParam } from '@/models/device/util';
import { Pagination, initPagination } from '@/utils/request';
import { DownOutlined, PlusOutlined } from '@ant-design/icons';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { history, useParams } from '@umijs/max';
import { useUnmountedRef } from 'ahooks';
import { Button, Dropdown, message } from 'antd';
import React, { useEffect, useState } from 'react';

const List: React.FC = () => {
  const unmountedRef = useUnmountedRef();
  const urlRestParam = useParams<{ device: string }>();
  const [dataList, setDataList] = useState<SoundFirmware[]>([]);
  const [listParam, setListParam] = useState<SoundFirmwareListParam>(
    initSoundFirmwareListParam,
  );
  const [pagination, setPagination] = useState<Pagination>(initPagination);
  const columns: Array<ProColumns<SoundFirmware>> = [
    {
      title: '音频版本',
      dataIndex: 'version',
      width: 100,
    },
    {
      title: 'MD5',
      dataIndex: 'digest',
      width: 250,
    },
    {
      title: '下载包',
      dataIndex: 'size',
      width: 120,
      render: (_, row) => (
        <a href={row.url} target="_blank" rel="noreferrer">
          {row.size}字节
        </a>
      ),
    },
    {
      title: '备注',
      dataIndex: 'note',
      width: 100,
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 150,
      render: (_, row) => (
        <Dropdown
          menu={{
            items: [
              {
                key: 'edit',
                label: <a onClick={() => editSoundFirmware(row.id)}>编辑</a>,
              },
              {
                key: 'delete',
                label: (
                  <a onClick={() => requestDeleteSoundFirmware(row.id)}>删除</a>
                ),
              },
            ],
          }}
        >
          <a onClick={(e) => e.preventDefault()}>
            操作 <DownOutlined />
          </a>
        </Dropdown>
      ),
    },
  ];

  useEffect(() => {
    requestSoundFirmwareList(listParam);
  }, [listParam]);

  // 获取列表数据
  const requestSoundFirmwareList = async (
    param: SoundFirmwareListParam = initSoundFirmwareListParam,
  ) => {
    if (unmountedRef.current) return;
    const { items, ...rest } = await fetchSoundFirmWareList(
      param,
      urlRestParam.device,
    );
    setPagination(rest);
    setDataList(items);
  };

  // 编辑
  const editSoundFirmware = (id: number) => {
    history.push(`/device/${urlRestParam.device}/sound_firmware/edit/${id}`);
  };

  // 删除
  const requestDeleteSoundFirmware = async (id: number) => {
    if (unmountedRef.current) return;
    try {
      const result = await fetchSoundFirmWareDeletion(id, urlRestParam.device);
      if (result === ApiSuccessEnum.success) {
        message.success('删除成功');
        setListParam({ ...listParam });
      }
    } catch (error) {
      console.log(error);
    }
  };

  // Search的表单按钮渲染
  // const searchOptionRender = (searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>) => {
  //   const { form } = searchConfig;
  //   return [
  //     <Button
  //       key="search"
  //       type="primary"
  //       onClick={() => {
  //         const _param = transferFormDataToParam<any, SoundFirmwareListParam>(
  //           form?.getFieldsValue(),
  //         );
  //         setListParam(_param);
  //       }}
  //     >
  //       查询
  //     </Button>,
  //     <Button
  //       key="reset"
  //       type="default"
  //       onClick={() => {
  //         form?.resetFields();
  //         setListParam(initSoundFirmwareListParam);
  //       }}
  //     >
  //       重置
  //     </Button>,
  //   ];
  // };

  const onPaginationChanged = (page: number, pageSize: number) => {
    let index = page;
    if (pageSize !== listParam.limit) {
      index = 1;
    }
    setListParam({
      ...listParam,
      limit: pageSize,
      offset: (index - 1) * pageSize,
    });
  };

  return (
    <ProTable<SoundFirmware>
      dataSource={dataList}
      columns={columns}
      defaultSize="small"
      rowKey="id"
      search={false}
      scroll={{
        x: columns
          .filter((col) => col.dataIndex !== 'action')
          .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
      }}
      toolbar={{
        actions: [
          <Button
            key="button"
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => editSoundFirmware(0)}
          >
            添加
          </Button>,
        ],
      }}
      pagination={{
        pageSize: pagination.limit,
        total: pagination.total,
        showQuickJumper: true,
        onChange: onPaginationChanged,
      }}
    />
  );
};

export default List;
