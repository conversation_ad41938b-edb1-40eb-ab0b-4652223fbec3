import { initLocaleContentForm } from '@/components/LocaleContentModal/util';
import { LocaleObject, SelectOption } from '@/models/common.interface';
import {
  FirmwareDetail,
  FirmwareParam,
  OpenEnum,
} from '@/models/device/interface';
import { uuid } from '@/utils/uuid';
import {
  FirmwareForm,
  FirmwareLocaleForm,
  FirmwareTableForm,
} from './interface';

export const initFirmwareTableForm: FirmwareTableForm = {
  version: '',
};

export const initialFirmwareForm: FirmwareForm = {
  hardware: '',
  version: '',
  isOpen: OpenEnum.BETA,
  fileUploadList: [],
};

export const isOpenOptions: SelectOption[] = [
  {
    label: '内测',
    value: OpenEnum.BETA,
  },
  {
    label: '灰度',
    value: OpenEnum.GRAY,
  },
  {
    label: '公开',
    value: OpenEnum.PUBLIC,
  },
];

export const initFirmwareLocaleForm: FirmwareLocaleForm = {
  ...initLocaleContentForm,
  note: '',
};

// 将接口返回的数据转换为formData
export const transferDetailToFormData = (detail: FirmwareDetail) => {
  const formData: FirmwareForm = {
    hardware: `${detail.hardware}`,
    version: detail.version,
    isOpen: detail.open,
    fileUploadList: [
      {
        uid: uuid(),
        name: '固件文件',
        ...detail.file,
      },
    ],
  };
  if (detail.iosVersion) {
    formData.iosVersion = detail.iosVersion;
  }
  if (detail.androidVersion) {
    formData.androidVersion = detail.androidVersion;
  }
  return formData;
};

// 将接口返回的releaseNotes信息转换为formData
export const transferNotesToLocaleContentFormDataList = (
  releaseNotes: {
    [key: string]: string;
  } = {},
): FirmwareLocaleForm[] => {
  const formData: FirmwareLocaleForm[] = [];
  for (const language in releaseNotes) {
    if (releaseNotes[language]) {
      formData.push({
        language,
        note: releaseNotes[language],
      });
    }
  }
  return formData;
};

// 转换formData为param之前的校验
export const validFormDataValue = (formData: FirmwareForm) => {
  if (formData.isOpen === OpenEnum.GRAY && !formData.androidVersion) {
    throw new Error('请填写android的灰度版本号');
  } else if (formData.isOpen === OpenEnum.GRAY && !formData.iosVersion) {
    throw new Error('请填写iOS的灰度版本号');
  }
};

// 将formData转换为param
export const transferFormDataToParam = (
  formData: FirmwareForm,
  localeFormDataList: FirmwareLocaleForm[],
  id: number,
): FirmwareParam => {
  const releaseNotes: LocaleObject = {};
  const param: FirmwareParam = {
    id: id ? `${id}` : '',
    file: JSON.stringify({
      size: formData.fileUploadList[0].size,
      url: formData.fileUploadList[0].url,
    }),
    hardware: +formData.hardware,
    version: formData.version,
    open: formData.isOpen,
    extra: '{ "imageType": "A" }',
    note: '',
  };
  localeFormDataList.forEach((localeFormData) => {
    releaseNotes[localeFormData.language] = localeFormData.note;
  });
  param.note = JSON.stringify(releaseNotes);
  if (formData.isOpen === OpenEnum.GRAY && formData.androidVersion) {
    param.androidVersion = formData.androidVersion;
  }
  if (formData.isOpen === OpenEnum.GRAY && formData.iosVersion) {
    param.iosVersion = formData.iosVersion;
  }
  return param;
};
