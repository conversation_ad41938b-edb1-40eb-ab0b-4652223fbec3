import { LocaleContentForm } from '@/components/LocaleContentModal/interface';
import { OpenEnum } from '@/models/device/interface';
import { UploadFile } from 'antd';

export interface FirmwareTableForm {
  hardware?: string;
  version: string;
}

export interface FirmwareForm {
  hardware: string;
  version: string;
  isOpen: OpenEnum;
  fileUploadList: Array<UploadFile<string>>;
  // 灰度时使用的字段
  androidVersion?: string;
  iosVersion?: string;
}

export interface FirmwareLocaleForm extends LocaleContentForm {
  note: string;
}
