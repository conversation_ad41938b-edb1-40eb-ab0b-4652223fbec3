import LocaleContentModal from '@/components/LocaleContentModal';
import Uploader from '@/components/Uploader';
import { ApiSuccessEnum, SelectOption } from '@/models/common.interface';
import { ConnectState } from '@/models/connect';
import {
  fetchFirmwareDetail,
  fetchFirmwareSaving,
} from '@/models/device/fetch';
import { OpenEnum } from '@/models/device/interface';
import { UploadTokenTypeEnum } from '@/services/qiniuOss/interface';
import global from '@/utils/global';
import { PlusOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { history, useDispatch, useParams, useSelector } from '@umijs/max';
import {
  Button,
  Col,
  Form,
  Input,
  List,
  Row,
  Select,
  Typography,
  message,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { validAppVersionFormat } from '../util';
import { FirmwareForm, FirmwareLocaleForm } from './interface';
import {
  initFirmwareLocaleForm,
  initialFirmwareForm,
  isOpenOptions,
  transferDetailToFormData,
  transferFormDataToParam,
  transferNotesToLocaleContentFormDataList,
  validFormDataValue,
} from './util';

const Edit: React.FC = () => {
  const dispatch = useDispatch();
  const urlRestParams = useParams<{
    device: string;
    id: string;
    hardware: string;
  }>();
  const [form] = Form.useForm<FirmwareForm>();
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const localeOptions: SelectOption[] = useSelector(
    ({ app }: ConnectState) => app.localeOptions,
  );
  const [selectedLocaleContent, setSelectedLocaleContent] =
    useState<FirmwareLocaleForm>();
  const [localeContentList, setLocaleContentList] = useState<
    FirmwareLocaleForm[]
  >([]);
  const layout = {
    labelCol: { offset: 2, span: 20 },
    wrapperCol: { offset: 2, span: 20 },
  };

  const isOpen = Form.useWatch('isOpen', form);

  // 根据id获取详情数据
  const requestDetailById = async (id: number) => {
    const detail = await fetchFirmwareDetail(
      `${id}`,
      urlRestParams.device || '',
    );
    const formData = transferDetailToFormData(detail);
    form.setFieldsValue(formData);
    setLocaleContentList(transferNotesToLocaleContentFormDataList(detail.note));
  };

  // 提交form表单
  const submit = async (formData: FirmwareForm) => {
    try {
      validFormDataValue(formData);
    } catch (error) {
      message.warning((error as Error).message);
      return;
    }
    setLoading(true);
    const _param = transferFormDataToParam(
      formData,
      localeContentList,
      +(urlRestParams?.id || 0),
    );
    // console.log('submit', formData, _param);
    let result = '';
    let state = '';
    try {
      if (_param && +(_param.id || 0)) {
        state = '更新';
      } else {
        state = '创建';
      }
      result = await fetchFirmwareSaving(_param, urlRestParams.device || '');
      if (result === ApiSuccessEnum.success) {
        message.success(`${state}成功！`);
        history.back();
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  // 编辑本地化内容
  const editLocaleContent = (localeContent?: FirmwareLocaleForm) => {
    setSelectedLocaleContent(localeContent);
    setVisible(true);
  };

  // 删除本地化内容
  const deleteLocaleContent = (index: number) => {
    localeContentList.splice(index, 1);
    setLocaleContentList([...localeContentList]);
  };

  const hideUserModal = () => {
    setVisible(false);
    setSelectedLocaleContent(undefined);
  };

  const setLocaleContent = (formValue: FirmwareLocaleForm) => {
    const existedLocaleContent: FirmwareLocaleForm | undefined =
      localeContentList.find((item) => item.language === formValue.language);
    if (existedLocaleContent) {
      existedLocaleContent.note = formValue.note;
      setLocaleContentList([...localeContentList]);
    } else {
      setLocaleContentList([...localeContentList, formValue]);
    }
  };

  // 处理上传文件格式
  const normFile = (e: any) => {
    // console.log('Upload event:', e);
    if (Array.isArray(e)) {
      return e;
    }
    return e && e.fileList;
  };

  useEffect(() => {
    if (!urlRestParams) return;
    if (urlRestParams.hardware)
      form.setFieldValue('hardware', urlRestParams.hardware);
    if (urlRestParams.id && +urlRestParams.id) {
      requestDetailById(+urlRestParams.id);
    }
  }, [urlRestParams, form]);

  useEffect(() => {
    // 额外dva接口请求
    dispatch({ type: 'app/requestLocaleList' });
  }, [dispatch]);

  return (
    <>
      <ProCard>
        <Form
          {...layout}
          form={form}
          layout="vertical"
          onFinish={submit}
          initialValues={{
            ...initialFirmwareForm,
            hardware: urlRestParams.hardware,
          }}
        >
          <Form.Item
            name="hardware"
            label="硬件"
            rules={[{ required: true, message: '请输入硬件' }]}
          >
            <Input disabled placeholder="请输入硬件" />
          </Form.Item>
          <Form.Item
            name="version"
            label="版本号"
            rules={[{ required: true, message: '请输入版本号' }]}
          >
            <Input
              disabled={!!+(urlRestParams.id || 0)}
              placeholder="请输入版本号"
            />
          </Form.Item>
          <Form.Item
            name="isOpen"
            label="公开"
            rules={[{ required: true, message: '请选择是否公开' }]}
          >
            <Select
              options={isOpenOptions.filter((opt) => {
                return (
                  global.graySupportedDeviceList.includes(
                    urlRestParams.device || '',
                  ) ||
                  opt.value === OpenEnum.BETA ||
                  opt.value === OpenEnum.PUBLIC
                );
              })}
              placeholder="请选择是否公开"
            />
          </Form.Item>
          {isOpen === OpenEnum.GRAY ? (
            <Row>
              <Col span={11} offset={1}>
                <Form.Item
                  name="androidVersion"
                  label="Android 版本号"
                  rules={[
                    { required: true, message: '请输入Android版本号' },
                    {
                      validator: (_, value) => {
                        // 已经有校验可选和非可选逻辑了，此处不在重复判断
                        if (!value) return Promise.resolve();
                        const valid = validAppVersionFormat(value);
                        if (!valid) {
                          return Promise.reject('版本号不合法，请重新输入');
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <Input placeholder="请输入Android版本号" />
                </Form.Item>
              </Col>
              <Col span={11}>
                <Form.Item
                  name="iosVersion"
                  label="iOS版本号"
                  rules={[
                    { required: true, message: '请输入iOS版本号' },
                    {
                      validator: (_, value) => {
                        // 已经有校验可选和非可选逻辑了，此处不在重复判断
                        if (!value) return Promise.resolve();
                        const valid = validAppVersionFormat(value);
                        if (!valid) {
                          return Promise.reject('版本号不合法，请重新输入');
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <Input placeholder="请输入iOS版本号" />
                </Form.Item>
              </Col>
            </Row>
          ) : null}
          <Form.Item
            label="文件"
            name="fileUploadList"
            getValueFromEvent={normFile}
            shouldUpdate
            valuePropName="fileList"
            rules={[{ required: true, message: '请选择图片' }]}
          >
            <Uploader
              type={UploadTokenTypeEnum.FILE}
              uploadData={{ namespace: `${urlRestParams.device}-fw` }}
            />
          </Form.Item>
          <Form.Item>
            <Button icon={<PlusOutlined />} onClick={() => editLocaleContent()}>
              本地化内容
            </Button>
          </Form.Item>
          <Form.Item>
            <List<FirmwareLocaleForm>
              size="small"
              bordered={false}
              dataSource={localeContentList}
              renderItem={(item, index) => (
                <List.Item>
                  <div>
                    <p>
                      <Typography.Text strong>语言：</Typography.Text>
                      {localeOptions.find((opt) => opt.value === item.language)
                        ?.label || item.language}
                      <Button
                        type="link"
                        onClick={() => editLocaleContent(item)}
                      >
                        编辑
                      </Button>{' '}
                      <Button
                        type="link"
                        onClick={() => deleteLocaleContent(index)}
                      >
                        删除
                      </Button>
                    </p>
                    <p>
                      <Typography.Text strong>名称：</Typography.Text>
                      {item.note}
                    </p>
                  </div>
                </List.Item>
              )}
            />
          </Form.Item>
          <Form.Item style={{ textAlign: 'center' }}>
            <Button
              type="primary"
              htmlType="submit"
              style={{ marginRight: 16 }}
              loading={loading}
              disabled={loading}
            >
              提交
            </Button>
            {/* <Button htmlType="reset" style={{ marginRight: 16 }} danger>
              重置
            </Button> */}
            <Button type="default" onClick={history.back}>
              取消
            </Button>
          </Form.Item>
        </Form>
      </ProCard>
      <LocaleContentModal<FirmwareLocaleForm>
        visible={visible}
        localeOptionList={localeOptions}
        initialFormValues={initFirmwareLocaleForm}
        detail={selectedLocaleContent}
        onCancel={hideUserModal}
        onConfirm={(fd) => {
          setLocaleContent(fd);
          hideUserModal();
        }}
      >
        <Form.Item
          colon
          name="note"
          label="ReleaseNotes"
          rules={[{ required: true, message: '请输入发布内容' }]}
        >
          <Input.TextArea />
        </Form.Item>
      </LocaleContentModal>
    </>
  );
};

export default Edit;
