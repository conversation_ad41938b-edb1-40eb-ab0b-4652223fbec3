import Prompt from '@/components/Prompt';
import { ApiSuccessEnum } from '@/models/common.interface';
import { postMessageFunction } from '@/models/common.util';
import {
  fetchFirmwareDeletion,
  fetchFirmwareHardwareList,
  fetchFirmwareList,
  fetchFirmwareNotifyingUpgrade,
  fetchMiniVersion,
  fetchMiniVersionSave,
} from '@/models/device/fetch';
import { Firmware, FirmwareListParam } from '@/models/device/interface';
import { initFirmwareListParam, openEnumName } from '@/models/device/util';
import global from '@/utils/global';
import { Pagination, initPagination } from '@/utils/request';
import { DownOutlined, PlusOutlined } from '@ant-design/icons';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProTable,
} from '@ant-design/pro-components';
import { getLocale, history, useParams } from '@umijs/max';
import {
  Button,
  Dropdown,
  FormInstance,
  MenuProps,
  Select,
  message,
} from 'antd';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { FirmwareTableForm } from './interface';
import { initFirmwareTableForm } from './util';

const List: React.FC = () => {
  const formRef = useRef<FormInstance<FirmwareTableForm>>();
  const urlParam = useParams<{ device: string }>();
  const locale = getLocale().replace('-', '_');
  const [dataList, setDataList] = useState<Firmware[]>([]);
  const [listParam, setListParam] = useState<FirmwareListParam>({
    ...initFirmwareListParam,
    _t: urlParam.device || '',
  });
  const [pagination, setPagination] = useState<Pagination>(initPagination);
  const [firmwareHardwareObj, setFirmwareHardwareObj] = useState<{
    [key: number]: number;
  }>();
  const [firmwareHardwareList, setFirmwareHardwareList] = useState<number[]>(
    [],
  );
  const [showPrompt, setShowPrompt] = useState(false);
  const [lowestVersion, setLowestVersion] = useState<string>();

  const initTableSearchForm = useCallback(
    (hardware: number) => {
      const form = formRef.current;
      form?.setFieldsValue({
        ...initFirmwareTableForm,
        hardware: `${hardware}`,
      });
    },
    [formRef],
  );

  // 获取固件大版本
  const requestFirmwareHardwareList = async () => {
    const _firmwareHardwareList = await fetchFirmwareHardwareList(
      urlParam.device || '',
    );
    return _firmwareHardwareList;
  };

  // 获取最小版本
  const requestLowestVersion = async (hardware: string) => {
    const result = await fetchMiniVersion(hardware, urlParam.device || '');
    if (result && typeof result === 'string') {
      setLowestVersion(result);
    } else {
      setLowestVersion(undefined);
    }
  };

  // 获取列表数据
  const requestFirmwareList = async (
    param: FirmwareListParam = initFirmwareListParam,
  ) => {
    const { items, ...rest } = await fetchFirmwareList(
      param,
      urlParam.device || '',
    );
    setDataList(items);
    setPagination(rest);
  };

  const requestDeleteFirmware = async (firmwareId: string) => {
    const operateValue = 'delete';
    const text = window.prompt(`请输入：${operateValue} 命令来执行此操作`);
    if (text !== operateValue) return;
    await fetchFirmwareDeletion(firmwareId, urlParam.device || '');
    message.success('删除成功');
    setListParam({ ...listParam });
  };

  // 编辑
  const editFirmware = (id?: string) => {
    const hardware = formRef.current?.getFieldValue('hardware');
    history.push(
      `/device/${urlParam.device}/firmware/edit/${id || 0}/${hardware}`,
    );
  };

  const requestFirmwareNotifyingUpgrade = async (firmwareId: string) => {
    const deviceId = window.prompt(`请输入设备ID`);
    if (!deviceId) return;
    await fetchFirmwareNotifyingUpgrade(
      firmwareId,
      +deviceId,
      urlParam.device || '',
    );
  };

  const getActionMenuItems = (firmware: Firmware): MenuProps['items'] => {
    let menuItems: MenuProps['items'] = [];
    if (firmware.file && firmware.file.url) {
      menuItems.push({
        key: 'download',
        label: (
          <a target="_blank" href={firmware.file.url} rel="noreferrer">
            下载
          </a>
        ),
      });
    }
    menuItems = menuItems.concat([
      {
        key: 'edit',
        label: <a onClick={() => editFirmware(firmware.id)}>编辑</a>,
      },
    ]);
    if (!firmware.open) {
      menuItems = menuItems.concat([
        {
          key: 'betaDevice',
          label: (
            <a
              onClick={() =>
                postMessageFunction({
                  type: 'redirect',
                  content: {
                    redirectUrl: '/dev/betadevices',
                    param: {
                      _t: urlParam.device || '',
                      firmwareId: firmware.id,
                    },
                  },
                })
              }
            >
              内测设备
            </a>
          ),
        },
        {
          key: 'notifyUpgrade',
          label: (
            <a onClick={() => requestFirmwareNotifyingUpgrade(firmware.id)}>
              通知升级
            </a>
          ),
        },
      ]);
    }
    menuItems.push({
      key: 'delete',
      label: <a onClick={() => requestDeleteFirmware(firmware.id)}>删除</a>,
    });
    return menuItems;
  };

  const getHardwareList = async () => {
    let _firmwareHardwareList: number[] = [];
    if (global.remoteHardwareDevice.includes(urlParam.device || '')) {
      _firmwareHardwareList = await requestFirmwareHardwareList();
    } else if (global.localOneHardwareDevice.includes(urlParam.device || '')) {
      _firmwareHardwareList = [1];
    } else if (global.localTwoHardwareDevice.includes(urlParam.device || '')) {
      _firmwareHardwareList = [1, 2];
    } else if (
      global.localThereHardwareDevice.includes(urlParam.device || '')
    ) {
      _firmwareHardwareList = [1, 2, 3];
    }
    if (!_firmwareHardwareList.length) return;

    const _firmwareHardwareObj: { [key: number]: number } = {};
    _firmwareHardwareList.forEach((item) => {
      _firmwareHardwareObj[item] = item;
    });
    setFirmwareHardwareList(_firmwareHardwareList);
    setFirmwareHardwareObj(_firmwareHardwareObj);
    setListParam({ ...listParam, hardware: _firmwareHardwareList[0] });
    initTableSearchForm(_firmwareHardwareList[0]);
    // 获取最小版本
    if (global.appMinVersionDeviceList.includes(urlParam.device || '')) {
      console.log(_firmwareHardwareList[0]);
      requestLowestVersion(`${_firmwareHardwareList[0]}`);
    }
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          const formData: FirmwareTableForm = form?.getFieldsValue();
          const _param: FirmwareListParam = {
            ...initFirmwareListParam,
            hardware: +(formData.hardware || listParam.hardware || 1),
          };
          if (formData.version) {
            _param.version = `${formData.version}`;
          }
          // console.log(formData, _param);
          setListParam(_param);

          if (_param.hardware !== listParam.hardware) {
            requestLowestVersion(`${_param.hardware}`);
          }
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          form?.resetFields();
          setListParam({
            ...initFirmwareListParam,
            hardware: firmwareHardwareList[0],
          });
        }}
      >
        重置
      </Button>,
    ];
  };

  const onPaginatorChanged = (page: number, pageSize: number) => {
    let index = page;
    if (listParam.limit !== pageSize) {
      index = 1;
    }
    setListParam({
      ...listParam,
      limit: pageSize,
      offset: (index - 1) * pageSize,
    });
  };

  const changeLowestVersion = () => {
    setShowPrompt(true);
  };

  const onPromptClosed = () => {
    setShowPrompt(false);
  };

  const requestLowestVersionSaving = async (version: string) => {
    const hardware = formRef.current?.getFieldValue('hardware');
    const result = await fetchMiniVersionSave(
      { hardware, version: `${version}` },
      urlParam.device || '',
    );
    if (result === ApiSuccessEnum.success) {
      message.success('修改最小版本成功');
      setLowestVersion(version);
    }
    onPromptClosed();
  };

  const columns: Array<ProColumns<Firmware>> = [
    {
      title: '硬件',
      dataIndex: 'hardware',
      valueEnum: firmwareHardwareObj,
      valueType: 'select',
      renderFormItem: (_, { defaultRender, ...rest }) => (
        <Select {...rest} allowClear={false} />
      ),
      width: 80,
    },
    {
      title: '版本号',
      dataIndex: 'version',
      valueType: 'digit',
      width: 100,
    },
    {
      title: '大小',
      dataIndex: 'file',
      search: false,
      width: 100,
      render: (_, row) => row.file.size,
    },
    {
      title: '公开',
      dataIndex: 'open',
      search: false,
      width: 100,
      valueEnum: openEnumName,
    },
    {
      title: 'releaseNotes',
      dataIndex: 'releaseNotes',
      search: false,
      width: 250,
      render: (_, row) => (row.note ? row.note[locale] : '-'),
    },
    {
      title: '操作',
      dataIndex: 'action',
      search: false,
      fixed: 'right',
      width: 100,
      render: (_, row) => (
        <Dropdown
          menu={{
            items: getActionMenuItems(row),
          }}
          trigger={['click']}
        >
          <a>
            操作 <DownOutlined />
          </a>
        </Dropdown>
      ),
    },
  ];

  useEffect(() => {
    getHardwareList();
  }, []);

  useEffect(() => {
    if (listParam.hardware) {
      requestFirmwareList(listParam);
    }
  }, [listParam]);

  return (
    <>
      <ProTable<Firmware>
        dataSource={dataList}
        columns={columns}
        defaultSize="small"
        rowKey="id"
        formRef={formRef}
        search={{
          defaultCollapsed: false,
          span: 6,
          optionRender: searchOptionRender,
        }}
        scroll={{
          x: columns
            .filter((col) => col.dataIndex === 'action')
            .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
        }}
        toolbar={{
          subTitle: global.appMinVersionDeviceList.includes(
            urlParam.device || '',
          ) ? (
            <>
              固件要求最低版本号：
              <Button
                type="link"
                style={{ cursor: 'pointer' }}
                onClick={() => changeLowestVersion()}
              >
                {lowestVersion || '请配置'}
              </Button>
            </>
          ) : (
            ''
          ),
          actions: [
            <Button
              key="button"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => editFirmware()}
            >
              新增
            </Button>,
          ],
        }}
        pagination={{
          pageSize: pagination.limit,
          total: pagination.total,
          showQuickJumper: true,
          onChange: onPaginatorChanged,
        }}
      />
      <Prompt
        visible={showPrompt}
        title="编辑"
        label="固件要求最低版本号"
        defaultValue={lowestVersion}
        onOk={(ev) => ev && requestLowestVersionSaving(`${ev}`)}
        onCancel={onPromptClosed}
      />
    </>
  );
};

export default List;
