import SelectInputGroup from '@/components/SelectInputGroup';
import { ApiSuccessEnum, SelectOption } from '@/models/common.interface';
import { postMessageFunction, spanConfig } from '@/models/common.util';
import { WifiSearchTypeEnum } from '@/models/device/interface';
import {
  fetchPackageGarbageList,
  fetchPackageResetBagBox,
} from '@/models/package/fetch';
import {
  PackageGarbage,
  PackageGarbageListParam,
  PackageResetBagBoxParam,
} from '@/models/package/interface';
import useUrlState from '@ahooksjs/use-url-state';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProTable,
} from '@ant-design/pro-components';
import { useParams } from '@umijs/max';
import {
  Button,
  Form,
  FormInstance,
  Input,
  Modal,
  Popconfirm,
  Space,
  message,
} from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { RubbishBoxTableForm, UrlParam } from '../interface';
import OperationDetail from './OperationDetail';

const searchTypeOptions: SelectOption[] = [
  { value: WifiSearchTypeEnum.ID, label: '设备ID' },
  { value: WifiSearchTypeEnum.SN, label: '设备SN' },
];

const List: React.FC = () => {
  const [urlParam] = useUrlState<UrlParam>();
  const urlRestParam = useParams<{ device: string }>();
  const [dataList, setDataList] = useState<PackageGarbage[]>([]);
  const [listParam, setListParam] = useState<PackageGarbageListParam>();
  // const [pagination, setPagination] = useState<Pagination>(initPagination);
  const [selectedPackageGarbage, setSelectedPackageGarbage] =
    useState<PackageGarbage>();
  const [showResetOperationTimesModal, setShowResetOperationTimesModal] =
    useState(false);
  const [showOperationDetailModal, setShowOperationDetailModal] =
    useState(false);
  const [resetReasonLoading, setResetReasonLoading] = useState(false);
  const [resetReason, setResetReason] = useState('');

  const tableFormRef = useRef<FormInstance<RubbishBoxTableForm>>();

  // 获取列表数据
  const requestPackageGarbageList = async (param: PackageGarbageListParam) => {
    if (!urlRestParam.device || !param || (!param.packageSn && !param.s))
      return;
    const list = await fetchPackageGarbageList(param, urlRestParam.device);
    // setPagination(rest);
    setDataList(list);
  };

  const closeAllModal = () => {
    setShowResetOperationTimesModal(false);
    setShowOperationDetailModal(false);
    setSelectedPackageGarbage(undefined);
  };

  const openResetOperationTimesModal = (record: PackageGarbage) => {
    setShowResetOperationTimesModal(true);
    setSelectedPackageGarbage(record);
  };

  const openOperationDetailModal = (record: PackageGarbage) => {
    setShowOperationDetailModal(true);
    setSelectedPackageGarbage(record);
  };

  const submitResetReason = async () => {
    try {
      if (!selectedPackageGarbage || !urlRestParam || !urlRestParam.device)
        return;
      const param: PackageResetBagBoxParam = {
        operateReason: resetReason,
        packageSn: selectedPackageGarbage.garbageSn,
        deviceSn: selectedPackageGarbage.deviceSn,
      };
      // console.log(param);
      setResetReasonLoading(true);
      const result = await fetchPackageResetBagBox(param, urlRestParam.device);
      if (result === ApiSuccessEnum.success) {
        message.success('重置成功');
        setResetReason('');
        closeAllModal();
        setListParam({ ...listParam });
      }
    } catch (error) {
      console.log(error);
    } finally {
      setResetReasonLoading(false);
    }
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          const formData: RubbishBoxTableForm = form?.getFieldsValue();
          // console.log(formData);
          if (
            (!formData.selectInputValue || !formData.selectInputValue.input) &&
            !formData.garbageSn
          )
            return;

          const param: UrlParam = {};

          if (formData.selectInputValue && formData.selectInputValue.input) {
            param.s = formData.selectInputValue.input;
            param.type = formData.selectInputValue.select as WifiSearchTypeEnum;
          }

          if (formData.garbageSn) {
            param.packageSn = formData.garbageSn;
          }
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: `/${urlRestParam.device}/rubbish_box_list`,
              param: param as Record<string, string>,
            },
          });
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: `/${urlRestParam.device}/rubbish_box_list`,
            },
          });
        }}
      >
        重置
      </Button>,
    ];
  };

  // const onPaginationChanged = (page: number, pageSize: number) => {
  //   let index = page;
  //   if (pageSize !== listParam.limit) {
  //     index = 1;
  //   }
  //   setListParam({
  //     ...listParam,
  //     limit: pageSize,
  //     offset: (index - 1) * pageSize,
  //   });
  // };

  const columns: Array<ProColumns<PackageGarbage>> = [
    { title: '用户ID', dataIndex: 'userId', width: 100, search: false },
    { title: '设备SN', dataIndex: 'deviceSn', width: 160, search: false },
    {
      title: '',
      dataIndex: 'selectInputValue',
      hideInTable: true,
      renderFormItem: () => (
        <SelectInputGroup
          inputProps={{
            placeholder: `请输入设备ID或设备SN`,
          }}
          selectProps={{
            defaultValue: WifiSearchTypeEnum.ID,
            options: searchTypeOptions,
          }}
        />
      ),
    },
    { title: '设备MAC', dataIndex: 'deviceMac', width: 120, search: false },
    { title: '垃圾袋盒SN', dataIndex: 'garbageSn', width: 160 },
    {
      title: '剩余次数',
      dataIndex: 'remainingTimes',
      width: 100,
      search: false,
    },
    {
      title: '累计操作重置',
      dataIndex: 'opetateTimes',
      width: 120,
      search: false,
      render: (_, record) => (record.opetateTimes ? '是' : '/'),
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      width: 150,
      search: false,
      render: (_, record) => {
        return (
          <Space>
            <Button
              type="link"
              onClick={() => {
                if (!record.deviceSn) {
                  message.warning('当前没有垃圾袋盒所关联的SN信息');
                  return;
                }
                openResetOperationTimesModal(record);
              }}
            >
              重置使用次数
            </Button>
            <Button
              type="link"
              onClick={() => openOperationDetailModal(record)}
            >
              操作详情
            </Button>
          </Space>
        );
      },
    },
  ];

  useEffect(() => {
    const form = tableFormRef.current;
    const formData: RubbishBoxTableForm = {};
    if (urlParam.s && urlParam.type) {
      formData.selectInputValue = {
        input: urlParam.s,
        select: urlParam.type,
      };
    }
    if (urlParam.packageSn) formData.garbageSn = urlParam.packageSn;
    form?.setFieldsValue(formData);

    const param: PackageGarbageListParam = {};
    if (urlParam.s && urlParam.type) {
      param.s = urlParam.s;
      param.type = urlParam.type;
    }
    if (urlParam.packageSn) {
      param.packageSn = urlParam.packageSn;
    }
    setListParam(param);
  }, [urlParam, urlRestParam]);

  useEffect(() => {
    if (!listParam) return;
    requestPackageGarbageList(listParam);
  }, [listParam]);

  return (
    <>
      <ProTable<PackageGarbage>
        dataSource={dataList}
        columns={columns}
        defaultSize="small"
        rowKey="garbageSn"
        formRef={tableFormRef}
        search={{
          defaultCollapsed: false,
          span: spanConfig,
          optionRender: searchOptionRender,
        }}
        scroll={{
          x: columns
            .filter((col) => col.dataIndex !== 'action')
            .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
        }}
        options={{ reload: () => setListParam({ ...listParam }) }}
      />
      {showResetOperationTimesModal && selectedPackageGarbage ? (
        <Modal
          width={600}
          title="重置垃圾袋盒使用次数"
          open={showResetOperationTimesModal}
          footer={null}
          onCancel={closeAllModal}
          destroyOnClose
        >
          <Form.Item label="重置原因">
            <Input
              value={resetReason}
              onChange={(e) => setResetReason(e.target.value)}
            />
          </Form.Item>
          <Space
            style={{ width: '100%', display: 'flex', justifyContent: 'center' }}
          >
            <Popconfirm
              style={{ marginRight: 24 }}
              title="提交后，垃圾袋盒使用次数将更新，是否确认？"
              onConfirm={submitResetReason}
              disabled={resetReasonLoading || !resetReason}
            >
              <Button type="primary" loading={resetReasonLoading}>
                提交
              </Button>
            </Popconfirm>
            <Button onClick={closeAllModal}>取消</Button>
          </Space>
        </Modal>
      ) : null}
      {showOperationDetailModal && selectedPackageGarbage ? (
        <OperationDetail
          onClose={closeAllModal}
          packageSn={selectedPackageGarbage.garbageSn}
        />
      ) : null}
    </>
  );
};

export default List;
