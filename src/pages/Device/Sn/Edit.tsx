import { fetchDeviceSnSaving } from '@/models/device/fetch';
import { DeviceSnParam } from '@/models/device/interface';
import { Form, Input, message, Modal } from 'antd';
import React, { useState } from 'react';
import { DeviceSnForm } from '../interface';
import { initDeviceSnForm } from '../util';

interface Props {
  visible: boolean;
  deviceType: string;
  onClose?: (success: boolean) => void;
}

const Edit: React.FC<Props> = ({ visible, deviceType, onClose }: Props) => {
  const layout = {
    labelCol: { span: 3 },
    wrapperCol: { span: 20 },
  };
  const [form] = Form.useForm<DeviceSnForm>();
  const [loading, setLoading] = useState(false);

  const requestCreateSn = async () => {
    try {
      setLoading(true);
      const param: DeviceSnParam = form.getFieldsValue();
      await fetchDeviceSnSaving(param, deviceType);
      message.success('创建成功！');
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <Modal
      title="创建序列号"
      open={visible}
      okButtonProps={{
        disabled: loading,
        loading,
      }}
      onCancel={() => onClose && onClose(false)}
      onOk={async () => {
        try {
          await requestCreateSn();
          form.resetFields();
          onClose && onClose(true);
        } catch (error) {
          console.log(error);
        } finally {
          setLoading(false);
        }
      }}
    >
      <Form {...layout} form={form} initialValues={initDeviceSnForm}>
        <Form.Item name="sn" label="Sn">
          <Input />
        </Form.Item>
        <Form.Item name="mac" label="MAC">
          <Input />
        </Form.Item>
        <Form.Item name="chipId" label="CHIPID">
          <Input />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default Edit;
