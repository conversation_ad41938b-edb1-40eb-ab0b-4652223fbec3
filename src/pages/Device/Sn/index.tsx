import { SelectOption } from '@/models/common.interface';
import { spanConfig } from '@/models/common.util';
import {
  fetchDeviceSnDeletion,
  fetchDeviceSnList,
} from '@/models/device/fetch';
import {
  DeviceSn,
  DeviceSnListParam,
  SearchTypeEnum,
  WifiSearchTypeEnum,
} from '@/models/device/interface';
import {
  initDeviceSnListParam,
  initWifiDeviceSnListParam,
} from '@/models/device/util';
import global from '@/utils/global';
import {
  initPagination,
  initPaginatorParam,
  Pagination,
} from '@/utils/request';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProTable,
} from '@ant-design/pro-components';
import { useParams } from '@umijs/max';
import { Button, FormInstance, Input, Modal, Select, message } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import DeviceTableRowAction from '../components/DeviceTableRowAction';
import { TableRowActionEnum } from '../components/DeviceTableRowAction/interface';
import { DeviceSearchForm } from '../interface';
import { initDeviceSearchForm } from '../util';
import Edit from './Edit';

const Sn: React.FC = () => {
  const urlParam = useParams<{ device: string }>();
  const [showEdit, setShowEdit] = useState(false);
  const [pagination, setPagination] = useState<Pagination>(initPagination);
  const [dataList, setDataList] = useState<DeviceSn[]>([]);
  const [listParam, setListParam] = useState<DeviceSnListParam>(
    global.bluetoothDeviceList.includes(urlParam.device)
      ? initDeviceSnListParam
      : initWifiDeviceSnListParam,
  );
  const searchTypeList: Array<
    SelectOption<SearchTypeEnum | WifiSearchTypeEnum>
  > = [
    {
      label: 'Mac',
      value: global.bluetoothDeviceList.includes(urlParam.device)
        ? SearchTypeEnum.MAC
        : WifiSearchTypeEnum.MAC,
    },
    {
      label: 'Sn',
      value: global.bluetoothDeviceList.includes(urlParam.device)
        ? SearchTypeEnum.Sn
        : WifiSearchTypeEnum.SN,
    },
  ];
  const formRef = useRef<FormInstance<DeviceSearchForm>>();
  const columns: Array<ProColumns<DeviceSn>> = [
    {
      title: 'MAC',
      dataIndex: 'mac',
      search: false,
    },
    {
      title: 'Sn',
      dataIndex: 'sn',
      search: false,
    },
    {
      title: 'CHIPID',
      dataIndex: 'chipId',
      search: false,
    },
    {
      title: '操作',
      dataIndex: 'action',
      search: false,
      render: (_, row) => (
        <DeviceTableRowAction
          deviceName={urlParam.device}
          actionTypeList={[TableRowActionEnum.DELETE]}
          deleteCb={() => {
            Modal.confirm({
              title: '删除Sn',
              content: '确定要删除序列号？',
              onOk: () => {
                requestDeviceSnDeletion(row.sn);
              },
            });
          }}
        />
      ),
    },
    // 顶部自定义搜索功能
    {
      title: '',
      dataIndex: 'type',
      hideInTable: true,
      renderFormItem: (item, { defaultRender, ...rest }) => (
        <Select {...rest} options={searchTypeList} />
      ),
    },
    {
      title: '',
      dataIndex: 's',
      hideInTable: true,
      renderFormItem: (item, { defaultRender, ...rest }) => (
        <Input {...rest} allowClear />
      ),
    },
  ];

  useEffect(() => {
    requestDeviceSnList(listParam);
  }, [listParam]);

  useEffect(() => {
    const form = formRef.current;
    form?.setFieldsValue({
      ...initDeviceSearchForm,
      type: global.bluetoothDeviceList.includes(urlParam.device)
        ? SearchTypeEnum.Sn
        : WifiSearchTypeEnum.SN,
    });
  }, [formRef]);

  // 获取设备SN列表数据
  const requestDeviceSnList = async (param: DeviceSnListParam) => {
    try {
      const { items, ...rest } = await fetchDeviceSnList(
        param,
        urlParam.device,
      );
      setPagination(rest);
      setDataList(items);

      if (!items.length && param.s) {
        message.warning('查无信息');
      }
    } catch (error) {
      console.log(error);
    }
  };

  // 删除SN号
  const requestDeviceSnDeletion = async (sn: string) => {
    try {
      await fetchDeviceSnDeletion(sn, urlParam.device);
      message.success('删除成功！');
      const index = dataList.findIndex((item) => item.sn === sn);
      dataList.splice(index, 1);
      setDataList([...dataList]);
    } catch (error) {
      console.log(error);
    }
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          const param: DeviceSnListParam = {
            ...initPaginatorParam,
            ...form?.getFieldsValue(),
          };
          setListParam(param);
        }}
      >
        搜索
      </Button>,
      <Button
        key="create"
        type="primary"
        danger
        onClick={() => setShowEdit(true)}
      >
        创建
      </Button>,
    ];
  };

  const onPaginatorChanged = (page: number, pageSize: number) => {
    let index = page;
    if (listParam.limit !== pageSize) {
      index = 1;
    }
    setListParam({
      ...listParam,
      limit: pageSize,
      offset: (index - 1) * pageSize,
    });
  };

  return (
    <>
      <ProTable<DeviceSn>
        headerTitle="设备列表"
        dataSource={dataList}
        columns={columns}
        formRef={formRef}
        defaultSize="small"
        rowKey="sn"
        search={{
          defaultCollapsed: false,
          span: spanConfig,
          optionRender: searchOptionRender,
        }}
        scroll={{
          x: columns
            .filter((col) => col.dataIndex === 'action')
            .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
        }}
        pagination={{
          pageSize: pagination.limit,
          total: pagination.total,
          showQuickJumper: true,
          onChange: onPaginatorChanged,
        }}
      />
      <Edit
        visible={showEdit}
        deviceType={urlParam.device}
        onClose={(success) => {
          setShowEdit(false);
          if (success) {
            setListParam({ ...listParam });
          }
        }}
      />
    </>
  );
};

export default Sn;
