import { postMessageFunction } from '@/models/common.util';
import { fetchDeviceLogsWithFile } from '@/models/device/fetch';
import {
  DeviceLogWithFile,
  DeviceLogWithFileListParam,
} from '@/models/device/interface';
import { initDeviceLogWithFileListParam } from '@/models/device/util';
import { antdUtils } from '@/utils/antd.util';
import { Pagination, initPagination } from '@/utils/request';
import useUrlState from '@ahooksjs/use-url-state';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProTable,
} from '@ant-design/pro-components';
import { useParams } from '@umijs/max';
import { Button, FormInstance } from 'antd';
import moment from 'moment';
import React, { useEffect, useRef, useState } from 'react';

interface UrlParam {
  deviceId: string;
  hardware?: number;
}

interface LogsTableForm {
  deviceId?: string;
}

const LogsWithFile: React.FC = () => {
  const [urlParams] = useUrlState<UrlParam>();
  const urlRestParam = useParams<{ device: string }>();
  const [dataList, setDataList] = useState<DeviceLogWithFile[]>([]);
  const [listParam, setListParam] = useState<DeviceLogWithFileListParam>();
  const [pagination, setPagination] = useState<Pagination>(initPagination);
  const tableFormRef = useRef<FormInstance<LogsTableForm>>();

  // 获取列表数据
  const requestLogsList = async (param: DeviceLogWithFileListParam) => {
    const { items, ...rest } = await fetchDeviceLogsWithFile(
      param,
      urlRestParam.device || '',
    );
    setPagination(rest);
    setDataList(items);
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          if (!form) return;
          const formData: LogsTableForm = form.getFieldsValue();
          let redirectUrl = `/new-dev/logs-with-file?deviceType=${urlRestParam.device}`;
          if (formData.deviceId) {
            redirectUrl = `${redirectUrl}&deviceId=${formData.deviceId}`;
          }
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl,
            },
          });
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          form?.resetFields();
        }}
      >
        重置
      </Button>,
    ];
  };

  const onPaginationChanged = (page: number, pageSize: number) => {
    if (!listParam) return;

    setListParam({
      ...listParam,
      ...antdUtils.getPaginatorParamByTablePaginationChange(
        listParam,
        page,
        pageSize,
      ),
    });
  };

  const columns: Array<ProColumns<DeviceLogWithFile>> = [
    {
      title: '设备',
      dataIndex: 'deviceId',
    },
    {
      title: '上传日期',
      dataIndex: 'createdAt',
      search: false,
      render: (_, row) => moment(row.createdAt).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '文件大小',
      dataIndex: 'file',
      search: false,
      render: (_, row) => row.file.size || '-',
    },
    {
      title: '操作',
      dataIndex: 'action',
      search: false,
      render: (_, row) =>
        row.file.url ? (
          <Button
            type="link"
            target="_blank"
            href={row.file.url}
            download={row.createdAt}
          >
            下载log
          </Button>
        ) : (
          '-'
        ),
    },
  ];

  useEffect(() => {
    const _listParam = {
      ...initDeviceLogWithFileListParam,
      hardware: +urlParams.hardware,
    };
    if (!urlParams || !urlParams.deviceId) {
      setListParam(_listParam);
      return;
    }
    const form = tableFormRef.current;
    if (form) form.setFieldsValue({ deviceId: urlParams.deviceId });
    _listParam.deviceId = urlParams.deviceId;
    setListParam(_listParam);
  }, [urlParams, tableFormRef]);

  useEffect(() => {
    if (listParam) requestLogsList(listParam);
  }, [listParam]);

  return (
    <ProTable<DeviceLogWithFile>
      dataSource={dataList}
      columns={columns}
      defaultSize="small"
      rowKey="createdAt"
      key={'table'}
      formRef={tableFormRef}
      search={{
        defaultCollapsed: false,
        span: 6,
        optionRender: searchOptionRender,
      }}
      scroll={{
        x: columns
          .filter((col) => col.dataIndex !== 'action')
          .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
      }}
      pagination={{
        ...antdUtils.transferPaginationToTablePagination(pagination),
        onChange: onPaginationChanged,
      }}
    />
  );
};

export default LogsWithFile;
