import { BreadcrumbInfo } from '@/models/common.interface';
import global from '@/utils/global';
import { LeftOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { Outlet, useLocation, useParams } from '@umijs/max';
import React, { useEffect, useState } from 'react';
// import styles from './index.less';

const breadcrumbInfo: BreadcrumbInfo = {
  path: '',
  breadcrumbName: '',
};

const Device: React.FC = () => {
  const location = useLocation();
  const [title, setTitle] = useState(`设备管理`);
  const [showBackIcon, setShowBackIcon] = useState(false);
  const [breadcrumbInfoList, setBreadcrumbInfoList] = useState<
    BreadcrumbInfo[]
  >([breadcrumbInfo]);
  const param = useParams<{ device: string }>();

  useEffect(() => {
    if (global.deviceNames[param.device as string]) {
      const _breadcrumbInfoList = global.getBreadcrumbInfo(
        {
          ...breadcrumbInfo,
          breadcrumbName: global.deviceNames[param.device as string],
        },
        '',
        location,
      );
      console.log(_breadcrumbInfoList);
      setBreadcrumbInfoList(_breadcrumbInfoList);
      if (_breadcrumbInfoList.length > 2) {
        setShowBackIcon(true);
      }
    }

    if (location?.pathname.includes('modules')) {
      setTitle('设备模块');
    } else if (location?.pathname.includes('sn')) {
      setTitle('设备SN');
    } else if (location?.pathname.includes('tc-acoustic/firmware/version')) {
      setTitle('腾讯固件版本');
    } else if (location?.pathname.includes('/firmware/version/list')) {
      setTitle('固件版本');
    } else if (location?.pathname.includes('sound')) {
      setTitle('音频固件包');
    } else if (location?.pathname.includes('logs')) {
      setTitle('Log');
    } else if (location?.pathname.includes('rubbish-box-nfc/list')) {
      setTitle('垃圾袋盒管理');
    } else if (location?.pathname.includes('nfc-account/list')) {
      setTitle('垃圾袋盒查询');
    } else if (location?.pathname.includes('rubbish-box/list')) {
      setTitle('垃圾袋盒');
    } else if (location?.pathname.includes('crystal_tray_cat_litter')) {
      setTitle('水晶猫砂盘');
    } else if (
      location?.pathname.includes('crystal-tray-cat-litter-management/list')
    ) {
      setTitle('水晶猫砂盘管理');
    } else if (
      location?.pathname.includes('crystal-tray-cat-litter-nfc/list')
    ) {
      setTitle('水晶猫砂盘查询');
    } else {
      setTitle('设备管理');
    }
  }, [location, param]);

  return (
    <PageContainer
      header={{
        backIcon: showBackIcon ? <LeftOutlined /> : '',
        onBack: () => history.back(),
        title,
        ghost: true,
        breadcrumb: {
          itemRender: (route) => (
            <span>{route.title || route.breadcrumbName}</span>
          ),
          items: breadcrumbInfoList.map((item) => ({
            title: item.breadcrumbName,
            path: item.path,
            key: item.path,
          })),
        },
      }}
    >
      <Outlet />
    </PageContainer>
  );
};

export default Device;
