import RedirectLink from '@/components/RedirectLink';
import UserIdLink from '@/components/TableRowLink/UserIdLink';
import { SelectOption } from '@/models/common.interface';
import { postMessageFunction, spanConfig } from '@/models/common.util';
import { fetchDeviceList, fetchLink, fetchUnlink } from '@/models/device/fetch';
import {
  Device,
  DeviceListParam,
  SearchTypeEnum,
} from '@/models/device/interface';
import { bluetoothModeNames } from '@/models/device/util';
import {
  initPagination,
  initPaginatorParam,
  Pagination,
} from '@/utils/request';
import useUrlState from '@ahooksjs/use-url-state';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProTable,
} from '@ant-design/pro-components';
import { useParams } from '@umijs/max';
import { Button, FormInstance, Input, message, Modal, Select } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useRef, useState } from 'react';
import DeviceTableRowAction, {
  Props as DeviceTableRowActionProps,
} from '../components/DeviceTableRowAction';
import { TableRowActionEnum } from '../components/DeviceTableRowAction/interface';
import DeviceDetail from '../Detail';
import { DeviceSearchForm } from '../interface';
import { initDeviceSearchForm } from '../util';

const Bluetooth: React.FC = () => {
  const urlRestParam = useParams<{ device: string }>();
  const [urlParam] = useUrlState<DeviceSearchForm>();
  // const urlSearch = parse(location.href.split('?')[1]);
  const [pagination, setPagination] = useState<Pagination>(initPagination);
  const [deviceInfo, setDeviceInfo] = useState<Device>();
  const [showDeviceInfo, setShowDeviceInfo] = useState(false);
  const [dataList, setDataList] = useState<Device[]>([]);
  const [listParam, setListParam] = useState<DeviceListParam>();
  const searchTypeList: Array<SelectOption<SearchTypeEnum>> = [
    {
      label: '设备id',
      value: SearchTypeEnum.ID,
    },
    {
      label: 'Mac',
      value: SearchTypeEnum.MAC,
    },
    {
      label: 'Sn',
      value: SearchTypeEnum.Sn,
    },
    {
      label: '主人id',
      value: SearchTypeEnum.OWNER,
    },
  ];
  const formRef = useRef<FormInstance<DeviceSearchForm>>();

  // 获取设备列表数据
  const requestDeviceList = async (param: DeviceListParam) => {
    const { items, ...rest } = await fetchDeviceList(
      param,
      urlRestParam?.device || '',
    );
    setPagination(rest);
    setDataList(items);
  };

  // 绑定设备
  const requestBindDevice = async (id: number, groupId: number) => {
    try {
      await fetchLink({ id, groupId }, urlRestParam?.device || '');
      message.success('绑定成功！');
      let _param: DeviceListParam = {
        limit: pagination.limit,
        offset: pagination.offset,
      };
      if (formRef.current)
        _param = { ..._param, ...formRef.current.getFieldsValue() };
      // requestDeviceList(_param);
      setListParam({ ..._param });
    } catch (error) {
      console.log(error);
    }
  };

  // 解绑设备
  const requestUnbindDevice = async (id: number) => {
    try {
      await fetchUnlink(id, urlRestParam?.device || '');
      message.success('解绑成功！');
      let _param: DeviceListParam = {
        limit: pagination.limit,
        offset: pagination.offset,
      };
      if (formRef.current)
        _param = { ..._param, ...formRef.current.getFieldsValue() };
      // requestDeviceList(_param);
      setListParam({ ..._param });
    } catch (error) {
      console.log(error);
    }
  };

  // 当前设备详情被关闭时触发
  const onDeviceDetailClose = () => {
    setDeviceInfo(undefined);
    setShowDeviceInfo(false);
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          const formData: DeviceSearchForm = form?.getFieldsValue();
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: `/${(
                urlRestParam?.device || ''
              ).toLowerCase()}/devices`,
              param:
                formData.type && formData.s
                  ? (formData as Record<string, string>)
                  : undefined,
            },
          });
        }}
      >
        搜索
      </Button>,
      // <Button
      //   key="reset"
      //   type="default"
      //   onClick={() => {
      //     form?.resetFields();
      //     setListParam(initDryboxListParam);
      //   }}
      // >
      //   重置
      // </Button>,
    ];
  };

  const columns: Array<ProColumns<Device>> = [
    {
      title: 'id',
      dataIndex: 'id',
      width: 100,
      search: false,
    },
    { title: '设备名', dataIndex: 'name', width: 120, search: false },
    {
      title: 'SN',
      dataIndex: 'sn',
      search: false,
      width: 150,
    },
    {
      title: 'MAC',
      dataIndex: 'mac',
      search: false,
      width: 150,
    },
    {
      title: '注册时间',
      dataIndex: 'registerTime',
      search: false,
      render: (_, row) => dayjs(row.createdAt).format('YYYY-MM-DD HH:mm:ss'),
      width: 180,
    },
    {
      title: '硬/固件',
      dataIndex: 'hardware',
      search: false,
      render: (_, row) => `${row.hardware || '-'}/${row.firmware || '-'}`,
      width: 120,
    },
    {
      title: '主人',
      dataIndex: 'userId',
      search: false,
      render: (_, row) => <UserIdLink userId={row.relation?.userId || ''} />,
      width: 100,
    },
    {
      title: '所属家庭',
      dataIndex: 'familyId',
      search: false,
      width: 100,
      render: (_, row) => (
        <RedirectLink
          text={row.familyId}
          linkUrl="/user/family"
          params={{ groupId: row.familyId }}
        />
      ),
    },
    {
      title: '模式',
      dataIndex: 'mode',
      search: false,
      width: 120,
      render: (_, row) => bluetoothModeNames[row.mode || 0],
    },
    {
      title: '操作',
      dataIndex: 'action',
      search: false,
      width: 100,
      fixed: 'right',
      render: (_, row) => {
        const deviceTableRowActionProps: DeviceTableRowActionProps = {
          deviceName: urlRestParam?.device || '',
          device: row,
        };
        deviceTableRowActionProps.actionTypeList = [
          TableRowActionEnum.BIND_OR_UNBIND,
          TableRowActionEnum.BIND_HISTORY,
        ];
        deviceTableRowActionProps.bindOrUnbindCb = () => {
          if (row.relation && row.relation.userId) {
            // 存在用户，则为解绑用户
            Modal.confirm({
              title: '解绑设备',
              content: '确定要解绑设备么？',
              onOk: () => requestUnbindDevice(row.id),
            });
          } else {
            // 不存在用户，则要绑定用户
            const groupId = prompt('请输入家庭组ID', '');
            if (groupId)
              Modal.confirm({
                title: '绑定设备',
                content: '确定绑定此家庭组到此设备？',
                onOk: () => requestBindDevice(row.id, +groupId),
              });
          }
        };

        return <DeviceTableRowAction {...deviceTableRowActionProps} />;
      },
    },
    // 顶部自定义搜索功能
    {
      title: '',
      dataIndex: 'type',
      hideInTable: true,
      renderFormItem: (item, { defaultRender, ...rest }) => (
        <Select {...rest} options={searchTypeList} />
      ),
    },
    {
      title: '',
      dataIndex: 's',
      hideInTable: true,
      renderFormItem: (item, { defaultRender, ...rest }) => (
        <Input {...rest} allowClear />
      ),
    },
  ];

  useEffect(() => {
    const { type, s } = urlParam;
    if (type && +type && s) {
      setListParam({
        ...initPaginatorParam,
        type: +type as SearchTypeEnum,
        s: s as string,
      });
    }

    // 回填form表单数据
    const form = formRef.current;
    const initValue: DeviceSearchForm = initDeviceSearchForm;
    if (type && +type && s) {
      initValue.type = +type as SearchTypeEnum;
      initValue.s = s as string;
    }
    form?.setFieldsValue(initValue);
  }, [urlParam]);

  useEffect(() => {
    if (listParam) requestDeviceList(listParam);
  }, [listParam]);

  return (
    <>
      <ProTable<Device>
        headerTitle="设备列表"
        dataSource={dataList}
        columns={columns}
        formRef={formRef}
        // formRef={}
        defaultSize="small"
        rowKey="id"
        search={{
          defaultCollapsed: false,
          span: spanConfig,
          optionRender: searchOptionRender,
        }}
        scroll={{
          x: columns
            .filter((col) => col.dataIndex === 'action')
            .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
        }}
        pagination={{
          pageSize: pagination.limit,
          total: pagination.total,
          showQuickJumper: true,
        }}
      />
      {deviceInfo ? (
        <DeviceDetail
          device={deviceInfo}
          visible={showDeviceInfo}
          onClose={onDeviceDetailClose}
        />
      ) : (
        ''
      )}
    </>
  );
};

export default Bluetooth;
