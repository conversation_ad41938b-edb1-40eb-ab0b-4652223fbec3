import { postMessageFunction } from '@/models/common.util';
import { fetchDeviceLogs } from '@/models/device/fetch';
import { DeviceLog, DeviceLogsParam } from '@/models/device/interface';
import { antdUtils } from '@/utils/antd.util';
import {
  initPagination,
  initPaginatorParam,
  Pagination,
} from '@/utils/request';
import useUrlState from '@ahooksjs/use-url-state';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProTable,
} from '@ant-design/pro-components';
import { useParams } from '@umijs/max';
import { Button, FormInstance } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useRef, useState } from 'react';
import { LogsTableForm } from '../interface';
import { initLogsTableForm } from '../util';

const List: React.FC = () => {
  const urlRestParam = useParams<{ device: string }>();
  const [urlParam] = useUrlState<any>();
  const [dataList, setDataList] = useState<DeviceLog[]>([]);
  const [listParam, setListParam] = useState<DeviceLogsParam>();
  const [pagination, setPagination] = useState<Pagination>(initPagination);
  const formRef = useRef<FormInstance<LogsTableForm>>();

  // 获取列表数据
  const requestLogsList = async (param: DeviceLogsParam) => {
    if (param.sn === undefined) return;
    const { items, ...rest } = await fetchDeviceLogs(
      param,
      urlRestParam.device || '',
    );
    setPagination(rest);
    setDataList(items);
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          if (!form) return;
          const formData: LogsTableForm = form.getFieldsValue();
          // setUrlParam({ sn: formData.sn });
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: `/${urlRestParam.device}/log?sn=${formData.sn}`,
            },
          });
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          form?.resetFields();
          // setUrlParam({});
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: `/${urlRestParam.device}/log`,
            },
          });
        }}
      >
        重置
      </Button>,
    ];
  };

  const onPaginationChanged = (page: number, pageSize: number) => {
    if (!listParam) return;

    setListParam({
      ...listParam,
      ...antdUtils.getPaginatorParamByTablePaginationChange(
        listParam,
        page,
        pageSize,
      ),
    });
  };

  const columns: Array<ProColumns<DeviceLog>> = [
    {
      title: '上传日期',
      dataIndex: 'createdAt',
      search: false,
      render: (_, row) => dayjs(row.createAt).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '来自',
      dataIndex: 'src',
      search: false,
    },
    {
      title: '内容',
      dataIndex: 'log',
      search: false,
    },
    {
      title: 'SN',
      dataIndex: 'sn',
      hideInTable: true,
    },
  ];

  useEffect(() => {
    if (!formRef) return;
    const form = formRef.current;
    if (urlParam) {
      form?.setFieldsValue({ ...urlParam });
      setListParam({ ...initPaginatorParam, ...urlParam });
      return;
    }
    form?.setFieldsValue({ ...initLogsTableForm });
    setListParam({ ...initPaginatorParam });
  }, [formRef, urlParam]);

  useEffect(() => {
    if (listParam) requestLogsList(listParam);
  }, [listParam]);

  return (
    <ProTable<DeviceLog>
      dataSource={dataList}
      columns={columns}
      defaultSize="small"
      rowKey="id"
      formRef={formRef}
      search={{
        defaultCollapsed: false,
        span: 6,
        optionRender: searchOptionRender,
      }}
      scroll={{
        x: columns
          .filter((col) => col.dataIndex !== 'action')
          .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
      }}
      pagination={{
        ...antdUtils.transferPaginationToTablePagination(pagination),
        onChange: onPaginationChanged,
      }}
    />
  );
};

export default List;
