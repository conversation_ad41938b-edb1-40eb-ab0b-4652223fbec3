import {
  DeviceModule,
  DeviceModuleParam,
  SearchTypeEnum,
  WifiSearchTypeEnum,
} from '@/models/device/interface';
import {
  DeviceModuleForm,
  DeviceSearchForm,
  DeviceSnForm,
  LogsTableForm,
} from './interface';

export const hasDependentHardwareDeviceList = [
  'd4sh',
  'd4sh-2',
  'd4h',
  'd4h-2',
];

export const initDeviceSearchForm: DeviceSearchForm = {
  type: SearchTypeEnum.ID,
  s: '',
};

export const initWifiDeviceSearchForm: DeviceSearchForm = {
  type: WifiSearchTypeEnum.ID,
  s: '',
};

export const initDeviceSnForm: DeviceSnForm = {
  sn: '',
  mac: '',
  chipId: '',
};

export const initDeviceModuleForm: DeviceModuleForm = {
  module: '',
  sort: null,
  minVersion: null,
  logLevel: null,
  updateTime: null,
};

// Device Module相关util
export const transferDeviceModuleToFormData = (
  deviceModule?: DeviceModule,
): DeviceModuleForm => {
  const form: DeviceModuleForm = { ...initDeviceModuleForm };
  if (deviceModule) {
    form.module = deviceModule.id;
    form.sort = deviceModule.priority;
    form.minVersion = deviceModule.minVersion;
    form.logLevel = deviceModule.logLevel;
    form.updateTime = deviceModule.upgradeTime;
  }
  return form;
};

export const transferDeviceModuleFromToParam = (
  formData: DeviceModuleForm,
  hardware?: number,
): DeviceModuleParam => {
  const param: DeviceModuleParam = {
    id: formData.module,
    priority: formData.sort || 0,
    minVersion: formData.minVersion || 0,
    logLevel: formData.logLevel || 0,
    upgradeTime: formData.updateTime || 0,
  };
  if (hardware) {
    param.hardware = hardware;
  }
  return param;
};

// 日志相关
export const initLogsTableForm: LogsTableForm = {
  sn: '',
};

/**
 * 验证输入字符串是否符合应用程序版本号的格式。
 *
 * 版本号格式支持以下几种形式：
 * - 单独的主版本号（如 "10"）
 * - 主版本号与次版本号（如 "10.32"）
 * - 完整的主版本号、次版本号与修订号（如 "10.32.0"）
 *
 * @param {string | undefined} value 待验证的版本号字符串。可以为 `undefined` 或空字符串。
 * @returns {boolean} 如果输入值符合版本号格式，返回 `true`；否则返回 `false`。
 */
export const validAppVersionFormat = (value?: string) => {
  if (!value) return false;
  const reg = new RegExp(/^\d+(\.\d+)?(\.\d+)?$/);
  if (!reg.test(value)) {
    return false;
  }
  return true;
};
