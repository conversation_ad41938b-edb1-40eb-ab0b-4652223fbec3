import { Input } from 'antd';
import React from 'react';

// interface Props {
//   id: number;
// }

const LogLevel: React.FC = () => {
  // const [level, setLevel] = useState('');
  // const onInputChanged = (ev: ChangeEvent<HTMLInputElement>) => setLevel(ev.target.value);

  return (
    <Input
      placeholder="请输入Log等级"
      // onChange={onInputChanged}
    />
  );
};

export default LogLevel;
