import { Device } from '@/models/device/interface';
import { DownOutlined } from '@ant-design/icons';
import { history } from '@umijs/max';
import { Dropdown, Menu, MenuProps, Space } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import { TableRowActionEnum } from './interface';
import { initActionTypeList } from './util';

export interface Props {
  actionTypeList?: TableRowActionEnum[];
  deviceName: string;
  device?: Device;
  // firmware?: Firmware;
  // deviceSn: DeviceSn;

  detailCb?: () => void;
  logLevelCb?: () => void;
  showPrivateKeyCb?: () => void;
  restartCb?: () => void;
  bindOrUnbindCb?: () => void;
  updateCb?: () => void;
  deleteCb?: () => void;
  downloadCb?: () => void;
  editCb?: () => void;
  internalTestDeviceCb?: () => void;
  notifyUpgradeCb?: () => void;
  logCb?: () => void;
  switchLiveType?: () => void;
  sendCommandCb?: () => void;
}

const DeviceTableRowAction: React.FC<Props> = ({
  actionTypeList = [...initActionTypeList],
  deviceName,
  device,
  // deviceSn,
  detailCb,
  logLevelCb,
  showPrivateKeyCb,
  restartCb,
  bindOrUnbindCb,
  updateCb,
  deleteCb,
  downloadCb,
  editCb,
  internalTestDeviceCb,
  notifyUpgradeCb,
  logCb,
  switchLiveType,
  sendCommandCb,
}: Props) => {
  const [actionList, setActionList] = useState<
    Array<{ label: string; key: TableRowActionEnum }>
  >([]);
  const allActions = useMemo<{
    [key in TableRowActionEnum]: { label: string; key: TableRowActionEnum };
  }>(
    () => ({
      [TableRowActionEnum.RESTART]: {
        label: '重启设备',
        key: TableRowActionEnum.RESTART,
      },
      [TableRowActionEnum.SET_LOG_LEVEL]: {
        label: '设置Log等级',
        key: TableRowActionEnum.SET_LOG_LEVEL,
      },
      [TableRowActionEnum.DETAIL]: {
        label: '设备详情',
        key: TableRowActionEnum.DETAIL,
      },
      [TableRowActionEnum.SHOW_PRIVATE_KEY]: {
        label: '显示密钥',
        key: TableRowActionEnum.SHOW_PRIVATE_KEY,
      },
      [TableRowActionEnum.BIND_OR_UNBIND]: {
        label: '解绑/绑定',
        key: TableRowActionEnum.BIND_OR_UNBIND,
      },
      [TableRowActionEnum.BIND_HISTORY]: {
        label: '绑定历史',
        key: TableRowActionEnum.BIND_HISTORY,
      },
      [TableRowActionEnum.UPDATE]: {
        label: '修改',
        key: TableRowActionEnum.UPDATE,
      },
      [TableRowActionEnum.DOWNLOAD]: {
        label: '下载',
        key: TableRowActionEnum.DOWNLOAD,
      },
      [TableRowActionEnum.EDIT]: {
        label: '编辑',
        key: TableRowActionEnum.EDIT,
      },
      [TableRowActionEnum.INTERNAL_TEST_DEVICE]: {
        label: '内测设备',
        key: TableRowActionEnum.INTERNAL_TEST_DEVICE,
      },
      [TableRowActionEnum.NOTIFY_UPGRADE]: {
        label: '通知升级',
        key: TableRowActionEnum.NOTIFY_UPGRADE,
      },
      [TableRowActionEnum.DELETE]: {
        label: '删除',
        key: TableRowActionEnum.DELETE,
      },
      [TableRowActionEnum.LOG]: {
        label: 'Log',
        key: TableRowActionEnum.LOG,
      },
      [TableRowActionEnum.LOG_WITH_FILE]: {
        label: 'Log',
        key: TableRowActionEnum.LOG_WITH_FILE,
      },
      [TableRowActionEnum.SWITCH_LIVE_TYPE]: {
        label: '切换直播方案',
        key: TableRowActionEnum.SWITCH_LIVE_TYPE,
      },
      [TableRowActionEnum.SEND_COMMAND]: {
        label: '下发指令',
        key: TableRowActionEnum.SEND_COMMAND,
      },
    }),
    [],
  );

  const actionFunctions: { [key in TableRowActionEnum]: () => void } = {
    [TableRowActionEnum.RESTART]: () => restartCb && restartCb(),
    [TableRowActionEnum.SET_LOG_LEVEL]: () => logLevelCb && logLevelCb(),
    [TableRowActionEnum.DETAIL]: () => detailCb && detailCb(),
    [TableRowActionEnum.SHOW_PRIVATE_KEY]: () =>
      showPrivateKeyCb && showPrivateKeyCb(),
    [TableRowActionEnum.BIND_OR_UNBIND]: () =>
      bindOrUnbindCb && bindOrUnbindCb(),
    [TableRowActionEnum.BIND_HISTORY]: () => {
      history.push(`/link_history/${deviceName}/${device?.id}`);
    },
    [TableRowActionEnum.UPDATE]: () => updateCb && updateCb(),
    [TableRowActionEnum.DELETE]: () => deleteCb && deleteCb(),
    [TableRowActionEnum.DOWNLOAD]: () => downloadCb && downloadCb(),
    [TableRowActionEnum.EDIT]: () => editCb && editCb(),
    [TableRowActionEnum.INTERNAL_TEST_DEVICE]: () =>
      internalTestDeviceCb && internalTestDeviceCb(),
    [TableRowActionEnum.NOTIFY_UPGRADE]: () =>
      notifyUpgradeCb && notifyUpgradeCb(),
    [TableRowActionEnum.LOG]: () => logCb && logCb(),
    [TableRowActionEnum.LOG_WITH_FILE]: () => logCb && logCb(),
    [TableRowActionEnum.SWITCH_LIVE_TYPE]: () =>
      switchLiveType && switchLiveType(),
    [TableRowActionEnum.SEND_COMMAND]: () => sendCommandCb && sendCommandCb(),
  };

  const onClick: MenuProps['onClick'] = ({ key }) =>
    actionFunctions[key as TableRowActionEnum]();

  useEffect(() => {
    if (actionList.length) return;
    setActionList(actionTypeList.map((item) => ({ ...allActions[item] })));
  }, [actionTypeList]);

  const menu = <Menu onClick={onClick} items={actionList} />;

  return (
    <>
      <Dropdown overlay={menu} trigger={['click']}>
        <a onClick={(e) => e.preventDefault()}>
          <Space>
            操作
            <DownOutlined />
          </Space>
        </a>
      </Dropdown>
    </>
  );
};

export default DeviceTableRowAction;
