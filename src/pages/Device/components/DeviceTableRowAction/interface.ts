export enum TableRowActionEnum {
  RESTART = 'restart',
  SET_LOG_LEVEL = 'setLogLevel',
  DETAIL = 'detail',
  SHOW_PRIVATE_KEY = 'showPrivateKey',
  BIND_OR_UNBIND = 'bindOrUnbind',
  BIND_HISTORY = 'bindHistory',
  UPDATE = 'update',
  DELETE = 'delete',
  DOWNLOAD = 'download',
  EDIT = 'edit',
  INTERNAL_TEST_DEVICE = 'internalTestDevice',
  NOTIFY_UPGRADE = 'notifyUpgrade',
  LOG = 'log',
  LOG_WITH_FILE = 'logWithFile',
  SWITCH_LIVE_TYPE = 'switchLiveType',
  SEND_COMMAND = 'sendCommand',
}
