import { initLocaleContentForm } from '@/components/LocaleContentModal/util';
import { LocaleObject, SelectOption } from '@/models/common.interface';
import {
  FirmwareVersionDetail,
  FirmwareVersionDetailParam,
  FirmwareVersionModule,
  FirmwareVersionParam,
  ForceUpgradeEnum,
  OpenEnum,
} from '@/models/device/interface';
import {
  FirmwareVersionForm,
  FirmwareVersionLocaleForm,
  FirmwareVersionTableForm,
} from './interface';

export const initFirmwareVersionTableForm: FirmwareVersionTableForm = {
  version: '',
};

export const initialFirmwareVersionForm: FirmwareVersionForm = {
  hardware: '',
  version: '',
  isForceUpgrade: ForceUpgradeEnum.FORCE,
  miniVersion: '',
  isOpen: OpenEnum.BETA,
  hardwareChoice: {},
  remark: '',
};

export const isForceUpgradeOptions: SelectOption[] = [
  {
    label: '自动',
    value: ForceUpgradeEnum.FORCE,
  },
  {
    label: '手动',
    value: ForceUpgradeEnum.NOT_FORCE,
  },
];

export const isOpenOptions: SelectOption[] = [
  {
    label: '内测',
    value: OpenEnum.BETA,
  },
  {
    label: '灰度',
    value: OpenEnum.GRAY,
  },
  {
    label: '公开',
    value: OpenEnum.PUBLIC,
  },
];

export const initFirmwareVersionLocaleForm: FirmwareVersionLocaleForm = {
  ...initLocaleContentForm,
  note: '',
};

// 将接口返回的数据转换为formData
export const transferDetailToFormData = (detail: FirmwareVersionDetail) => {
  const formData: FirmwareVersionForm = {
    hardware: `${detail.hardware}`,
    version: detail.version,
    isForceUpgrade: detail.forceUpgrade,
    miniVersion: detail.appMinVersion,
    isOpen: detail.open,
    hardwareChoice: {},
    remark: detail.remark || '',
  };
  detail.details.forEach((_detail) => {
    formData.hardwareChoice[_detail.module] = _detail.id;
  });
  if (detail.iosVersion) {
    formData.iosVersion = detail.iosVersion;
  }
  if (detail.androidVersion) {
    formData.androidVersion = detail.androidVersion;
  }
  return formData;
};

// 将接口返回的releaseNotes信息转换为formData
export const transferReleaseNotesToLocaleContentFormDataList = (
  releaseNotes: {
    [key: string]: string;
  } = {},
): FirmwareVersionLocaleForm[] => {
  const formData: FirmwareVersionLocaleForm[] = [];
  for (const language in releaseNotes) {
    if (releaseNotes[language]) {
      formData.push({
        language,
        note: releaseNotes[language],
      });
    }
  }
  return formData;
};

// 转换formData为param之前的校验
export const validFormDataValue = (formData: FirmwareVersionForm) => {
  if (formData.isOpen === OpenEnum.GRAY && !formData.androidVersion) {
    throw new Error('请填写android的灰度版本号');
  } else if (formData.isOpen === OpenEnum.GRAY && !formData.iosVersion) {
    throw new Error('请填写iOS的灰度版本号');
  }
};

// 将formData转换为param
export const transferFormDataToParam = (
  formData: FirmwareVersionForm,
  localeFormDataList: FirmwareVersionLocaleForm[],
  moduleList: FirmwareVersionModule[],
  id: number,
): FirmwareVersionParam => {
  const detailListParam: FirmwareVersionDetailParam[] = [];
  const releaseNotes: LocaleObject = {};
  const param: FirmwareVersionParam = {
    id: id ? `${id}` : '',
    file: JSON.stringify({ size: '', url: '' }),
    hardware: +formData.hardware,
    version: formData.version,
    forceUpgrade: formData.isForceUpgrade,
    appMinVersion: formData.miniVersion ? `${formData.miniVersion}` : '',
    open: formData.isOpen,
    releaseNotes: '',
    details: '',
  };
  if (formData.remark) {
    param.remark = formData.remark;
  }
  localeFormDataList.forEach((localeFormData) => {
    releaseNotes[localeFormData.language] = localeFormData.note;
  });
  param.releaseNotes = JSON.stringify(releaseNotes);
  if (formData.hardwareChoice) {
    for (const key in formData.hardwareChoice) {
      if (formData.hardwareChoice[key]) {
        const item = formData.hardwareChoice[key];
        const module = moduleList.find((m) => m.module === key);
        const { note, ...moduleItem } =
          module?.items.find((mi) => mi.id === item) || {};
        if (moduleItem)
          detailListParam.push(moduleItem as FirmwareVersionDetailParam);
      }
    }
    param.details = JSON.stringify(detailListParam);
  }
  if (formData.isOpen === OpenEnum.GRAY && formData.androidVersion) {
    param.androidVersion = formData.androidVersion;
  }
  if (formData.isOpen === OpenEnum.GRAY && formData.iosVersion) {
    param.iosVersion = formData.iosVersion;
  }
  return param;
};
