import { LocaleContentForm } from '@/components/LocaleContentModal/interface';
import { ForceUpgradeEnum, OpenEnum } from '@/models/device/interface';

export interface FirmwareVersionTableForm {
  hardware?: string;
  version: string;
}

export interface FirmwareVersionForm {
  hardware: string;
  version: string;
  isForceUpgrade: ForceUpgradeEnum;
  miniVersion?: string;
  isOpen: OpenEnum;
  hardwareChoice: Partial<{ [key: string]: number }>;
  remark: string;
  // 灰度时使用的字段
  androidVersion?: string;
  iosVersion?: string;
}

export interface FirmwareVersionLocaleForm extends LocaleContentForm {
  note: string;
}
