import LocaleContentModal from '@/components/LocaleContentModal';
import { ApiSuccessEnum, SelectOption } from '@/models/common.interface';
import { ConnectState } from '@/models/connect';
import {
  fetchFirmwareDetailsGroupByModule,
  fetchFirmwareVersionTxDetail,
  fetchFirmwareVersionTxSaving,
} from '@/models/device/fetch';
import { FirmwareVersionModule, OpenEnum } from '@/models/device/interface';
import global from '@/utils/global';
import { PlusOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { history, useDispatch, useParams, useSelector } from '@umijs/max';
import {
  Button,
  Col,
  Form,
  Input,
  List,
  Row,
  Select,
  Typography,
  message,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { validAppVersionFormat } from '../util';
import { FirmwareVersionForm, FirmwareVersionLocaleForm } from './interface';
import {
  initFirmwareVersionLocaleForm,
  initialFirmwareVersionForm,
  isForceUpgradeOptions,
  isOpenOptions,
  transferDetailToFormData,
  transferFormDataToParam,
  transferReleaseNotesToLocaleContentFormDataList,
  validFormDataValue,
} from './util';

interface ModuleOption {
  options: Array<SelectOption<number>>;
  label: string;
  name: string;
}

const Edit: React.FC = () => {
  const dispatch = useDispatch();
  const urlRestParams = useParams<{
    device: string;
    id: string;
    hardware: string;
  }>();
  const [form] = Form.useForm<FirmwareVersionForm>();
  const [loading, setLoading] = useState(false);
  const [moduleOptionList, setModuleOptionList] = useState<ModuleOption[]>();
  const [visible, setVisible] = useState(false);
  const localeOptions: SelectOption[] = useSelector(
    ({ app }: ConnectState) => app.localeOptions,
  );
  const [selectedLocaleContent, setSelectedLocaleContent] =
    useState<FirmwareVersionLocaleForm>();
  const [localeContentList, setLocaleContentList] = useState<
    FirmwareVersionLocaleForm[]
  >([]);
  const [moduleList, setModuleList] = useState<FirmwareVersionModule[]>([]);
  const layout = {
    labelCol: { offset: 2, span: 20 },
    wrapperCol: { offset: 2, span: 20 },
  };

  const isOpen = Form.useWatch('isOpen', form);

  // 根据id获取详情数据
  const requestDetailById = async (id: number) => {
    const detail = await fetchFirmwareVersionTxDetail(
      `${id}`,
      urlRestParams.device || '',
    );
    const formData = transferDetailToFormData(detail);
    form.setFieldsValue(formData);
    setLocaleContentList(
      transferReleaseNotesToLocaleContentFormDataList(detail.releaseNotes),
    );
  };

  const requestFirmwareDetailsGroupByModule = async () => {
    const _moduleList = await fetchFirmwareDetailsGroupByModule(
      urlRestParams.hardware || '',
      urlRestParams.device || '',
    );
    const _moduleOptionList: ModuleOption[] = _moduleList.map((module) => ({
      options: module.items.map((item) => ({
        value: item.id,
        label: `版本:${item.version} (${item.note})`,
      })),
      label: module.module,
      name: module.module,
    }));
    setModuleList(_moduleList);
    setModuleOptionList(_moduleOptionList);
    // 默认选择第一项
    if (!+(urlRestParams.id || 0)) {
      _moduleOptionList
        .filter(
          (moduleOption) => moduleOption.options && moduleOption.options.length,
        )
        .forEach((moduleOption) => {
          form.setFieldValue(
            ['hardwareChoice', moduleOption.name],
            moduleOption.options[0].value,
          );
        });
    }
  };

  // 提交form表单
  const submit = async (formData: FirmwareVersionForm) => {
    try {
      validFormDataValue(formData);
    } catch (error) {
      message.warning((error as Error).message);
      return;
    }
    setLoading(true);
    const _param = transferFormDataToParam(
      formData,
      localeContentList,
      moduleList,
      +(urlRestParams?.id || 0),
    );
    let result = '';
    let state = '';
    try {
      if (_param && +(_param.id || 0)) {
        state = '更新';
      } else {
        // state = '创建';
      }
      result = await fetchFirmwareVersionTxSaving(
        _param,
        urlRestParams.device || '',
      );
      if (result === ApiSuccessEnum.success) {
        message.success(`${state}成功！`);
        history.back();
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  // 编辑本地化内容
  const editLocaleContent = (localeContent?: FirmwareVersionLocaleForm) => {
    setSelectedLocaleContent(localeContent);
    setVisible(true);
  };

  // 删除本地化内容
  const deleteLocaleContent = (index: number) => {
    localeContentList.splice(index, 1);
    setLocaleContentList([...localeContentList]);
  };

  const hideUserModal = () => {
    setVisible(false);
    setSelectedLocaleContent(undefined);
  };

  const setLocaleContent = (formValue: FirmwareVersionLocaleForm) => {
    const existedLocaleContent: FirmwareVersionLocaleForm | undefined =
      localeContentList.find((item) => item.language === formValue.language);
    if (existedLocaleContent) {
      existedLocaleContent.note = formValue.note;
      setLocaleContentList([...localeContentList]);
    } else {
      setLocaleContentList([...localeContentList, formValue]);
    }
  };

  useEffect(() => {
    if (!urlRestParams) return;
    if (urlRestParams.hardware)
      form.setFieldValue('hardware', urlRestParams.hardware);
    if (urlRestParams.id && +urlRestParams.id) {
      requestDetailById(+urlRestParams.id);
    }
  }, [urlRestParams, form]);

  useEffect(() => {
    // 额外dva接口请求
    dispatch({ type: 'app/requestLocaleList' });
    requestFirmwareDetailsGroupByModule();
  }, [dispatch]);

  return (
    <>
      <ProCard>
        <Form
          {...layout}
          form={form}
          layout="vertical"
          onFinish={submit}
          initialValues={{
            ...initialFirmwareVersionForm,
            hardware: urlRestParams.hardware,
          }}
        >
          <Form.Item
            name="hardware"
            label="硬件"
            rules={[{ required: true, message: '请输入硬件' }]}
          >
            <Input disabled placeholder="请输入硬件" />
          </Form.Item>
          <Form.Item
            name="version"
            label="版本号"
            rules={[{ required: true, message: '请输入版本号' }]}
          >
            <Input
              disabled={!!+(urlRestParams.id || 0)}
              placeholder="请输入版本号"
            />
          </Form.Item>
          {urlRestParams.device &&
          global.firmwareInternalRemarkDeviceList.includes(
            urlRestParams.device.toLocaleLowerCase(),
          ) ? (
            <Form.Item name="remark" label="内部备注">
              <Input.TextArea
                placeholder="请输入内部备注"
                maxLength={200}
                showCount
              />
            </Form.Item>
          ) : null}
          <Form.Item
            name="isForceUpgrade"
            label="升级方式"
            rules={[{ required: true, message: '请选择升级方式' }]}
          >
            <Select
              options={isForceUpgradeOptions}
              placeholder="请选择升级方式"
            />
          </Form.Item>
          <Form.Item name="miniVersion" label="App要求最低版本号">
            <Input />
          </Form.Item>
          <Form.Item
            name="isOpen"
            label="公开"
            rules={[{ required: true, message: '请选择是否公开' }]}
          >
            <Select
              options={isOpenOptions.filter((opt) => {
                return (
                  global.graySupportedDeviceList.includes(
                    (urlRestParams.device || '').toLowerCase(),
                  ) ||
                  opt.value === OpenEnum.BETA ||
                  opt.value === OpenEnum.PUBLIC
                );
              })}
              placeholder="请选择是否公开"
            />
          </Form.Item>

          {isOpen === OpenEnum.GRAY ? (
            <Row>
              <Col span={11} offset={1}>
                <Form.Item
                  name="androidVersion"
                  label="Android 版本号"
                  rules={[
                    { required: true, message: '请输入Android版本号' },
                    {
                      validator: (_, value) => {
                        // 已经有校验可选和非可选逻辑了，此处不在重复判断
                        if (!value) return Promise.resolve();
                        const valid = validAppVersionFormat(value);
                        if (!valid) {
                          return Promise.reject('版本号不合法，请重新输入');
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <Input placeholder="请输入Android版本号" />
                </Form.Item>
              </Col>
              <Col span={11}>
                <Form.Item
                  name="iosVersion"
                  label="iOS版本号"
                  rules={[
                    { required: true, message: '请输入iOS版本号' },
                    {
                      validator: (_, value) => {
                        // 已经有校验可选和非可选逻辑了，此处不在重复判断
                        if (!value) return Promise.resolve();
                        const valid = validAppVersionFormat(value);
                        if (!valid) {
                          return Promise.reject('版本号不合法，请重新输入');
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <Input placeholder="请输入iOS版本号" />
                </Form.Item>
              </Col>
            </Row>
          ) : null}

          <Form.Item style={{ marginBottom: 0 }}>
            <Typography.Title level={5}>固件选择</Typography.Title>
          </Form.Item>
          {(moduleOptionList || []).map((moduleOption) => (
            <Form.Item
              key={moduleOption.name}
              label={moduleOption.label}
              name={['hardwareChoice', moduleOption.name]}
              rules={[
                { required: true, message: `请选择${moduleOption.label}` },
              ]}
            >
              <Select
                disabled={!!+(urlRestParams.id || 0)}
                placeholder={`请选择${moduleOption.label}`}
                options={moduleOption.options}
              />
            </Form.Item>
          ))}
          <Form.Item>
            <Button icon={<PlusOutlined />} onClick={() => editLocaleContent()}>
              本地化内容
            </Button>
          </Form.Item>
          <Form.Item>
            <List<FirmwareVersionLocaleForm>
              size="small"
              bordered={false}
              dataSource={localeContentList}
              renderItem={(item, index) => (
                <List.Item>
                  <div>
                    <p>
                      <Typography.Text strong>语言：</Typography.Text>
                      {localeOptions.find((opt) => opt.value === item.language)
                        ?.label || item.language}
                      <Button
                        type="link"
                        onClick={() => editLocaleContent(item)}
                      >
                        编辑
                      </Button>{' '}
                      <Button
                        type="link"
                        onClick={() => deleteLocaleContent(index)}
                      >
                        删除
                      </Button>
                    </p>
                    <p>
                      <Typography.Text strong>名称：</Typography.Text>
                      {item.note}
                    </p>
                  </div>
                </List.Item>
              )}
            />
          </Form.Item>
          <Form.Item style={{ textAlign: 'center' }}>
            <Button
              type="primary"
              htmlType="submit"
              style={{ marginRight: 16 }}
              loading={loading}
              disabled={loading}
            >
              提交
            </Button>
            {/* <Button htmlType="reset" style={{ marginRight: 16 }} danger>
              重置
            </Button> */}
            <Button type="default" onClick={history.back}>
              取消
            </Button>
          </Form.Item>
        </Form>
      </ProCard>
      <LocaleContentModal<FirmwareVersionLocaleForm>
        visible={visible}
        localeOptionList={localeOptions}
        initialFormValues={initFirmwareVersionLocaleForm}
        detail={selectedLocaleContent}
        onCancel={hideUserModal}
        onConfirm={(fd) => {
          setLocaleContent(fd);
          hideUserModal();
        }}
      >
        <Form.Item
          colon
          name="note"
          label="ReleaseNotes"
          rules={[{ required: true, message: '请输入发布内容' }]}
        >
          <Input.TextArea />
        </Form.Item>
      </LocaleContentModal>
    </>
  );
};

export default Edit;
