// import { BetaDeviceDetail, BetaDeviceParam } from '@/models/device/interface';
import { BetaDeviceForm } from './interface';

export const initialBetaDeviceForm: BetaDeviceForm = {};

// // 将formData转换为param
// export const transferFormDataToParam = (formData: BetaDeviceForm, id?: number): BetaDeviceParam => {
//   const param: BetaDeviceParam = {};
//   id && (param.id = id);
//   return param;
// };

// // 将接口返回的详情数据转换为formData
// export const transferDetailToFormData = (detail: BetaDeviceDetail): BetaDeviceForm => {
//   const formData: BetaDeviceForm = {};
//   return formData;
// };
