import { BreadcrumbInfo } from '@/models/common.interface';
import global from '@/utils/global';
import { LeftOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { Outlet, useLocation, useParams } from '@umijs/max';
import React, { useEffect, useState } from 'react';

const breadcrumbInfo: BreadcrumbInfo = {
  path: '',
  breadcrumbName: 'BetaDevice',
};

const BetaDevice: React.FC = () => {
  const location = useLocation();
  const [title] = useState(`内测设备管理`);
  const [showBackIcon, setShowBackIcon] = useState(false);
  const [breadcrumbInfoList, setBreadcrumbInfoList] = useState([
    breadcrumbInfo,
  ]);
  const param = useParams<{ device: string }>();
  useEffect(() => {
    const _breadcrumbInfoList = global.getBreadcrumbInfo(
      {
        ...breadcrumbInfo,
        breadcrumbName: global.deviceNames[param.device as string],
      },
      '',
      location,
    );
    setBreadcrumbInfoList(_breadcrumbInfoList);
    if (_breadcrumbInfoList.length > 2) {
      setShowBackIcon(true);
    }
  }, [location, param]);

  return (
    <PageContainer
      header={{
        backIcon: showBackIcon ? <LeftOutlined /> : '',
        onBack: () => history.back(),
        title,
        ghost: true,
        breadcrumb: {
          itemRender: (route) => <span>{route.title}</span>,
          routes: breadcrumbInfoList,
        },
      }}
    >
      <Outlet />
    </PageContainer>
  );
};

export default BetaDevice;
