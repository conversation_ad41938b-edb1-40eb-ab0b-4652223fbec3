import { ApiSuccessEnum } from '@/models/common.interface';
import { spanConfig, transferFormDataToParam } from '@/models/common.util';
import {
  fetchFirmwareBetaDeviceAdding,
  fetchFirmwareBetaDeviceDeletion,
  fetchFirmwareBetaDeviceList,
} from '@/models/device/fetch';
import {
  FirmwareBetaDevice,
  FirmwareBetaDeviceListParam,
} from '@/models/device/interface';
import { initFirmwareBetaDeviceListParam } from '@/models/device/util';
import { Pagination, initPagination } from '@/utils/request';
import { PlusOutlined } from '@ant-design/icons';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProTable,
} from '@ant-design/pro-components';
import { useParams } from '@umijs/max';
import { Button, Input, Modal, Popconfirm, message } from 'antd';
import { parse } from 'qs';
import React, { ChangeEvent, useEffect, useState } from 'react';

const List: React.FC = () => {
  const [listParam, setListParam] = useState<FirmwareBetaDeviceListParam>(
    initFirmwareBetaDeviceListParam,
  );
  const [dataList, setDataList] = useState<FirmwareBetaDevice[]>([]);
  const [pagination, setPagination] = useState<Pagination>(initPagination);
  const urlParam = useParams<{ device: string }>();
  const queryInfo = parse(location.href.split('?')[1]);
  const [deviceIds, setDeviceIds] = useState('');
  const [visible, setVisible] = useState(false);
  const columns: Array<ProColumns<FirmwareBetaDevice>> = [
    {
      title: '设备ID',
      dataIndex: 'deviceId',
    },
    {
      title: '操作',
      dataIndex: 'action',
      search: false,
      render: (_, row) => (
        <Popconfirm
          title="确定要删除该内测设备么？"
          onConfirm={() => deleteBetaDevice(row.deviceId)}
        >
          <Button type="link">删除</Button>
        </Popconfirm>
      ),
    },
  ];

  useEffect(() => {
    if (queryInfo) {
      requestBetaDeviceList({
        ...listParam,
        firmwareId: queryInfo.firmwareId as string,
      });
    }
  }, [listParam]);

  // 获取列表数据
  const requestBetaDeviceList = async (
    param: FirmwareBetaDeviceListParam = initFirmwareBetaDeviceListParam,
  ) => {
    const { items, ...rest } = await fetchFirmwareBetaDeviceList(
      param,
      urlParam.device,
    );
    const betaDeviceList: FirmwareBetaDevice[] = items.map((item) => ({
      deviceId: item,
    }));
    setDataList(betaDeviceList);
    setPagination(rest);
  };

  // 编辑
  const editBetaDevice = () => {
    setVisible(true);
  };

  // 删除
  const deleteBetaDevice = async (deviceId: number) => {
    const result = await fetchFirmwareBetaDeviceDeletion(
      queryInfo.firmwareId as string,
      deviceId,
      urlParam.device,
    );
    if (result === ApiSuccessEnum.success) {
      message.success('删除成功！');
      setListParam({ ...listParam });
    }
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          const _param = transferFormDataToParam<
            { deviceId: number },
            FirmwareBetaDeviceListParam
          >(form?.getFieldsValue());
          setListParam({
            ...listParam,
            ..._param,
          });
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          form?.resetFields();
        }}
      >
        重置
      </Button>,
    ];
  };

  const onOk = async () => {
    const result = await fetchFirmwareBetaDeviceAdding(
      {
        firmwareId: queryInfo.firmwareId as string,
        deviceIds,
      },
      urlParam.device,
    );
    if (result === ApiSuccessEnum.success) {
      message.success('添加成功！');
      setListParam({ ...listParam });
      onCancel();
    }
  };

  const onCancel = () => {
    setVisible(false);
    setDeviceIds('');
  };

  const onBetaDeviceIdsChanges = ({
    target: { value },
  }: ChangeEvent<HTMLTextAreaElement>) => {
    setDeviceIds(value);
  };

  const onPaginationChanged = (page: number, pageSize: number) => {
    let index = page;
    if (pageSize !== listParam.limit) {
      index = 1;
    }
    setListParam({
      ...listParam,
      limit: pageSize,
      offset: (index - 1) * pageSize,
    });
  };

  return (
    <>
      <ProTable<FirmwareBetaDevice>
        dataSource={dataList}
        columns={columns}
        defaultSize="small"
        rowKey="deviceId"
        search={{
          defaultCollapsed: false,
          span: spanConfig,
          optionRender: searchOptionRender,
        }}
        pagination={{
          pageSize: pagination.limit,
          total: pagination.total,
          showQuickJumper: true,
          onChange: onPaginationChanged,
        }}
        scroll={{
          x: columns
            .filter((col) => col.dataIndex === 'action')
            .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
        }}
        toolbar={{
          actions: [
            <Button
              key="button"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => editBetaDevice()}
            >
              新增
            </Button>,
          ],
        }}
      />
      <Modal
        title="添加内测设备"
        open={visible}
        onOk={onOk}
        onCancel={onCancel}
      >
        <Input.TextArea
          rows={5}
          value={deviceIds}
          onChange={onBetaDeviceIdsChanges}
        />
      </Modal>
    </>
  );
};

export default List;
