import {
  PackageRegionEnum,
  PackageSnListParam,
} from '@/models/package/interface';
import { initPackageSnListParam } from '@/models/package/util';
import { NFCAccountTableForm, UrlParam } from './interface';

// 将tableForm转换为urlParam
export const transferTableFormToUrlParam = (
  formData: NFCAccountTableForm,
): UrlParam => {
  const param: UrlParam = {};

  if (formData.batchNo) param.batchNo = formData.batchNo;

  if (formData.packageSn) param.sn = formData.packageSn;

  if (formData.region) param.region = `${formData.region}`;

  return param;
};

// 将urlParam转换为listParam
export const transferUrlParamToListParam = (
  urlParam: UrlParam,
): PackageSnListParam => {
  const param: PackageSnListParam = {
    ...initPackageSnListParam,
    sn: urlParam.sn,
    // secret: urlParam.secret,
    // used: urlParam.used,
    batchNo: urlParam.batchNo,
  };
  if (urlParam.region) {
    param.region = +urlParam.region as PackageRegionEnum;
  }
  // if (urlParam.baggingStart && urlParam.baggingEnd) {
  //   param.packageStartTime = +urlParam.baggingStart / 1000;
  //   param.packageEndTime = +urlParam.baggingEnd / 1000;
  // }
  // if (urlParam.signStart && urlParam.signEnd) {
  //   param.signupStartTime = +urlParam.signStart / 1000;
  //   param.signupEndTime = +urlParam.signEnd / 1000;
  // }
  return param;
};

// 将urlParam转换为tableForm
export const transferUrlParamToTableForm = (
  urlParam: UrlParam,
): NFCAccountTableForm => {
  const formData: NFCAccountTableForm = {
    packageSn: urlParam.sn,
    // packageSecret: urlParam.secret,
    // used: urlParam.used,
    batchNo: urlParam.batchNo,
  };
  if (urlParam.region) {
    formData.region = urlParam.region;
  }
  // if (urlParam.baggingStart && urlParam.baggingEnd) {
  //   formData.baggingTime = [
  //     dayjs(+urlParam.baggingStart),
  //     dayjs(+urlParam.baggingEnd),
  //   ];
  // }
  // if (urlParam.signStart && urlParam.signEnd) {
  //   formData.signTime = [dayjs(+urlParam.signStart), dayjs(+urlParam.signEnd)];
  // }
  return formData;
};
