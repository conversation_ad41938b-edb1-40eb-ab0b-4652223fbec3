import { ApiSuccessEnum } from '@/models/common.interface';
import { postMessageFunction, spanConfig } from '@/models/common.util';
import { fetchPackageSnList, fetchPackageUnlock } from '@/models/package/fetch';
import {
  PackageLockEnum,
  PackageRegionEnum,
  PackageSn,
  PackageSnListParam,
} from '@/models/package/interface';
import { initPackageSnListParam } from '@/models/package/util';
import { Pagination, initPagination } from '@/utils/request';
import { useParams } from '@@/exports';
import useUrlState from '@ahooksjs/use-url-state';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProTable,
} from '@ant-design/pro-components';
import { Button, FormInstance, Popconfirm, message } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { NFCAccountTableForm, UrlParam } from '../interface';
import {
  transferTableFormToUrlParam,
  transferUrlParamToListParam,
  transferUrlParamToTableForm,
} from '../util';

const List: React.FC = () => {
  const [urlParam] = useUrlState<UrlParam>();
  const formRef = useRef<FormInstance<NFCAccountTableForm>>();
  const urlRestParam = useParams<{ device: string }>();
  const [dataList, setDataList] = useState<PackageSn[]>([]);
  const [listParam, setListParam] = useState<PackageSnListParam>();
  const [pagination, setPagination] = useState<Pagination>(initPagination);

  // 获取列表数据
  const requestNFCAccountList = async (
    param: PackageSnListParam = initPackageSnListParam,
  ) => {
    if (!urlRestParam.device) {
      return;
    }
    const { items, ...rest } = await fetchPackageSnList(
      param,
      urlRestParam.device,
    );
    setPagination(rest);
    setDataList(items);
  };

  // 编辑
  // const editNFCAccount = (id: number) => {
  //   history.push(`/edit/${id}`);
  // };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          const formData: NFCAccountTableForm = form?.getFieldsValue();
          const _urlParam = transferTableFormToUrlParam(formData);
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: `/t6/nfc_account_list`,
              param: _urlParam as Record<string, string>,
            },
          });
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: `/t6/nfc_account_list`,
            },
          });
        }}
      >
        重置
      </Button>,
    ];
  };

  const onPaginationChanged = (page: number, pageSize: number) => {
    let index = page;
    if (pageSize !== listParam?.limit) {
      index = 1;
    }
    console.log('onPaginationChanged', listParam);
    setListParam({
      ...listParam,
      limit: pageSize,
      offset: (index - 1) * pageSize,
    });
  };

  // 解锁
  const unlock = async (record: PackageSn) => {
    if (!urlRestParam.device) {
      return;
    }
    const result = await fetchPackageUnlock(
      { sn: record.packageSn, secret: record.packageSecret },
      urlRestParam.device,
    );
    if (!result || result !== ApiSuccessEnum.success) return;
    message.success('解锁成功');
    setListParam({ ...(listParam || initPackageSnListParam) });
  };

  const columns: Array<ProColumns<PackageSn>> = [
    {
      title: '垃圾袋盒SN',
      dataIndex: 'packageSn',
    },
    {
      title: '垃圾袋盒secret',
      dataIndex: 'packageSecret',
      search: false,
    },
    // {
    //   title: '已使用次数',
    //   dataIndex: 'used',
    //   valueType: 'digit',
    //   width: 120,
    //   search: false,
    // },
    // {
    //   title: '最近一次套袋',
    //   dataIndex: 'baggingTime',
    //   width: 180,
    //   valueType: 'dateRange',
    //   search: false,
    //   render: (_, record) =>
    //     record.baggingTime
    //       ? dayjs.unix(record.baggingTime).format('YYYY-MM-DD HH:mm:ss')
    //       : '-',
    // },
    {
      title: '所属区域',
      dataIndex: 'region',
      width: 120,
      valueType: 'select',
      valueEnum: {
        [PackageRegionEnum.CHINA]: '国内版',
        [PackageRegionEnum.OVERSEA]: '海外版',
      },
    },
    {
      title: '解锁状态',
      dataIndex: 'lock',
      width: 120,
      search: false,
      valueEnum: {
        [PackageLockEnum.UNLOCKED]: '已解锁',
        [PackageLockEnum.LOCKED]: '未解锁',
      },
    },
    // {
    //   title: '首次注册时间',
    //   dataIndex: 'signTime',
    //   width: 180,
    //   valueType: 'dateRange',
    //   search: false,
    //   render: (_, record) =>
    //     record.signTime
    //       ? dayjs.unix(record.signTime).format('YYYY-MM-DD HH:mm:ss')
    //       : '-',
    // },
    {
      title: '注册批次号',
      dataIndex: 'batchNo',
      width: 120,
      search: false,
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 100,
      search: false,
      render: (_, record) => (
        <Popconfirm
          title="提交后，垃圾袋盒将解锁，是否确认？"
          onConfirm={() => unlock(record)}
        >
          <Button
            disabled={record.lock === PackageLockEnum.UNLOCKED}
            type="link"
          >
            解锁
          </Button>
        </Popconfirm>
      ),
    },
  ];

  useEffect(() => {
    if (!urlParam) {
      setListParam({ ...initPackageSnListParam });
      return;
    }
    const formData = transferUrlParamToTableForm(urlParam);
    const form = formRef.current;
    console.log(form, formData);
    form?.setFieldsValue(formData);

    const _listParam = transferUrlParamToListParam(urlParam);
    setListParam(_listParam);
  }, [urlParam]);

  useEffect(() => {
    if (listParam) requestNFCAccountList(listParam);
  }, [listParam]);

  return (
    <ProTable<PackageSn>
      dataSource={dataList}
      formRef={formRef}
      columns={columns}
      defaultSize="small"
      rowKey="packageSn"
      search={{
        defaultCollapsed: false,
        span: spanConfig,
        optionRender: searchOptionRender,
      }}
      scroll={{
        x: columns
          .filter((col) => col.dataIndex !== 'action')
          .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
      }}
      options={{ reload: () => listParam && setListParam({ ...listParam }) }}
      pagination={{
        pageSize: pagination.limit,
        total: pagination.total,
        showQuickJumper: true,
        onChange: onPaginationChanged,
      }}
    />
  );
};

export default List;
