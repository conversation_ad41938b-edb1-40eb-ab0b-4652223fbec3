import { Device } from '@/models/device/interface';
import { getOTAStatusInfo } from '@/models/device/util';
import { Descriptions, Drawer } from 'antd';
import dayjs from 'dayjs';
import React from 'react';

interface Props {
  device: Device;
  visible: boolean;
  onClose?: () => void;
}

const DeviceDetail: React.FC<Props> = ({ device, visible, onClose }: Props) => {
  const statusOptions = ['离线', '在线'];
  const childrenLockOptions = ['关闭', '开启'];

  return (
    <Drawer
      width={450}
      title="设备详情"
      placement="right"
      onClose={onClose}
      open={visible}
    >
      <Descriptions column={1} bordered>
        <Descriptions.Item label="ID">{device.id}</Descriptions.Item>
        <Descriptions.Item label="MAC">{device.mac}</Descriptions.Item>
        <Descriptions.Item label="Sn">{device.sn}</Descriptions.Item>
        <Descriptions.Item label="注册时间">
          {dayjs(device.createdAt).format('YYYY-MM-DD')}
        </Descriptions.Item>
        <Descriptions.Item label="硬/固件">{`${device.hardware}/${device.firmware}`}</Descriptions.Item>
        {/* <Descriptions.Item label="模块">{`${device.firmwareDetails[0].module}:${device.firmwareDetails[0].version}`}</Descriptions.Item> */}
        <Descriptions.Item label="时区">{device.timezone}</Descriptions.Item>
        <Descriptions.Item label="主人ID">
          {device.relation?.userId}
        </Descriptions.Item>
        <Descriptions.Item label="宠物ID">--</Descriptions.Item>
        <Descriptions.Item label="状态">
          {device.state.pim ? `${statusOptions[device.state.pim]}` : '未知'}
        </Descriptions.Item>
        <Descriptions.Item label="OTA状态">
          {getOTAStatusInfo(device.state.ota)}
        </Descriptions.Item>
        <Descriptions.Item label="密钥">{device.secret}</Descriptions.Item>
        <Descriptions.Item label="区域">{device.locale}</Descriptions.Item>
        <Descriptions.Item label="儿童锁开关">
          {childrenLockOptions[device.settings?.manualLock] || '未知'}
        </Descriptions.Item>
        {device.state.wifi ? (
          <>
            <Descriptions.Item label="ssid">
              {device.state.wifi.ssid}
            </Descriptions.Item>
            <Descriptions.Item label="信号强度">
              {device.state.wifi.rsq}
            </Descriptions.Item>
          </>
        ) : null}
      </Descriptions>
    </Drawer>
  );
};

export default DeviceDetail;
