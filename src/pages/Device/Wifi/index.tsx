import RedirectLink from '@/components/RedirectLink';
import { ApiSuccessEnum, SelectOption } from '@/models/common.interface';
import {
  postMessageFunction,
  spanConfig,
  wifiPimStatusNames,
} from '@/models/common.util';
import {
  fetchDeviceList,
  fetchLink,
  fetchLinkByPetId,
  fetchRestartDevice,
  fetchSendCommand,
  fetchSetLogLevel,
  fetchSwtichLiveType,
  fetchUnlink,
} from '@/models/device/fetch';
import {
  Device,
  DeviceListParam,
  P2pTypeEnum,
  SendCommandParam,
  ServiceStatusEnum,
  WifiSearchTypeEnum,
} from '@/models/device/interface';
import { p2pTypeNameObj, serviceStatusNameObj } from '@/models/device/util';
import global from '@/utils/global';
import {
  Pagination,
  initPagination,
  initPaginatorParam,
} from '@/utils/request';
import useUrlState from '@ahooksjs/use-url-state';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProTable,
} from '@ant-design/pro-components';
import { useParams } from '@umijs/max';
import {
  Button,
  FormInstance,
  Input,
  Modal,
  Select,
  Space,
  message,
} from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useRef, useState } from 'react';
import DeviceDetail from '../Detail';
import DeviceTableRowAction, {
  Props as DeviceTableRowActionProps,
} from '../components/DeviceTableRowAction';
import { TableRowActionEnum } from '../components/DeviceTableRowAction/interface';
import { DeviceSearchForm } from '../interface';
import { initWifiDeviceSearchForm } from '../util';

const Wifi: React.FC = () => {
  const urlRestParam = useParams<{ device: string }>();
  const [urlParam] = useUrlState<DeviceSearchForm>();
  // const statusOptions = ['离线', '在线'];
  const [pagination, setPagination] = useState<Pagination>(initPagination);
  const [deviceInfo, setDeviceInfo] = useState<Device>();
  const [showDeviceInfo, setShowDeviceInfo] = useState(false);
  const [dataList, setDataList] = useState<Device[]>([]);
  const [listParam, setListParam] = useState<DeviceListParam>();
  const searchTypeList: Array<SelectOption<WifiSearchTypeEnum>> = [
    {
      label: '设备id',
      value: WifiSearchTypeEnum.ID,
    },
    {
      label: 'Mac',
      value: WifiSearchTypeEnum.MAC,
    },
    {
      label: 'Sn',
      value: WifiSearchTypeEnum.SN,
    },
  ];
  const formRef = useRef<FormInstance<DeviceSearchForm>>();

  const requestBindDeviceByPetId = async (id: number, petId: string) => {
    try {
      await fetchLinkByPetId(id, petId, urlRestParam.device || '');
      message.success('设置成功！');
      if (listParam) setListParam({ ...listParam });
    } catch (error) {
      console.log(error);
    }
  };

  // 绑定设备
  const requestBindDevice = async (id: number, groupId: number) => {
    try {
      await fetchLink({ id, groupId }, urlRestParam.device || '');
      message.success('设置成功！');
      if (listParam) setListParam({ ...listParam });
    } catch (error) {
      console.log(error);
    }
  };

  // 解绑设备
  const requestUnbindDevice = async (id: number) => {
    try {
      await fetchUnlink(id, urlRestParam.device || '');
      message.success('解绑成功！');
      if (listParam) setListParam({ ...listParam });
    } catch (error) {
      console.log(error);
    }
  };

  // 获取设备列表数据
  const requestDeviceList = async (param: DeviceListParam) => {
    const { items, ...rest } = await fetchDeviceList(
      param,
      urlRestParam.device || '',
    );
    setPagination(rest);
    setDataList(items);
  };

  // 重启设备
  const requestRestart = async (id: number) => {
    await fetchRestartDevice(id, urlRestParam.device || '');
    message.success('重启成功!');
    if (listParam) setListParam({ ...listParam });
  };

  // 设置log等级
  const requestSetLogLevel = async (id: number, level: string) => {
    await fetchSetLogLevel({ id, level }, urlRestParam.device || '');
    message.success('设置成功!');
    if (listParam) setListParam({ ...listParam });
  };

  // 切换直播方案
  const requestSwitchLiveType = async (record: Device) => {
    if (!urlRestParam.device) return;
    const result = await fetchSwtichLiveType(urlRestParam.device, record.id);
    if (result !== ApiSuccessEnum.success) return;
    message.success('切换声网成功！');
    if (listParam) setListParam({ ...listParam });
  };

  // 下发指令
  const requestSendCommand = async (record: Device, command: string) => {
    const param: SendCommandParam = {
      command,
      deviceId: record.id,
    };
    // console.log(param);
    const result = await fetchSendCommand(urlRestParam.device || '', param);
    console.log(result);
    if (result) {
      message.success('指令下发成功！');
    } else {
      message.error('指令下发失败！');
    }
  };

  const bindDevice = (record: Device) => {
    if (
      global.bindDeviceByPetId.includes(
        (urlRestParam.device || '').toLowerCase(),
      )
    ) {
      const petId = prompt('请输入宠物ID', '');
      if (petId)
        Modal.confirm({
          title: '绑定设备',
          content: '确定绑定此宠物到此设备？',
          onOk: () => requestBindDeviceByPetId(record.id, petId),
        });
      return;
    }
    const groupId = prompt('请输入家庭组ID', '');
    if (groupId)
      Modal.confirm({
        title: '绑定设备',
        content: '确定绑定此家庭组到此设备？',
        onOk: () => requestBindDevice(record.id, +groupId),
      });
  };

  // 当前设备详情被关闭时触发
  const onDeviceDetailClose = () => {
    setDeviceInfo(undefined);
    setShowDeviceInfo(false);
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          const formData: DeviceSearchForm = form?.getFieldsValue();
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: formData.s
                ? `/${urlRestParam.device}/devices?type=${formData.type}&s=${formData.s}`
                : `/${urlRestParam.device}/devices`,
            },
          });
        }}
      >
        搜索
      </Button>,
      // <Button
      //   key="reset"
      //   type="default"
      //   onClick={() => {
      //     form?.resetFields();
      //     setListParam(initDryboxListParam);
      //   }}
      // >
      //   重置
      // </Button>,
    ];
  };

  const columns: Array<ProColumns<Device>> = [
    {
      title: 'id',
      dataIndex: 'id',
      width: 100,
      search: false,
    },
    {
      title: 'MAC',
      dataIndex: 'mac',
      width: 150,
      search: false,
    },
    {
      title: 'SN',
      dataIndex: 'sn',
      width: 150,
      search: false,
    },
    {
      title: '注册时间',
      dataIndex: 'createdAt',
      width: 180,
      search: false,
      render: (_, row) => dayjs(row.createdAt).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '硬/固件',
      dataIndex: 'hardware',
      width: 80,
      search: false,
      render: (_, row) => `${row.hardware}/${row.firmware}`,
    },
    {
      title: '时区',
      dataIndex: 'timezone',
      width: 80,
      search: false,
    },
    {
      title: '区域',
      dataIndex: 'locale',
      width: 150,
      hideInTable:
        (urlRestParam.device || '').toLowerCase() === 'cozy' ||
        (urlRestParam.device || '').toLowerCase() === 'h3',
      search: false,
    },
    {
      title: '主人ID',
      dataIndex: 'userId',
      width: 100,
      search: false,
      render: (_, row) => {
        if (row.relation) {
          return (
            <RedirectLink
              text={row.relation.userId}
              key={row.relation.userId}
              linkUrl="/user/users"
              params={{ username: row.relation.userId }}
            />
          );
        } else if (row.owner) {
          return (
            <RedirectLink
              text={row.owner.userId}
              key={row.owner.userId}
              linkUrl="/user/users"
              params={{ username: row.owner.userId }}
            />
          );
        } else return '-';
      },
    },
    {
      title: '宠物ID',
      dataIndex: 'petIds',
      width: 80,
      search: false,
      render: (_, row) => {
        if (row.relation) {
          return row.relation.petIds && row.relation.petIds.length
            ? row.relation.petIds.map((item) => (
                <RedirectLink
                  text={item}
                  key={item}
                  linkUrl="/pet/pets"
                  params={{ id: item }}
                />
              ))
            : '-';
        } else if (row.owner) {
          return row.owner.petId ? (
            <RedirectLink
              text={row.owner.petId}
              key={row.owner.petId}
              linkUrl="/pet/pets"
              params={{ id: row.owner.petId }}
            />
          ) : (
            '-'
          );
        } else return '-';
      },
    },
    {
      title: '所属家庭',
      dataIndex: 'familyId',
      search: false,
      width: 100,
      render: (_, row) => (
        <RedirectLink
          text={row.familyId}
          linkUrl="/user/family"
          params={{ groupId: row.familyId }}
        />
      ),
    },
    {
      title:
        urlRestParam.device?.toLocaleLowerCase().includes('h3') ||
        urlRestParam.device?.toLocaleLowerCase().includes('d4sh') ||
        urlRestParam.device?.toLocaleLowerCase().includes('d4h') ||
        urlRestParam.device?.toLocaleLowerCase().includes('t5') ||
        urlRestParam.device?.toLocaleLowerCase().includes('t6') ||
        urlRestParam.device?.toLocaleLowerCase().includes('t7')
          ? '设备状态'
          : '状态',
      dataIndex: 'pim',
      width: 80,
      search: false,
      render: (_, row) =>
        row.state.pim ? `${wifiPimStatusNames[row.state.pim]}` : '未知',
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      width: 80,
      search: false,
      render: (_, row) => {
        const deviceTableRowActionProps: DeviceTableRowActionProps = {
          deviceName: urlRestParam.device || '',
          device: row,
        };
        deviceTableRowActionProps.actionTypeList = [
          TableRowActionEnum.RESTART,
          TableRowActionEnum.SET_LOG_LEVEL,
          TableRowActionEnum.DETAIL,
          TableRowActionEnum.SHOW_PRIVATE_KEY,
          TableRowActionEnum.BIND_OR_UNBIND,
          TableRowActionEnum.BIND_HISTORY,
          TableRowActionEnum.LOG_WITH_FILE,
        ];
        if (
          (urlRestParam.device?.toLowerCase() === 'd4sh' ||
            urlRestParam.device?.toLowerCase() === 'd4h') &&
          row.p2pType === P2pTypeEnum.TENCENT_LIVE
        ) {
          deviceTableRowActionProps.actionTypeList.push(
            TableRowActionEnum.SWITCH_LIVE_TYPE,
          );
        }
        // 下发指令
        if (
          urlRestParam.device?.toLowerCase() === 'd4sh' ||
          urlRestParam.device?.toLowerCase() === 'd4h' ||
          urlRestParam.device?.toLowerCase() === 't5' ||
          urlRestParam.device?.toLowerCase() === 't6' ||
          urlRestParam.device?.toLowerCase() === 't7'
        ) {
          deviceTableRowActionProps.actionTypeList.push(
            TableRowActionEnum.SEND_COMMAND,
          );
        }
        deviceTableRowActionProps.restartCb = () => {
          Modal.confirm({
            title: '重启设备',
            content: '确定要重启设备么？',
            onOk: () => requestRestart(row.id),
          });
        };
        deviceTableRowActionProps.logLevelCb = () => {
          const level = prompt('设置Log等级', '');
          if (level) requestSetLogLevel(row.id, level);
        };
        deviceTableRowActionProps.detailCb = () => {
          setShowDeviceInfo(true);
          setDeviceInfo(row);
        };
        deviceTableRowActionProps.showPrivateKeyCb = () => {
          Modal.info({
            title: '密钥',
            content: row.secret,
            cancelButtonProps: { hidden: true },
          });
        };
        deviceTableRowActionProps.bindOrUnbindCb = () => {
          if (
            global.bindDeviceByPetId.includes(
              (urlRestParam.device || '').toLowerCase(),
            ) &&
            ((row.relation &&
              row.relation.petIds &&
              row.relation.petIds.length) ||
              (row.owner && row.owner.petId))
          ) {
            // 存在宠物
            Modal.confirm({
              title: '解绑设备',
              content: '确定要解绑设备么？',
              onOk: () => {
                requestUnbindDevice(row.id);
              },
            });
          } else if (row.relation && row.relation.userId) {
            // 存在用户，则为解绑用户
            Modal.confirm({
              title: '解绑设备',
              content: '确定要解绑设备么？',
              onOk: () => {
                requestUnbindDevice(row.id);
              },
            });
          } else {
            // 不存在用户，则要绑定用户or宠物
            bindDevice(row);
          }
        };
        deviceTableRowActionProps.logCb = () => {
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: `/new-dev/logs-with-file?deviceType=${urlRestParam.device}&deviceId=${row.id}`,
            },
          });
        };
        deviceTableRowActionProps.switchLiveType = () => {
          requestSwitchLiveType(row);
        };
        deviceTableRowActionProps.sendCommandCb = () => {
          const command = prompt('请输入指令', '');
          if (command) requestSendCommand(row, command);
        };
        return <DeviceTableRowAction {...deviceTableRowActionProps} />;
      },
    },
    // 顶部自定义搜索功能
    {
      title: '',
      dataIndex: 'type',
      hideInTable: true,
      renderFormItem: (item, { defaultRender, ...rest }) => (
        <Select {...rest} options={searchTypeList} />
      ),
    },
    {
      title: '',
      dataIndex: 's',
      hideInTable: true,
      renderFormItem: (item, { defaultRender, ...rest }) => (
        <Input {...rest} allowClear />
      ),
    },
  ];

  const getColumns = () => {
    let index = -1;
    const _columns = [...columns];

    switch (urlRestParam.device?.toLowerCase()) {
      case 't6':
        index = _columns.findIndex((item) => item.title === '注册时间');
        _columns.splice(index, 0, {
          title: '垃圾袋盒',
          dataIndex: 'packageSn',
          width: 160,
          search: false,
          render: (_, record) => {
            return record.state.packageSn ? (
              <RedirectLink
                text={record.state.packageSn}
                linkUrl="/t6/rubbish_box_list"
                // params={{ packageSn: record.state.packageSn }}
                params={{ s: record.id, type: WifiSearchTypeEnum.ID }}
              />
            ) : (
              '-'
            );
          },
        });
        break;
      case 't7':
        index = _columns.findIndex((item) => item.title === '注册时间');
        _columns.splice(index, 0, {
          title: '水晶猫砂盘',
          dataIndex: 'sandTraySn',
          width: 160,
          search: false,
          render: (_, record) => {
            return record.state.sandTraySn ? (
              <RedirectLink
                text={record.state.sandTraySn}
                linkUrl="/t7/crystal_tray_cat_litter"
                // params={{ sandTraySn: record.state.sandTraySn }}
                params={{ s: record.id, type: WifiSearchTypeEnum.ID }}
              />
            ) : (
              '-'
            );
          },
        });
        break;
      default:
        break;
    }

    index = -1;
    switch (urlRestParam.device?.toLowerCase()) {
      case 'h3':
      case 'd4sh':
      case 'd4h':
      case 't6':
      case 't5':
        index = _columns.findIndex((item) => item.title === '设备状态');
      case 't7':
        index = _columns.findIndex((item) => item.title === '宠物ID');
        // console.log(index)
        _columns.splice(index, 0, {
          title: '腾讯/声网',
          dataIndex: 'p2pType',
          width: 80,
          search: false,
          render: (_, record) => {
            return record.p2pType ? p2pTypeNameObj[record.p2pType] : '-';
          },
        });
        _columns.splice(index + 1, 0, {
          title: '设备服务状态',
          dataIndex: 'serviceStatus',
          search: false,
          width: 150,
          render: (_, row) => (
            <Space>
              <span>
                {row.serviceStatus !== undefined
                  ? serviceStatusNameObj[row.serviceStatus]
                  : '未知'}
              </span>
              {row.serviceStatus !== ServiceStatusEnum.NOT_AVAILABLE &&
              urlRestParam.device &&
              urlRestParam.device.toLowerCase() !== 'd4sh' &&
              urlRestParam.device.toLowerCase() !== 'd4h' &&
              urlRestParam.device.toLowerCase() !== 't5' &&
              urlRestParam.device.toLowerCase() !== 't6' &&
              urlRestParam.device.toLowerCase() !== 't7' ? (
                <RedirectLink
                  text="查看详情"
                  linkUrl="/business/cloudService"
                  params={{
                    deviceType: `${urlRestParam.device.toLowerCase()}`,
                    deviceId: row.id,
                  }}
                />
              ) : (
                ''
              )}
              {row.serviceStatus !== ServiceStatusEnum.NOT_AVAILABLE &&
              urlRestParam.device &&
              (urlRestParam.device.toLowerCase() === 'd4sh' ||
                urlRestParam.device.toLowerCase() === 'd4h' ||
                urlRestParam.device.toLowerCase() === 't5' ||
                urlRestParam.device.toLowerCase() === 't6' ||
                urlRestParam.device.toLowerCase() === 't7') ? (
                <RedirectLink
                  text="查看详情"
                  linkUrl="/business/cloudServiceV2"
                  params={{
                    deviceType: `${urlRestParam.device.toLowerCase()}`,
                    sn: row.sn,
                  }}
                />
              ) : (
                ''
              )}
            </Space>
          ),
        });
        break;
      default:
        break;
    }

    return _columns;
  };

  useEffect(() => {
    if (!formRef) return;

    const form = formRef.current;
    if (urlParam && urlParam.s) {
      form?.setFieldsValue(urlParam);
      setListParam({ ...initPaginatorParam, ...urlParam });
      return;
    }
    form?.setFieldsValue(initWifiDeviceSearchForm);
  }, [formRef, urlParam]);

  useEffect(() => {
    if (listParam) {
      requestDeviceList(listParam);
    }
  }, [listParam]);

  return (
    <>
      <ProTable<Device>
        dataSource={dataList}
        columns={getColumns()}
        formRef={formRef}
        // formRef={}
        defaultSize="small"
        rowKey="id"
        search={{
          defaultCollapsed: false,
          span: spanConfig,
          optionRender: searchOptionRender,
        }}
        scroll={{
          x: columns
            .filter((col) => col.dataIndex === 'action')
            .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
        }}
        pagination={{
          pageSize: pagination.limit,
          total: pagination.total,
          showQuickJumper: true,
        }}
      />
      {deviceInfo ? (
        <DeviceDetail
          device={deviceInfo}
          visible={showDeviceInfo}
          onClose={onDeviceDetailClose}
        />
      ) : (
        ''
      )}
    </>
  );
};

export default Wifi;
