import {
  fetchDeviceModuleDeletion,
  fetchDeviceModuleList,
} from '@/models/device/fetch';
import { DeviceModule, DeviceModuleListParam } from '@/models/device/interface';
import { initDeviceModuleListParam } from '@/models/device/util';
import { initPaginatorParam } from '@/utils/request';
import useUrlState from '@ahooksjs/use-url-state';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { useParams } from '@umijs/max';
import { Button, Modal, message } from 'antd';
import React, { useEffect, useState } from 'react';
import DeviceTableRowAction from '../components/DeviceTableRowAction';
import { TableRowActionEnum } from '../components/DeviceTableRowAction/interface';
import { hasDependentHardwareDeviceList } from '../util';
import Edit from './Edit';

const Module: React.FC = () => {
  const urlRestParam = useParams<{ device: string }>();
  const [urlParam] = useUrlState<{ hardware: string }>();
  const [showEdit, setShowEdit] = useState(false);
  const [dataList, setDataList] = useState<DeviceModule[]>([]);
  const [selectedDeviceModule, setSelectedDeviceModule] =
    useState<DeviceModule>();
  const [listParam, setListParam] = useState<DeviceModuleListParam>({
    ...initDeviceModuleListParam,
    hardware: urlParam.hardware,
  });

  const columns: Array<ProColumns<DeviceModule>> = [
    {
      title: '模块',
      dataIndex: 'id',
      search: false,
    },
    {
      title: '排序',
      dataIndex: 'priority',
      search: false,
    },
    {
      title: '最小版本',
      dataIndex: 'minVersion',
      search: false,
    },
    {
      title: 'Log等级',
      dataIndex: 'logLevel',
      search: false,
    },
    {
      title: '升级时间(秒)',
      dataIndex: 'upgradeTime',
      search: false,
    },
    {
      title: '操作',
      dataIndex: 'action',
      search: false,
      render: (_, row) => (
        <DeviceTableRowAction
          deviceName={urlRestParam.device || ''}
          actionTypeList={[
            TableRowActionEnum.UPDATE,
            TableRowActionEnum.DELETE,
          ]}
          updateCb={() => {
            setSelectedDeviceModule(row);
            setShowEdit(true);
          }}
          deleteCb={() =>
            Modal.confirm({
              title: '删除模块',
              content: '确定要删除该模块么？',
              onOk: () => {
                requestDeviceModuleDeletion(row.id, +urlParam.hardware);
              },
            })
          }
        />
      ),
    },
  ];

  useEffect(() => {
    if (!listParam) return;
    requestDeviceModuleList(listParam);
  }, [listParam]);

  // 获取设备模块列表数据
  const requestDeviceModuleList = async (param: DeviceModuleListParam) => {
    if (!urlRestParam.device) return;
    try {
      const items = await fetchDeviceModuleList(param, urlRestParam.device);
      setDataList(items);
    } catch (error) {
      console.log(error);
    }
  };

  // 删除模块
  const requestDeviceModuleDeletion = async (id: string, hardware: number) => {
    if (!urlRestParam.device) return;
    try {
      await fetchDeviceModuleDeletion(id, hardware, urlRestParam.device);
      message.success('删除成功！');
      const index = dataList.findIndex((item) => item.id === id);
      dataList.splice(index, 1);
      setDataList([...dataList]);
    } catch (error) {
      console.log(error);
    }
  };

  // 当Edit被成功保存时的回调
  const onEditOk = () => {
    setListParam({ ...listParam, hardware: urlParam.hardware });
    onEditClosed();
  };

  // 关闭Edit弹框
  const onEditClosed = () => {
    setShowEdit(false);
    setSelectedDeviceModule(undefined);
  };

  return (
    <>
      <ProTable<DeviceModule>
        headerTitle={
          hasDependentHardwareDeviceList.includes(urlRestParam.device || '')
            ? `当前硬件版本：${urlParam.hardware}`
            : '设备列表'
        }
        dataSource={dataList}
        columns={columns}
        defaultSize="small"
        rowKey="id"
        scroll={{
          x: columns
            .filter((col) => col.dataIndex === 'action')
            .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
        }}
        search={false}
        pagination={{
          pageSize: initPaginatorParam.limit,
          showQuickJumper: true,
        }}
        toolbar={{
          actions: [
            <Button
              type="primary"
              danger
              onClick={() => {
                setSelectedDeviceModule(undefined);
                setShowEdit(true);
              }}
            >
              创建
            </Button>,
          ],
        }}
      />
      {urlRestParam.device ? (
        <Edit
          visible={showEdit}
          deviceType={urlRestParam.device}
          deviceModule={selectedDeviceModule}
          onOk={onEditOk}
          onClose={onEditClosed}
        />
      ) : null}
    </>
  );
};

export default Module;
