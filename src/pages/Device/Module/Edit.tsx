import { fetchDeviceModuleEdition } from '@/models/device/fetch';
import { DeviceModule, DeviceModuleParam } from '@/models/device/interface';
import useUrlState from '@ahooksjs/use-url-state';
import { Form, Input, InputNumber, Modal, message } from 'antd';
import React, { useEffect, useState } from 'react';
import { DeviceModuleForm } from '../interface';
import {
  initDeviceModuleForm,
  transferDeviceModuleFromToParam,
  transferDeviceModuleToFormData,
} from '../util';

interface Props {
  visible: boolean;
  deviceModule?: DeviceModule;
  deviceType: string;
  onOk?: () => void;
  onClose?: () => void;
}

const Edit: React.FC<Props> = ({
  visible,
  deviceType,
  deviceModule,
  onOk,
  onClose,
}: Props) => {
  const layout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 20 },
  };
  const [urlParam] = useUrlState<{ hardware: string }>();
  const [form] = Form.useForm<DeviceModuleForm>();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const formData = transferDeviceModuleToFormData(deviceModule);
    console.log(formData);
    form.setFieldsValue(formData);
  }, [deviceModule, form]);

  // 创建/修改模块
  const requestDeviceModuleEdition = async () => {
    try {
      setLoading(true);
      const param: DeviceModuleParam = transferDeviceModuleFromToParam(
        form.getFieldsValue(),
        urlParam.hardware,
      );
      await fetchDeviceModuleEdition(param, deviceType);
      message.success(`${deviceModule ? '修改' : '创建'}成功！`);
      form.resetFields();
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const submit = async () => {
    try {
      await requestDeviceModuleEdition();
      onOk && onOk();
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      width={800}
      title="创建序列号"
      open={visible}
      okButtonProps={{
        disabled: loading,
        loading,
      }}
      onCancel={() => {
        form.resetFields();
        onClose && onClose();
      }}
      onOk={submit}
    >
      <Form {...layout} form={form} initialValues={initDeviceModuleForm}>
        <Form.Item name="module" label="模块">
          <Input placeholder="模块" />
        </Form.Item>
        <Form.Item name="sort" label="排序">
          <InputNumber placeholder="排序" />
        </Form.Item>
        <Form.Item name="minVersion" label="最小版本">
          <InputNumber placeholder="最小版本" />
        </Form.Item>
        <Form.Item name="logLevel" label="Log等级">
          <InputNumber placeholder="Log等级" />
        </Form.Item>
        <Form.Item name="updateTime" label="升级时间(秒)">
          <InputNumber placeholder="升级时间(秒)" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default Edit;
