import { BooleanEnum } from '@/models/common.enum';
import { ApiSuccessEnum } from '@/models/common.interface';
import { postMessageFunction, spanConfig } from '@/models/common.util';
import { fetchSandTrayUnlock } from '@/models/device/fetch';
import { SandTrayRegionEnum } from '@/models/device/interface';
import { lockedNameMap, sandTrayRegionNameMap } from '@/models/device/util';
import { fetchSandTraySnList } from '@/models/sandTray/fetch';
import { SandTraySn, SandTraySnListParam } from '@/models/sandTray/interface';
import { initSandTraySnListParam } from '@/models/sandTray/util';
import { Pagination, initPagination } from '@/utils/request';
import useUrlState from '@ahooksjs/use-url-state';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProTable,
} from '@ant-design/pro-components';
import { useParams } from '@umijs/max';
import { Button, FormInstance, message } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import {
  CrystalTrayCatLitterNFCTableForm,
  CrystalTrayCatLitterNFCUrlParam,
} from '../interface';

const List: React.FC = () => {
  const urlRestParam = useParams<{ device: string }>();
  const [urlParam] = useUrlState<CrystalTrayCatLitterNFCUrlParam>({});
  const formRef = useRef<FormInstance<CrystalTrayCatLitterNFCTableForm>>();
  const [dataList, setDataList] = useState<SandTraySn[]>([]);
  const [listParam, setListParam] = useState<SandTraySnListParam>();
  const [paginator, setPaginator] = useState<Pagination>(initPagination);

  // 获取列表数据
  const requestCrystalTrayCatLitterNFCList = async (
    param: SandTraySnListParam,
  ) => {
    if (!urlRestParam.device) return;
    const { items, ...rest } = await fetchSandTraySnList(
      param,
      urlRestParam.device,
    );
    setPaginator(rest);
    setDataList(items);
  };

  // 解锁猫砂盘
  const handleUnlock = async (record: SandTraySn) => {
    if (!urlRestParam.device) return;

    try {
      const result = await fetchSandTrayUnlock(
        {
          sn: record.sandTraySn,
          secret: record.sandTraySecret,
        },
        urlRestParam.device,
      );

      if (result === ApiSuccessEnum.success) {
        message.success('解锁成功！');
        // 刷新列表数据
        // if (listParam) {
        //   requestCrystalTrayCatLitterNFCList(listParam);
        // }
        setListParam({ ...(listParam || initSandTraySnListParam) });
      }
    } catch (error) {
      console.error('解锁失败:', error);
      message.error('解锁失败，请重试');
    }
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          const formData: CrystalTrayCatLitterNFCTableForm =
            form?.getFieldsValue();
          // console.log(formData);
          const param: CrystalTrayCatLitterNFCUrlParam = {};
          if (formData.sandTraySn) {
            param.sn = formData.sandTraySn;
          }
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: `/t7/crystal_tray_cat_litter_nfc_account_list`,
              param: { ...param },
            },
          });
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          form?.resetFields();
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: `/t7/crystal_tray_cat_litter_nfc_account_list`,
            },
          });
        }}
      >
        重置
      </Button>,
    ];
  };

  const onPaginationChanged = (page: number, pageSize: number) => {
    let index = page;
    if (!listParam || pageSize !== listParam.limit) {
      index = 1;
    }
    setListParam({
      ...listParam,
      limit: pageSize,
      offset: (index - 1) * pageSize,
    });
  };

  const columns: Array<ProColumns<SandTraySn>> = [
    {
      title: '水晶猫砂盘SN',
      dataIndex: 'sandTraySn',
      width: 120,
      formItemProps: {
        label: '水晶猫砂盘SN',
        labelCol: {
          span: 8,
        },
      },
    },
    {
      title: '水晶猫砂盘secret',
      dataIndex: 'sandTraySecret',
      width: 120,
      search: false,
    },
    {
      title: '所属区域',
      dataIndex: 'region',
      width: 120,
      search: false,
      render: (_, record) => {
        return sandTrayRegionNameMap[record.region as SandTrayRegionEnum];
      },
    },
    {
      title: '解锁状态',
      dataIndex: 'lock',
      width: 120,
      search: false,
      render: (_, record) => {
        return lockedNameMap[record.lock as BooleanEnum];
      },
    },
    { title: '注册批次', dataIndex: 'batchNo', width: 120, search: false },
    {
      title: '操作',
      key: 'action',
      width: 100,
      fixed: 'right',
      search: false,
      render: (_, record) => {
        return (
          <Button
            type="link"
            disabled={record.lock === BooleanEnum.FALSE}
            onClick={() => handleUnlock(record)}
          >
            解锁
          </Button>
        );
      },
    },
  ];

  useEffect(() => {
    if (!listParam) return;
    console.log('useEffect', listParam);
    requestCrystalTrayCatLitterNFCList(listParam);
  }, [listParam]);

  useEffect(() => {
    const tableForm: CrystalTrayCatLitterNFCTableForm = {};
    const _listParam: SandTraySnListParam = { ...initSandTraySnListParam };
    if (urlParam.sn) {
      tableForm.sandTraySn = urlParam.sn;
      _listParam.sn = urlParam.sn;
    }
    if (urlParam.batchNo) {
      _listParam.batchNo = urlParam.batchNo;
    }
    formRef.current?.setFieldsValue(tableForm);

    setListParam(_listParam);
  }, [urlParam]);

  return (
    <ProTable<SandTraySn>
      dataSource={dataList}
      columns={columns}
      formRef={formRef}
      defaultSize="small"
      rowKey="id"
      search={{
        defaultCollapsed: false,
        span: spanConfig,
        optionRender: searchOptionRender,
      }}
      options={{
        reload: () => {
          if (listParam) setListParam({ ...listParam });
        },
      }}
      scroll={{
        x: columns
          .filter((col) => col.dataIndex !== 'action')
          .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
      }}
      pagination={{
        pageSize: paginator.limit,
        total: paginator.total,
        showQuickJumper: true,
        onChange: onPaginationChanged,
      }}
    />
  );
};

export default List;
