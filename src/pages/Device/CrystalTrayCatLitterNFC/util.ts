import {
  CrystalTrayCatLitterNFCDetail,
  CrystalTrayCatLitterNFCParam,
} from '@/models/crystalTrayCatLitterNFC/interface';
import { CrystalTrayCatLitterNFCForm } from './interface';

export const initialCrystalTrayCatLitterNFCForm: CrystalTrayCatLitterNFCForm = {
};

// 将formData转换为param
export const transferFormDataToParam = (formData: CrystalTrayCatLitterNFCForm, id?: number): CrystalTrayCatLitterNFCParam => {
  const param: CrystalTrayCatLitterNFCParam = {
  };
  if (id) {
    param.id = id;
  }
  return param;
};

// 将接口返回的详情数据转换为formData
export const transferDetailToFormData = (detail: CrystalTrayCatLitterNFCDetail): CrystalTrayCatLitterNFCForm => {
  const formData: CrystalTrayCatLitterNFCForm = {
  };
  return formData;
};