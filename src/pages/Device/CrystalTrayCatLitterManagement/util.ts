import {
  SandTrayManagementListParam,
  SandTrayTypeEnum,
  VersionCodeEnum,
} from '@/models/sandTray/interface';
import { initSandTrayManagementListParam } from '@/models/sandTray/util';
import dayjs from 'dayjs';
import {
  CrystalTrayCatLitterForm,
  CrystalTrayCatLitterTableForm,
  CrystalTrayCatLitterUrlParam,
} from './interface';

export const initialCrystalTrayCatLitterForm: CrystalTrayCatLitterForm = {
  number: null,
  orderNo: '',
  type: SandTrayTypeEnum.NORMAL,
  versionCode: VersionCodeEnum.CB,
  // 默认国内
  // region: PackageRegionEnum.CHINA,
};

// 将url中的参数转为表格搜索表单数据
export const transferUrlParamToTableFormData = (
  urlParam: CrystalTrayCatLitterUrlParam,
) => {
  const formData: CrystalTrayCatLitterTableForm = {};
  if (urlParam.batchNo) {
    formData.batchNo = urlParam.batchNo;
  }
  if (urlParam.orderNo) {
    formData.orderNo = urlParam.orderNo;
  }
  if (urlParam.operator) {
    formData.operator = urlParam.operator;
  }
  if (urlParam.startTime && urlParam.endTime) {
    formData.timestamp = [];
    formData.timestamp[0] = dayjs.unix(+urlParam.startTime);
    formData.timestamp[1] = dayjs.unix(+urlParam.endTime);
  }
  return formData;
};

export const transferUrlParamToListParam = (
  urlParam: CrystalTrayCatLitterUrlParam,
): SandTrayManagementListParam => {
  const listParam: SandTrayManagementListParam = {
    ...initSandTrayManagementListParam,
    batchNo: urlParam.batchNo,
    orderNo: urlParam.orderNo,
    operator: urlParam.operator,
  };
  if (urlParam.startTime && urlParam.endTime) {
    listParam.startTime = +urlParam.startTime;
    listParam.endTime = +urlParam.endTime;
  }
  return listParam;
};
