import { SelectInputValue } from '@/components/SelectInputGroup/interface';
import { WifiSearchTypeEnum } from '@/models/device/interface';
import { SandTrayTypeEnum, VersionCodeEnum } from '@/models/sandTray/interface';
import { Dayjs } from 'dayjs';

export interface CrystalTrayCatLitterUrlParam {
  batchNo?: string;
  orderNo?: string;
  operator?: string;
  // 10位时间戳
  startTime?: string;
  // 10位时间戳
  endTime?: string;
}

export interface CrystalTrayCatLitterTableForm {
  batchNo?: string;
  orderNo?: string;
  operator?: string;
  timestamp?: Dayjs[];
}

// 注册时用
export interface CrystalTrayCatLitterForm {
  orderNo: string;
  number: number | null;
  type: SandTrayTypeEnum;
  versionCode: VersionCodeEnum;
  //   region: PackageRegionEnum;
  //   type: PackageTypeEnum;
}

export interface UrlParam {
  s?: string;
  sandTraySn?: string;
  type?: WifiSearchTypeEnum;
}

export interface CrystalTrayCatLitterTableForm {
  selectInputValue?: SelectInputValue;
  sandTraySn?: string;
}

// export interface RubbishBoxForm {}
