import { PackageRegionEnum } from '@/models/package/interface';
import { fetchSandTrayRegister } from '@/models/sandTray/fetch';
import {
  SandTrayManagementParam,
  SandTrayTypeEnum,
  VersionCodeEnum,
  VersionCodeNameMap,
} from '@/models/sandTray/interface';
import { useParams } from '@@/exports';
import { ProCard } from '@ant-design/pro-components';
import { Button, Form, Input, InputNumber, Radio, message } from 'antd';
import React, { useState } from 'react';
import { CrystalTrayCatLitterForm } from '../interface';
import { initialCrystalTrayCatLitterForm } from '../util';

interface Props {
  onSubmitSuccess: () => void;
  onClose: () => void;
}

const versionCodeOptions = [
  { label: VersionCodeNameMap[VersionCodeEnum.CB], value: VersionCodeEnum.CB },
  { label: VersionCodeNameMap[VersionCodeEnum.CA], value: VersionCodeEnum.CA },
];

const Edit: React.FC<Props> = ({ onClose, onSubmitSuccess }: Props) => {
  const urlParam = useParams<{ device: string }>();
  const [form] = Form.useForm<CrystalTrayCatLitterForm>();
  const [loading, setLoading] = useState(false);

  // 提交form表单
  const submit = async (formData: CrystalTrayCatLitterForm) => {
    if (!formData.number) {
      message.warning('请输入大于0的整数!');
      return;
    }
    if (!urlParam.device) {
      message.warning('请提供设备类型');
      return;
    }

    setLoading(true);

    let region = PackageRegionEnum.CHINA;

    if (formData.versionCode === VersionCodeEnum.CA) {
      region = PackageRegionEnum.OVERSEA;
    } else {
      region = PackageRegionEnum.CHINA;
    }
    const _param: SandTrayManagementParam = {
      orderNo: formData.orderNo,
      num: formData.number,
      type: formData.type,
      region,
      // region: formData.region,
      // type: formData.type,
    };
    console.log(_param);
    try {
      await fetchSandTrayRegister(_param, urlParam.device);
      message.success(`注册成功！`);
      onSubmitSuccess();
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <ProCard>
      <Form
        form={form}
        layout="vertical"
        onFinish={submit}
        initialValues={initialCrystalTrayCatLitterForm}
      >
        <Form.Item
          name="orderNo"
          label="金蝶订单号"
          rules={[{ required: true, message: '请输入金蝶订单号' }]}
        >
          <Input placeholder="请输入金蝶订单号" />
        </Form.Item>
        <Form.Item
          name="number"
          label="指定数量"
          rules={[{ required: true, message: '请输入大于0的整数' }]}
        >
          <InputNumber
            controls={false}
            min={1}
            max={99999}
            placeholder="请输入大于0的整数"
          />
        </Form.Item>
        <Form.Item
          name="versionCode"
          label="锁定区域"
          rules={[{ required: true, message: '请选择锁定区域' }]}
        >
          <Radio.Group
            style={{ width: '100%' }}
            options={versionCodeOptions}
            optionType="button"
          ></Radio.Group>
        </Form.Item>
        <Form.Item
          name="type"
          label="猫砂分类"
          rules={[{ required: true, message: '请选择猫砂分类' }]}
        >
          <Radio.Group
            style={{ width: '100%' }}
            options={[
              { label: '普通砂', value: SandTrayTypeEnum.NORMAL },
              {
                label: 'PH检验砂',
                value: SandTrayTypeEnum.PH_CHECK,
              },
            ]}
            optionType="button"
          ></Radio.Group>
        </Form.Item>
        <Form.Item wrapperCol={{ span: 24 }} style={{ textAlign: 'center' }}>
          <Button
            type="primary"
            htmlType="submit"
            style={{ marginRight: 16 }}
            loading={loading}
            disabled={loading}
          >
            提交
          </Button>
          <Button type="default" onClick={() => onClose()}>
            取消
          </Button>
        </Form.Item>
      </Form>
    </ProCard>
  );
};

export default Edit;
