import { Locality } from '@/models/app/interface';
import { SelectOption } from '@/models/common.interface';
import { ConnectState } from '@/models/connect';
import { useDispatch, useSelector } from '@umijs/max';
import { Select, Space } from 'antd';
import React, { useEffect, useState } from 'react';

export interface RegionSelectorValue {
  region: 'CN';
  province: string | null;
  city: string | null;
  district: string | null;
}
export const initRegionSelectorValue: RegionSelectorValue = {
  region: 'CN',
  province: null,
  city: null,
  district: null,
};

interface Props {
  value?: RegionSelectorValue;
  onChange?: (value: RegionSelectorValue) => void;
}

const RegionSelector: React.FC<Props> = ({ value, onChange }: Props) => {
  const dispatch = useDispatch();
  const [regionSelectorValue, setRegionSelectorValue] =
    useState<RegionSelectorValue>(initRegionSelectorValue);

  const localityMap: Record<string, Locality[]> = useSelector(
    ({ app }: ConnectState) => app.localityMap,
  );

  const transformLocalityMapObjectToSelectOptions = (list: Locality[] = []) => {
    const options: SelectOption[] = list.map((item) => ({
      label: item.name,
      value: item.code,
    }));
    return options;
  };

  const triggerChange = (val: RegionSelectorValue) => {
    onChange?.(val);
  };

  useEffect(() => {
    dispatch({ type: 'app/requestLocalities', payload: { parent: 'CN' } });
  }, [dispatch]);

  // useEffect(() => {
  //   console.log(
  //     localityMap,
  //     regionSelectorValue.region,
  //     localityMap[regionSelectorValue.region],
  //   );
  // }, [localityMap, regionSelectorValue]);

  useEffect(() => {
    setRegionSelectorValue({ ...(value || initRegionSelectorValue) });

    if (
      value &&
      value.province &&
      (!localityMap[value.province] || localityMap[value.province].length === 0)
    ) {
      dispatch({
        type: 'app/requestLocalities',
        payload: { parent: value.province },
      });
    }

    if (
      value &&
      value.city &&
      (!localityMap[value.city] || localityMap[value.city].length === 0)
    ) {
      dispatch({
        type: 'app/requestLocalities',
        payload: { parent: value.city },
      });
    }
  }, [value, localityMap]);

  return (
    <Space>
      <Select
        style={{ width: 200 }}
        allowClear
        showSearch
        placeholder="请选择省"
        value={regionSelectorValue.province}
        options={transformLocalityMapObjectToSelectOptions(
          localityMap[regionSelectorValue.region],
        )}
        onChange={(ev) => {
          console.log(ev);
          dispatch({ type: 'app/requestLocalities', payload: { parent: ev } });
          const rsvalue: RegionSelectorValue = {
            ...initRegionSelectorValue,
            province: ev,
          };
          setRegionSelectorValue(rsvalue);
          triggerChange(rsvalue);
        }}
      />
      <Select
        style={{ width: 200 }}
        allowClear
        showSearch
        placeholder="请选择市"
        options={transformLocalityMapObjectToSelectOptions(
          regionSelectorValue.province
            ? localityMap[regionSelectorValue.province]
            : [],
        )}
        value={regionSelectorValue.city}
        onChange={(ev) => {
          dispatch({ type: 'app/requestLocalities', payload: { parent: ev } });
          const rsvalue: RegionSelectorValue = {
            ...initRegionSelectorValue,
            province: regionSelectorValue.province,
            city: ev,
          };
          setRegionSelectorValue(rsvalue);
          triggerChange(rsvalue);
        }}
      />
      <Select
        style={{ width: 200 }}
        allowClear
        showSearch
        placeholder="请选择区"
        options={transformLocalityMapObjectToSelectOptions(
          regionSelectorValue.city ? localityMap[regionSelectorValue.city] : [],
        )}
        value={regionSelectorValue.district}
        onChange={(ev) => {
          dispatch({ type: 'app/requestLocalities', payload: { parent: ev } });
          const rsvalue: RegionSelectorValue = {
            ...initRegionSelectorValue,
            province: regionSelectorValue.province,
            city: regionSelectorValue.city,
            district: ev,
          };
          setRegionSelectorValue(rsvalue);
          triggerChange(rsvalue);
        }}
      />
    </Space>
  );
};

export default RegionSelector;
