import Uploader from '@/components/Uploader';
import { ApiSuccessEnum, SelectOption } from '@/models/common.interface';
import { postMessageFunction } from '@/models/common.util';
import {
  fetchAppBannerDetail,
  fetchAppBannerSave,
} from '@/models/community/fetch';
import { fetchUserGroupList } from '@/models/tag/fetch';
import { UserGroup, UserGroupListParam } from '@/models/tag/interface';
import { initUserGroupListParam } from '@/models/tag/util';
import { UploadTokenTypeEnum } from '@/services/qiniuOss/interface';
import normFile from '@/utils/normFile';
import { CloseCircleOutlined, PlusOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { history, useParams } from '@umijs/max';
import {
  Alert,
  Button,
  Col,
  DatePicker,
  Form,
  Input,
  Radio,
  Row,
  Select,
  Space,
  Switch,
  Typography,
  message,
} from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useMemo, useState } from 'react';
import { AppBannerForm, UserGroupTypeEnum } from '../interface';
import {
  initialAppBannerForm,
  transferDetailToFormData,
  transferFormDataToParam,
  userGroupTypeOptions,
} from '../util';
import RegionSelector from './RegionSelector';

const Edit: React.FC = () => {
  const param = useParams<{ id: string }>();
  const [form] = Form.useForm<AppBannerForm>();
  const [loading, setLoading] = useState(false);
  const [userGroupList, setUserGroupList] = useState<UserGroup[]>([]);
  const [userGroupOptions, setUserGroupOptions] = useState<SelectOption[]>([]);
  const layout = {
    labelCol: { span: 3 },
    wrapperCol: { span: 20 },
  };

  const userGroupType = Form.useWatch('userGroupType', form);
  const userGroupIds = Form.useWatch('userGroupIds', form);

  const estimateNumber = useMemo(() => {
    const selectedUserGroupList = (userGroupList || []).filter((userGroup) =>
      (userGroupIds || []).includes(userGroup.id),
    );
    if (!selectedUserGroupList || !selectedUserGroupList.length) {
      return 0;
    }
    return selectedUserGroupList.reduce(
      (prev, curr) => prev + curr.estimate,
      0,
    );
  }, [userGroupList, userGroupIds]);

  // 根据id获取详情数据
  const requestDetailById = async (id: number) => {
    const detail = await fetchAppBannerDetail(id);
    // console.log(detail);
    const formData = transferDetailToFormData(detail);
    form.setFieldsValue(formData);
  };

  const addNewUserGroup = () => {
    postMessageFunction({
      type: 'redirect',
      content: {
        redirectUrl: `/user/group?action=create`,
      },
    });
  };

  // 获取用户分组列表数据
  const requestUserGroupList = async (
    param: UserGroupListParam = { ...initUserGroupListParam, limit: 100000 },
  ) => {
    const { items } = await fetchUserGroupList(param);
    const options: SelectOption[] = items
      .filter((item) => !!item.enable)
      .map((item) => ({
        value: item.id,
        label: item.name,
      }));
    setUserGroupList(items);
    setUserGroupOptions(options);
  };

  // 提交form表单
  const submit = async (formData: AppBannerForm) => {
    setLoading(true);
    const _param = transferFormDataToParam(formData, +(param?.id || 0));
    let result = '';
    let state = '';
    console.log('submit', formData, _param);
    // return;
    try {
      if (param && param.id && +param.id) {
        state = '更新';
      } else {
        state = '创建';
      }
      result = await fetchAppBannerSave(_param);

      if (result === ApiSuccessEnum.success) {
        message.success(`${state}成功！`);
        history.back();
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    requestUserGroupList();
  }, []);

  useEffect(() => {
    if (param && param.id && +param.id) {
      requestDetailById(+param.id);
    }
  }, [param]);

  return (
    <ProCard>
      <Form
        {...layout}
        form={form}
        onFinish={submit}
        initialValues={initialAppBannerForm}
      >
        <Form.Item
          name="title"
          label="标题（用户不可见）"
          rules={[{ required: true, message: '请输入标题' }]}
        >
          <Input placeholder="请输入标题" />
        </Form.Item>
        <Form.Item name="enabled" label="是否启用" valuePropName="checked">
          <Switch checkedChildren="是" unCheckedChildren="否" />
        </Form.Item>
        <Form.Item
          name="userIds"
          label="用户ID"
          extra={
            <Alert
              type="info"
              message="用于内部测试，为空则默认全部用户；输入多个以英文逗号进行分隔。"
            />
          }
        >
          <Input placeholder="请输入用户ID" />
        </Form.Item>
        <Form.Item
          name="expiredTimes"
          label="有效期"
          rules={[{ required: true, message: '请配置有效期' }]}
        >
          <DatePicker.RangePicker
            minDate={dayjs()}
            format="YYYY-MM-DD HH:mm"
            showNow
            showTime={{
              format: 'HH:mm',
              disabledTime: (time) => {
                const currentTime = dayjs();
                const hours = Array.from({ length: 24 }, (_, i) => i);
                const minutes = Array.from({ length: 60 }, (_, i) => i);
                return {
                  disabledHours: () =>
                    hours.filter(
                      (hour) =>
                        time.day() === currentTime.day() &&
                        currentTime.hour() > hour,
                    ),
                  disabledMinutes: (hour: number) =>
                    minutes.filter(
                      (minute) =>
                        time.day() === currentTime.day() &&
                        (currentTime.hour() > hour ||
                          currentTime.minute() > minute),
                    ),
                };
              },
            }}
            placeholder={['开始时间', '结束时间']}
          />
        </Form.Item>
        <Form.Item
          label="图片"
          name="images"
          getValueFromEvent={normFile}
          shouldUpdate
          valuePropName="fileList"
          rules={[{ required: true, message: '请选择配图' }]}
        >
          <Uploader
            type={UploadTokenTypeEnum.IMAGE}
            uploadData={{ namespace: `post` }}
          />
        </Form.Item>
        <Form.Item name="link" label="链接">
          <Input placeholder="请输入链接" />
        </Form.Item>
        <Form.List name="regionValueList">
          {(fields, { add, remove }) => (
            <>
              <Row align="middle" style={{ marginBottom: 8 }}>
                <Col offset={2}>地区选择:</Col>
                <Col>
                  <Button
                    type="link"
                    icon={<PlusOutlined />}
                    onClick={() => add()}
                  >
                    添加地区
                  </Button>
                </Col>
              </Row>
              {fields.map(({ key, name, ...restField }, index) => (
                <Row key={key}>
                  <Col offset={2}>
                    <Space align="center">
                      <Form.Item {...restField} name={[name, 'regionInfo']}>
                        <RegionSelector />
                      </Form.Item>
                      <CloseCircleOutlined
                        style={{
                          marginBottom: 30,
                          marginLeft: 8,
                          color: 'red',
                        }}
                        onClick={() => remove(index)}
                      />
                    </Space>
                  </Col>
                </Row>
              ))}
            </>
          )}
        </Form.List>
        <Form.Item
          name="userGroupType"
          label="目标人群"
          rules={[{ required: true, message: '请选择目标人群' }]}
        >
          <Radio.Group
            options={userGroupTypeOptions}
            optionType="button"
            buttonStyle="solid"
          />
        </Form.Item>
        {userGroupType === UserGroupTypeEnum.SELECTED ? (
          <>
            <Form.Item
              name="userGroupIds"
              label="用户分群"
              rules={[{ required: true, message: '请选择用户分群' }]}
              extra={
                <>
                  没有想要的用户分群？去{' '}
                  <Button type="link" onClick={addNewUserGroup}>
                    新增用户分群
                  </Button>
                </>
              }
            >
              <Select
                mode="multiple"
                placeholder="请选择用户分群"
                options={userGroupOptions}
              />
            </Form.Item>
            <Form.Item label="预估人数">
              <Typography.Text>{estimateNumber}</Typography.Text>
            </Form.Item>
          </>
        ) : null}
        <Form.Item wrapperCol={{ span: 24 }} style={{ textAlign: 'center' }}>
          <Button
            type="primary"
            htmlType="submit"
            style={{ marginRight: 16 }}
            loading={loading}
            disabled={loading}
          >
            提交
          </Button>
          <Button type="default" onClick={history.back}>
            取消
          </Button>
        </Form.Item>
      </Form>
    </ProCard>
  );
};

export default Edit;
