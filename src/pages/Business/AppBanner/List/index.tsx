import { ApiSuccessEnum } from '@/models/common.interface';
import { postMessageFunction, spanConfig } from '@/models/common.util';
import {
  fetchAppBannerDeletion,
  fetchAppBannerList,
} from '@/models/community/fetch';
import {
  AppBanner,
  AppBannerListParam,
  AppBannerTypeEnum,
} from '@/models/community/interface';
import { initAppBannerListParam } from '@/models/community/util';
import { Pagination, initPagination } from '@/utils/request';
import useUrlState from '@ahooksjs/use-url-state';
import { PlusOutlined } from '@ant-design/icons';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { Button, Image, Popconfirm, Space, message } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useRef, useState } from 'react';
import { AppBannerTableForm, UrlParam } from '../interface';

const IMAGE_WIDTH = 130;

const List: React.FC = () => {
  const [urlParam] = useUrlState<{ type: AppBannerTypeEnum }>();
  const formRef = useRef<ProFormInstance<AppBannerTableForm>>();
  const [dataList, setDataList] = useState<AppBanner[]>([]);
  const [listParam, setListParam] = useState<AppBannerListParam>();
  const [paginator, setPaginator] = useState<Pagination>(initPagination);

  // 获取列表数据
  const requestAppBannerList = async (
    param: AppBannerListParam = initAppBannerListParam,
  ) => {
    const { items, ...rest } = await fetchAppBannerList(param);
    setPaginator(rest);
    setDataList(items);
  };

  // 编辑
  const editAppBanner = (id: number) => {
    history.push(`/business/app-banner/edit/${id}`);
  };

  const deleteAppBanner = async (id: number) => {
    try {
      const result = await fetchAppBannerDeletion(id);
      if (result === ApiSuccessEnum.success) {
        message.success('删除成功');
        setListParam({ ...(listParam || initAppBannerListParam) });
      }
    } catch (error) {
      console.log(error);
    }
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          const formData: AppBannerTableForm = form?.getFieldsValue();
          const param: UrlParam = {};
          if (formData.status) {
            param.type = formData.status;
          }
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: `/community/appbanner_list`,
              param: param as Record<string, any>,
            },
          });
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          form?.resetFields();
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: `/community/appbanner_list`,
            },
          });
        }}
      >
        重置
      </Button>,
    ];
  };

  const onPaginationChanged = (page: number, pageSize: number) => {
    let index = page;
    if (!listParam || pageSize !== listParam.limit) {
      index = 1;
    }
    const _param: AppBannerListParam = {
      ...initAppBannerListParam,
      limit: pageSize,
      offset: (index - 1) * pageSize,
    };
    if (listParam && listParam.type) {
      _param.type = listParam.type;
    }
    setListParam(_param);
  };

  const columns: Array<ProColumns<AppBanner>> = [
    {
      title: '更新时间',
      dataIndex: 'updateAt',
      search: false,
      width: 180,
      render: (_, record) =>
        dayjs(record.updateAt).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '标题',
      dataIndex: 'title',
      search: false,
      width: 120,
    },
    {
      title: '配图',
      dataIndex: 'url',
      valueType: 'image',
      search: false,
      width: IMAGE_WIDTH + 16,
      render: (_, record) => (
        <Image src={record.url} alt={record.title} width={IMAGE_WIDTH} />
      ),
    },
    {
      title: '链接',
      dataIndex: 'link.url',
      search: false,
      width: 200,
      render: (_, record) =>
        record.link && record.link.url ? (
          <a target="_blank" href={record.link.url} rel="noreferrer">
            {record.link.url}
          </a>
        ) : (
          '-'
        ),
    },
    {
      title: '指定地区',
      dataIndex: 'regions',
      search: false,
      width: 200,
      render: (_, record) =>
        record.regions?.map((item, index) => (
          <div key={String(index)}>{item}</div>
        )),
    },
    {
      title: '目标人群',
      dataIndex: 'tagName',
      search: false,
      width: 200,
    },
    {
      title: '有效期',
      dataIndex: 'endTime',
      search: false,
      width: 300,
      render: (_, record) => (
        <>
          {dayjs(record.startTime).format('YYYY-MM-DD HH:mm:ss')} -{' '}
          {dayjs(record.endTime).format('YYYY-MM-DD HH:mm:ss')}
        </>
      ),
    },
    {
      title: '当前状态',
      dataIndex: 'status',
      valueType: 'select',
      width: 100,
      formItemProps: {
        label: '状态',
      },
      valueEnum: {
        [AppBannerTypeEnum.ENABLED]: '已启用',
        [AppBannerTypeEnum.UNUSED]: '未启用',
        [AppBannerTypeEnum.DISABLED]: '已失效',
      },
      render: (_, record) => {
        let text = record.enable === 1 ? '已启用' : '未启用';
        if (new Date().getTime() > record.endTime) {
          text = '已失效';
        }
        return text;
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      search: false,
      fixed: 'right',
      width: 150,
      render: (_, record) => (
        <Space>
          <Button key="1" type="link" onClick={() => editAppBanner(record.id)}>
            编辑
          </Button>
          <Popconfirm
            key="2"
            title="确定要删除么？"
            onConfirm={() => deleteAppBanner(record.id)}
          >
            <Button type="link">删除</Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  useEffect(() => {
    if (!urlParam || !urlParam.type) {
      setListParam(initAppBannerListParam);
      return;
    }
    formRef.current?.setFieldsValue({
      status: urlParam.type,
    });

    setListParam({
      ...initAppBannerListParam,
      type: urlParam.type,
    });
  }, [urlParam]);

  useEffect(() => {
    if (listParam) {
      requestAppBannerList(listParam);
    }
  }, [listParam]);

  return (
    <ProTable<AppBanner>
      dataSource={dataList}
      columns={columns}
      defaultSize="small"
      rowKey="id"
      formRef={formRef}
      search={{
        defaultCollapsed: false,
        span: spanConfig,
        optionRender: searchOptionRender,
      }}
      options={{
        reload: () => {
          if (listParam) setListParam({ ...listParam });
        },
      }}
      scroll={{
        x: columns
          .filter((col) => col.dataIndex !== 'action')
          .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
      }}
      toolbar={{
        actions: [
          <Button
            key="add-button"
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => editAppBanner(0)}
          >
            新增
          </Button>,
        ],
      }}
      pagination={{
        pageSize: paginator.limit,
        total: paginator.total,
        showQuickJumper: true,
        onChange: onPaginationChanged,
      }}
    />
  );
};

export default List;
