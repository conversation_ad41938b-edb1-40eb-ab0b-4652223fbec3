import { AppBannerTypeEnum } from '@/models/community/interface';
import { UploadFile } from 'antd';
import { Dayjs } from 'dayjs';
import { RegionSelectorValue } from './Edit/RegionSelector';

export enum UserGroupTypeEnum {
  ALL = 0,
  SELECTED = 1,
}

export interface UrlParam {
  type?: AppBannerTypeEnum;
}

export interface AppBannerTableForm {
  status?: AppBannerTypeEnum;
}

export interface AppBannerForm {
  title: string;
  enabled: boolean;
  userIds?: string;
  expiredTimes: Dayjs[];
  images: UploadFile[];
  link: string;
  userGroupType: UserGroupTypeEnum;
  userGroupIds: number[];
  regionValueList: { regionInfo: RegionSelectorValue }[];
}
