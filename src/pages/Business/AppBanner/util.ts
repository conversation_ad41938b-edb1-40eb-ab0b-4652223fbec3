import { SelectOption } from '@/models/common.interface';
import { AppBanner, AppBannerParam } from '@/models/community/interface';
import { uuid } from '@/utils/uuid';
import Dayjs from 'dayjs';
import {
  RegionSelectorValue,
  initRegionSelectorValue,
} from './Edit/RegionSelector';
import { AppBannerForm, UserGroupTypeEnum } from './interface';

export const initialAppBannerForm: AppBannerForm = {
  title: '',
  enabled: false,
  expiredTimes: [],
  images: [],
  link: '',
  userGroupType: UserGroupTypeEnum.ALL,
  userGroupIds: [],
  regionValueList: [],
};

export const userGroupTypeOptions: SelectOption[] = [
  { label: '全部用户', value: UserGroupTypeEnum.ALL },
  { label: '用户分群', value: UserGroupTypeEnum.SELECTED },
];

const generateRegionInfo = (baseCode: string): RegionSelectorValue => {
  if (baseCode === 'CN') {
    return initRegionSelectorValue;
  }
  if (!/^CN_\d{6}$/.test(baseCode)) {
    throw new Error('非法的格式. 期待的格式: CN_xxxxxx');
  }

  const provinceCode = `CN_${baseCode.substring(3, 5)}0000`;
  const cityCode = `CN_${baseCode.substring(3, 7)}00`;

  return {
    region: 'CN',
    province: provinceCode,
    city: cityCode,
    district: baseCode,
  };
};

// 将formData转换为param
export const transferFormDataToParam = (
  formData: AppBannerForm,
  id?: number,
): AppBannerParam => {
  const param: AppBannerParam = {
    title: formData.title,
    url: formData.images[0].url || '',
    enable: formData.enabled ? 1 : 0,
    // regions: ["CN_440204","CN_140203"] 国家_地区编码
    regions: JSON.stringify(
      formData.regionValueList &&
        formData.regionValueList.length &&
        formData.regionValueList.every(
          (rv) => rv.regionInfo && rv.regionInfo.district,
        )
        ? formData.regionValueList
            .filter((rv) => rv.regionInfo.district !== null)
            .map((rv) => rv.regionInfo.district)
        : ['ALL'],
    ),
    // link: {"url": "xxxxx"}
    link: JSON.stringify({
      url: formData.link || '',
      needLogin: '0',
    }),
    startTime: formData.expiredTimes[0].valueOf(),
    endTime: formData.expiredTimes[1].valueOf(),
  };
  if (id) {
    param.id = id;
  }
  if (formData.userGroupIds && formData.userGroupIds.length > 0) {
    // tagId: 1,2,3,4,5
    param.tagId = formData.userGroupIds.join(',');
  }
  if (formData.userIds) {
    param.testUser = formData.userIds;
  }
  return param;
};

// 将接口返回的详情数据转换为formData
export const transferDetailToFormData = (detail: AppBanner): AppBannerForm => {
  const formData: AppBannerForm = {
    title: detail.title,
    enabled: !!detail.enable,
    expiredTimes: [Dayjs(detail.startTime), Dayjs(detail.endTime)],
    images: [{ uid: uuid(), name: 'pic', url: detail.url }],
    link: detail.link ? detail.link.url : '',
    userGroupType: detail.tagId
      ? UserGroupTypeEnum.SELECTED
      : UserGroupTypeEnum.ALL,
    userGroupIds: (detail.tagId || '').split(',').map((id) => +id),
    regionValueList:
      detail.regions[0] === 'ALL'
        ? []
        : detail.regions.map((region) => ({
            regionInfo: generateRegionInfo(region),
          })),
    userIds: detail.testUser,
  };
  return formData;
};
