import VideoPlayer from '@/components/VideoPlayer';
import { postMessageFunction } from '@/models/common.util';
import { fetchPetList } from '@/models/pet/fetch';
import { Pet, PetListParam } from '@/models/pet/interface';
import { initPetListParam } from '@/models/pet/util';
import { Pagination, initPagination } from '@/utils/request';
import useUrlState from '@ahooksjs/use-url-state';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProTable,
} from '@ant-design/pro-components';
import { Button, FormInstance, Image } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { PhotoIdentifyTableForm, PhotoIdentifyUrlParam } from '../interface';

const List: React.FC = () => {
  const formRef = useRef<FormInstance<PhotoIdentifyTableForm>>();
  const [urlParam] = useUrlState<PhotoIdentifyUrlParam>();
  const [dataList, setDataList] = useState<Pet[]>([]);
  const [listParam, setListParam] = useState<PetListParam>();
  const [pagination, setPagination] = useState<Pagination>(initPagination);

  // 获取列表数据
  const requestPetList = async (param: PetListParam = initPetListParam) => {
    const { items, ...rest } = await fetchPetList(param);
    setPagination(rest);
    setDataList(items);
  };

  const previewImageList = (imageList: string[], defaultValue?: string) => {
    return imageList && imageList.length ? (
      <Image.PreviewGroup>
        {imageList.map((src, index) => (
          <Image key={String(index)} width={100} src={src} />
        ))}
      </Image.PreviewGroup>
    ) : (
      defaultValue || '-'
    );
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          const formData: PhotoIdentifyTableForm = form?.getFieldsValue();
          const param: PhotoIdentifyUrlParam = {};
          if (formData.owner) {
            param.userId = formData.owner;
          }
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: '/pet/photoIdentify',
              param: param as Record<string, string>,
            },
          });
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          form?.resetFields();
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: '/pet/photoIdentify',
            },
          });
        }}
      >
        重置
      </Button>,
    ];
  };

  const onPaginationChanged = (page: number, pageSize: number) => {
    let index = page;
    if (!listParam || pageSize !== listParam.limit) {
      index = 1;
    }
    setListParam({
      ...listParam,
      limit: pageSize,
      offset: (index - 1) * pageSize,
    });
  };

  const columns: Array<ProColumns<Pet>> = [
    {
      title: '主人',
      dataIndex: 'owner',
      width: 100,
      render: (_, record) => record.owner.id,
    },
    { title: '宠物', dataIndex: 'name', search: false, width: 120 },
    {
      title: '正面照',
      dataIndex: 'omsDiscernPic.faceInfo',
      search: false,
      width: 500,
      render: (_, record) => {
        const faceInfoImageList: string[] = [];
        (record.omsDiscernPic?.faceInfo || []).forEach((item) => {
          if (item.faceOrigin) {
            faceInfoImageList.push(item.faceOrigin);
          }
        });
        return previewImageList(faceInfoImageList);
      },
    },
    {
      title: '身体照',
      dataIndex: 'omsDiscernPic.body',
      search: false,
      width: 500,
      render: (_, record) => previewImageList(record.omsDiscernPic.body),
    },
    {
      title: '身体视频',
      dataIndex: 'omsDiscernPic.body',
      search: false,
      width: 200,
      render: (_, record) =>
        record.omsDiscernPic.bodyVideo ? (
          <Image
            style={{ cursor: 'pointer' }}
            alt="视频"
            src={`${record.omsDiscernPic.bodyVideo}?vframe/jpg/offset/0.01/w/100/h/100`}
            preview={{
              imageRender: () => (
                <VideoPlayer
                  width={1000}
                  height={800}
                  src={record.omsDiscernPic.bodyVideo}
                />
              ),
              toolbarRender: () => null,
            }}
          />
        ) : (
          '--'
        ),
    },
  ];

  useEffect(() => {
    console.log(urlParam.userId);
    const formData: PhotoIdentifyTableForm = {
      owner: urlParam.userId,
    };

    const form = formRef.current;
    form?.setFieldsValue(formData);

    if (urlParam.userId) {
      setListParam({ ...initPetListParam, id: `owner-${urlParam.userId}` });
    }
  }, [urlParam]);

  useEffect(() => {
    if (listParam) requestPetList(listParam);
  }, [listParam]);

  return (
    <ProTable<Pet>
      dataSource={dataList}
      columns={columns}
      defaultSize="small"
      rowKey="id"
      formRef={formRef}
      search={{
        defaultCollapsed: false,
        span: 6,
        optionRender: searchOptionRender,
      }}
      scroll={{
        x: columns
          .filter((col) => col.dataIndex !== 'action')
          .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
      }}
      options={{
        reload: () => listParam && setListParam({ ...listParam }),
      }}
      pagination={{
        pageSize: pagination.limit,
        total: pagination.total,
        showQuickJumper: true,
        onChange: onPaginationChanged,
      }}
    />
  );
};

export default List;
