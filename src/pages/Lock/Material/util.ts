import {
  LockMaterialDetail,
  LockMaterialParam,
  LockMaterialSearchTypeEnum,
} from '@/models/lock/interface';
import { LockMaterialForm, LockMaterialSearchForm } from './interface';

export const initLockMaterialSearchForm: LockMaterialSearchForm = {
  type: LockMaterialSearchTypeEnum.CODE,
  content: '',
};

export const initLockMaterialForm: LockMaterialForm = {
  code: '',
  name: '',
  specification: '',
  project: '',
};

// 将formData转换为param
export const transferFormDataToParam = (
  formData: LockMaterialForm,
  id?: number,
): LockMaterialParam => {
  const param: LockMaterialParam = {
    code: formData.code,
    name: formData.name,
    specification: formData.specification,
    project: formData.project,
  };
  id && (param.id = id);
  return param;
};

// 将接口返回的详情数据转换为formData
export const transferDetailToFormData = (detail: LockMaterialDetail): LockMaterialForm => {
  const formData: LockMaterialForm = {
    code: detail.code,
    name: detail.name,
    specification: detail.specification,
    project: detail.project,
  };
  return formData;
};
