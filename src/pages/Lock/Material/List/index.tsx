import { ApiSuccessEnum, SelectOption } from '@/models/common.interface';
import { spanConfig, transferFormDataToParam } from '@/models/common.util';
import {
  fetchLockMaterialDeletion,
  fetchLockMaterialList,
} from '@/models/lock/fetch';
import {
  LockMaterial,
  LockMaterialListParam,
  LockMaterialSearchTypeEnum,
} from '@/models/lock/interface';
import { initLockMaterialListParam } from '@/models/lock/util';
import { initPagination, Pagination } from '@/utils/request';
import { PlusOutlined } from '@ant-design/icons';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProTable,
} from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { Button, FormInstance, message, Popconfirm, Select, Space } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { LockMaterialSearchForm } from '../interface';
import { initLockMaterialSearchForm } from '../util';

const List: React.FC = () => {
  const searchTypeList: Array<SelectOption<LockMaterialSearchTypeEnum>> = [
    {
      label: '按照名称',
      value: LockMaterialSearchTypeEnum.NAME,
    },
    {
      label: '按照编码',
      value: LockMaterialSearchTypeEnum.CODE,
    },
    {
      label: '按照项目',
      value: LockMaterialSearchTypeEnum.PROJECT,
    },
  ];

  const [dataList, setDataList] = useState<LockMaterial[]>([]);
  const [pagination, setPagination] = useState<Pagination>(initPagination);
  const [listParam, setListParam] = useState<LockMaterialListParam>(
    initLockMaterialListParam,
  );
  const formRef = useRef<FormInstance<LockMaterialSearchForm>>();

  // 获取列表数据
  const requestMaterialList = async (
    param: LockMaterialListParam = initLockMaterialListParam,
  ) => {
    const { items, ...rest } = await fetchLockMaterialList(param);
    setDataList(items);
    setPagination(rest);
  };

  // 编辑
  const editMaterial = (id?: number) => {
    history.push(`/lock/material/edit/${id}`);
  };

  // 删除
  const deleteLockMaterial = async (id: number) => {
    const result = await fetchLockMaterialDeletion(id);
    if (result === ApiSuccessEnum.success) {
      message.success('删除成功！');
      const index = dataList.findIndex((item) => item.id === id);
      dataList.splice(index, 1);
      setDataList([...dataList]);
    }
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          const _param = transferFormDataToParam<
            LockMaterialSearchForm,
            LockMaterialListParam
          >(form?.getFieldsValue());
          const param = { ...initLockMaterialListParam };
          if (_param.type && _param.content) {
            param.type = _param.type;
            param.content = _param.content;
          }
          setListParam(param);
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          form?.resetFields();
          // setListParam(initMaterialListParam);
        }}
      >
        重置
      </Button>,
    ];
  };

  const onPaginatorChanged = (index: number, pageSize: number) => {
    const param: LockMaterialListParam = {
      ...listParam,
      limit: pageSize,
      offset: pageSize * (index - 1),
    };
    setListParam(param);
  };

  const columns: Array<ProColumns<LockMaterial>> = [
    {
      title: 'ID',
      dataIndex: 'id',
      search: false,
    },
    {
      title: '编码',
      dataIndex: 'code',
      search: false,
    },
    {
      title: '名称',
      dataIndex: 'name',
      search: false,
    },
    {
      title: '规格',
      dataIndex: 'specification',
      search: false,
    },
    {
      title: '项目',
      dataIndex: 'project',
      search: false,
    },
    {
      title: '操作',
      dataIndex: 'action',
      search: false,
      render: (_, row) => (
        <Space>
          <Button type="link" onClick={() => editMaterial(row.id)}>
            编辑
          </Button>
          <Popconfirm
            title="确定要删除该物料数据么？"
            onConfirm={() => deleteLockMaterial(row.id)}
          >
            <Button type="link">删除</Button>
          </Popconfirm>
        </Space>
      ),
    },
    {
      title: '',
      hideInTable: true,
      dataIndex: 'type',
      renderFormItem: (item, { defaultRender, ...rest }) => (
        <Select {...rest} options={searchTypeList} />
      ),
    },
    {
      title: '',
      hideInTable: true,
      dataIndex: 'content',
    },
  ];

  useEffect(() => {
    requestMaterialList(listParam);
  }, [listParam]);

  useEffect(() => {
    const form = formRef.current;
    form?.setFieldsValue(initLockMaterialSearchForm);
  }, [formRef]);

  return (
    <ProTable<LockMaterial>
      dataSource={dataList}
      columns={columns}
      formRef={formRef}
      defaultSize="small"
      rowKey="code"
      search={{
        defaultCollapsed: false,
        span: spanConfig,
        optionRender: searchOptionRender,
      }}
      scroll={{
        x: columns
          .filter((col) => col.dataIndex === 'action')
          .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
      }}
      pagination={{
        pageSize: pagination.limit,
        total: pagination.total,
        showQuickJumper: true,
        onChange: onPaginatorChanged,
      }}
      toolbar={{
        actions: [
          <Button
            key="button"
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => editMaterial(0)}
          >
            新增
          </Button>,
        ],
      }}
    />
  );
};

export default List;
