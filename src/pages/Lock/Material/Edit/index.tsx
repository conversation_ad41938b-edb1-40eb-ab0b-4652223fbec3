import { ApiSuccessEnum } from '@/models/common.interface';
import {
  fetchLockMaterialDetail,
  fetchLockMaterialSaving,
} from '@/models/lock/fetch';
import { ProCard } from '@ant-design/pro-components';
import { history, useParams } from '@umijs/max';
import { Button, Form, Input, message } from 'antd';
import React, { useEffect, useState } from 'react';
import { LockMaterialForm } from '../interface';
import {
  initLockMaterialForm,
  transferDetailToFormData,
  transferFormDataToParam,
} from '../util';

const Edit: React.FC = () => {
  const urlRestParam = useParams<{ id: string }>();
  const [form] = Form.useForm<LockMaterialForm>();
  const [loading, setLoading] = useState(false);
  const layout = {
    labelCol: { span: 3 },
    wrapperCol: { span: 20 },
  };

  // 根据id获取详情数据
  const requestDetailById = async (id: number) => {
    const detail = await fetchLockMaterialDetail(id);
    const formData = transferDetailToFormData(detail);
    form.setFieldsValue(formData);
  };

  // 提交form表单
  const submit = async (formData: LockMaterialForm) => {
    setLoading(true);
    const param = transferFormDataToParam(formData, +(urlRestParam?.id || 0));
    let result = '';
    let state = '';
    try {
      if (param && +(param.id || 0)) {
        state = '更新';
      } else {
        state = '创建';
      }
      result = await fetchLockMaterialSaving(param);

      if (result === ApiSuccessEnum.success) {
        message.success(`${state}成功！`);
        history.back();
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (urlRestParam && urlRestParam.id && +urlRestParam.id) {
      requestDetailById(+urlRestParam.id);
    }
  }, [urlRestParam]);

  return (
    <ProCard>
      <Form
        {...layout}
        form={form}
        onFinish={submit}
        initialValues={initLockMaterialForm}
      >
        <Form.Item
          name="code"
          label="物料编码"
          rules={[{ required: true, message: '请输入物料编码' }]}
        >
          <Input placeholder="请输入物料编码" />
        </Form.Item>
        <Form.Item
          name="name"
          label="物料名称"
          rules={[{ required: true, message: '请输入物料名称' }]}
        >
          <Input placeholder="请输入物料名称" />
        </Form.Item>
        <Form.Item
          name="specification"
          label="物料规格"
          rules={[{ required: true, message: '请输入物料规格' }]}
        >
          <Input placeholder="请输入物料规格" />
        </Form.Item>
        <Form.Item
          name="project"
          label="所属项目"
          rules={[{ required: true, message: '请输入所属项目' }]}
        >
          <Input placeholder="请输入所属项目" />
        </Form.Item>
        <Form.Item wrapperCol={{ span: 24 }} style={{ textAlign: 'center' }}>
          <Button
            type="primary"
            htmlType="submit"
            style={{ marginRight: 16 }}
            loading={loading}
            disabled={loading}
          >
            提交
          </Button>
          <Button htmlType="reset" style={{ marginRight: 16 }} danger>
            重置
          </Button>
          <Button type="default" onClick={history.back}>
            取消
          </Button>
        </Form.Item>
      </Form>
    </ProCard>
  );
};

export default Edit;
