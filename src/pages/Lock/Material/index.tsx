import { BreadcrumbInfo } from '@/models/common.interface';
import global from '@/utils/global';
import { LeftOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { Outlet, useLocation } from '@umijs/max';
import React, { useEffect, useState } from 'react';

const breadcrumbInfo: BreadcrumbInfo = {
  path: '',
  breadcrumbName: '',
};

const Material: React.FC = () => {
  const location = useLocation();
  const [title] = useState(`锁区管理`);
  const [showBackIcon, setShowBackIcon] = useState(false);
  const [breadcrumbInfoList, setBreadcrumbInfoList] = useState([
    breadcrumbInfo,
  ]);
  useEffect(() => {
    // const _breadcrumbInfoList = global.getBreadcrumbInfo(breadcrumbInfo, location);
    const _breadcrumbInfoList = global.getBreadcrumbInfo(
      { ...breadcrumbInfo, breadcrumbName: '锁区物料' },
      '',
      location,
    );

    setBreadcrumbInfoList(_breadcrumbInfoList);
    if (_breadcrumbInfoList.length > 2) {
      setShowBackIcon(true);
    }
  }, [location]);

  return (
    <PageContainer
      header={{
        backIcon: showBackIcon ? <LeftOutlined /> : '',
        onBack: () => history.back(),
        title,
        ghost: true,
        breadcrumb: {
          itemRender: (route) => <span>{route.title}</span>,
          routes: breadcrumbInfoList,
        },
      }}
    >
      <Outlet />
    </PageContainer>
  );
};

export default Material;
