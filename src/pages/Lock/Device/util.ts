import {
  LockDeviceParam,
  LockedDeviceListParam,
} from '@/models/lock/interface';
import { initLockedDeviceListParam } from '@/models/lock/util';
import { LockDeviceForm, LockedDeviceTableForm, UrlParam } from './interface';

// export const initialDeviceForm: DeviceForm = {
// };

export const transferUrlParamToListParam = (
  urlParam: UrlParam,
): LockedDeviceListParam => {
  if (!urlParam.deviceTypeId) {
    return initLockedDeviceListParam;
  }
  const param: LockedDeviceListParam = {
    ...initLockedDeviceListParam,
    deviceTypeId: +urlParam.deviceTypeId,
  };
  if (urlParam.sn) param.sn = urlParam.sn;
  return param;
};

export const transferUrlParamToTableFormData = (
  urlParam: UrlParam,
): LockedDeviceTableForm => {
  if (!urlParam.deviceTypeId) {
    return {};
  }
  const formData: LockedDeviceTableForm = {
    ...initLockedDeviceListParam,
    deviceType: urlParam.deviceTypeId,
  };
  if (urlParam.sn) formData.sn = urlParam.sn;
  return formData;
};

export const initLockDeviceForm: LockDeviceForm = {
  deviceTypeId: null,
  sn: '',
};

// 将formData转换为param
export const transferFormDataToParam = (
  formData: LockDeviceForm,
): LockDeviceParam => {
  const param: LockDeviceParam = {
    deviceTypeId: +(formData.deviceTypeId || 0),
    sn: formData.sn,
  };
  return param;
};

// 将接口返回的详情数据转换为formData
// export const transferDetailToFormData = (detail: DeviceDetail): DeviceForm => {
//   const formData: DeviceForm = {};
//   return formData;
// };
