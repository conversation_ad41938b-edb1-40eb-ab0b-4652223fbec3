import { postMessageFunction, spanConfig } from '@/models/common.util';
import {
  fetchDeviceTypeList,
  fetchLockedDeviceList,
} from '@/models/lock/fetch';
import { DeviceTypeInfo, LockedDeviceInfo } from '@/models/lock/interface';
import useUrlState from '@ahooksjs/use-url-state';
import { PlusOutlined } from '@ant-design/icons';
import {
  BaseQueryFilterProps,
  ProColumns,
  ProTable,
} from '@ant-design/pro-components';
import { Button, FormInstance, message } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { LockedDeviceListParam } from '../../../../models/lock/interface';
import Edit from '../Edit';
import { LockedDeviceTableForm, UrlParam } from '../interface';
import {
  transferUrlParamToListParam,
  transferUrlParamToTableFormData,
} from '../util';

const List: React.FC = () => {
  const [urlParam] = useUrlState<UrlParam>({});
  const [dataList, setDataList] = useState<LockedDeviceInfo[]>([]);
  const [listParam, setListParam] = useState<LockedDeviceListParam>();
  const [deviceTypeList, setDeviceTypeList] = useState<Array<DeviceTypeInfo>>(
    [],
  );
  const [deviceTypeMap, setDeviceTypeMap] = useState<Record<number, string>>();
  const [showEdit, setShowEdit] = useState(false);
  const formRef = useRef<FormInstance<LockedDeviceTableForm>>();

  // 获取列表数据
  const requestLockedDeviceList = async (param: LockedDeviceListParam) => {
    const item = await fetchLockedDeviceList(param);
    if (!item.sn) {
      setDataList([]);
      return;
    }
    setDataList([item]);
  };

  // 获取设备数据
  const requestDeviceTypeList = async () => {
    const _deviceTypeMap = {};
    try {
      const deviceTypeList = await fetchDeviceTypeList();
      // 过滤只支持的设备类型
      const supportedDeviceTypes = [
        'd3',
        'd4',
        'd4s',
        'd4h',
        'd4sh',
        't3',
        't4',
        't5',
        't6',
      ];
      const filteredDeviceTypeList = deviceTypeList.filter((deviceType) =>
        supportedDeviceTypes.includes(deviceType.name.toLowerCase()),
      );

      filteredDeviceTypeList.forEach((deviceType, index) => {
        // console.log(index, deviceType);
        (_deviceTypeMap as Record<number, string>)[deviceType.id] =
          deviceType.name;
      });
      setDeviceTypeList(filteredDeviceTypeList);
      setDeviceTypeMap(_deviceTypeMap);
    } catch (error) {
      console.log(error);
    }
  };

  // 编辑
  const editDevice = () => {
    setShowEdit(true);
  };

  const onEditClose = () => {
    setShowEdit(false);
  };

  // Search的表单按钮渲染
  const searchOptionRender = (
    searchConfig: Omit<BaseQueryFilterProps, 'submitter' | 'isForm'>,
  ) => {
    const { form } = searchConfig;
    return [
      <Button
        key="search"
        type="primary"
        onClick={() => {
          const formData: LockedDeviceTableForm = form?.getFieldsValue();
          const param: LockedDeviceListParam = {};

          if (!formData.deviceType || !formData.sn) {
            message.warning('请提供完整的设备类型 和 SN信息');
            return;
          }

          if (formData.deviceType) param.deviceTypeId = +formData.deviceType;
          if (formData.sn) param.sn = formData.sn;
          // console.log(formData, param);
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: `/lock/device`,
              param: param as unknown as Record<string, string>,
            },
          });
        }}
      >
        查询
      </Button>,
      <Button
        key="reset"
        type="default"
        onClick={() => {
          form?.resetFields();
          postMessageFunction({
            type: 'redirect',
            content: {
              redirectUrl: `/lock/device`,
            },
          });
        }}
      >
        重置
      </Button>,
    ];
  };

  const columns: Array<ProColumns<LockedDeviceInfo>> = [
    {
      title: '设备ID',
      dataIndex: 'deviceId',
      search: false,
    },
    {
      title: '设备类型',
      dataIndex: 'deviceType',
      valueType: 'select',
      valueEnum: deviceTypeMap,
    },
    {
      title: '设备SN',
      dataIndex: 'sn',
    },
    {
      title: '锁机理由',
      dataIndex: 'disableReason',
      search: false,
    },
  ];

  useEffect(() => {
    requestDeviceTypeList();
  }, []);

  useEffect(() => {
    if (!urlParam) return;
    const form = formRef.current;
    const _listParam = transferUrlParamToListParam(urlParam);
    const formData: LockedDeviceTableForm =
      transferUrlParamToTableFormData(urlParam);
    setListParam(_listParam);

    console.log(formData);
    if (!form) return;
    form.setFieldsValue(formData);
  }, [urlParam]);

  useEffect(() => {
    if (!listParam || !listParam.deviceTypeId) return;
    requestLockedDeviceList(listParam);
  }, [listParam]);

  return (
    <>
      <ProTable<LockedDeviceInfo>
        dataSource={dataList}
        columns={columns}
        defaultSize="small"
        rowKey="sn"
        formRef={formRef}
        search={{
          defaultCollapsed: false,
          span: spanConfig,
          optionRender: searchOptionRender,
        }}
        options={{
          reload: () => {
            if (listParam) setListParam({ ...listParam });
          },
        }}
        scroll={{
          x: columns
            .filter((col) => col.dataIndex !== 'action')
            .reduce((prev, curr) => Number(curr.width || 0) + prev, 0),
        }}
        toolbar={{
          actions: [
            <Button
              key="button"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => editDevice()}
            >
              新增锁机
            </Button>,
          ],
        }}
      />
      {showEdit ? (
        <Edit
          deviceTypeOptions={deviceTypeList.map((item, index) => ({
            label: item.name,
            value: item.id,
          }))}
          onCancel={() => onEditClose()}
        />
      ) : null}
    </>
  );
};

export default List;
