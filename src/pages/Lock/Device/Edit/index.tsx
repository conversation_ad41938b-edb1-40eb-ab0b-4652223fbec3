import { ApiSuccessEnum, SelectOption } from '@/models/common.interface';
import { fetchLockDevice } from '@/models/lock/fetch';
import { history } from '@umijs/max';
import { Button, Drawer, Form, Input, Select, message } from 'antd';
import React, { useState } from 'react';
import { LockDeviceForm } from '../interface';
import { initLockDeviceForm, transferFormDataToParam } from '../util';

interface Props {
  deviceTypeOptions: SelectOption[];
  onCancel: () => void;
}

const Edit: React.FC<Props> = ({ deviceTypeOptions, onCancel }: Props) => {
  const [form] = Form.useForm<LockDeviceForm>();
  const [loading, setLoading] = useState(false);
  const layout = {
    labelCol: { span: 24 },
    wrapperCol: { span: 24 },
  };

  // 提交form表单
  const submit = async (formData: LockDeviceForm) => {
    setLoading(true);
    const _param = transferFormDataToParam(formData);
    // console.log(formData, _param);
    try {
      let result = await fetchLockDevice(_param);

      if (result === ApiSuccessEnum.success) {
        message.success(`锁机成功！`);
        onCancel();
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Drawer open onClose={onCancel}>
      <Form
        {...layout}
        layout="vertical"
        form={form}
        onFinish={submit}
        initialValues={initLockDeviceForm}
      >
        <Form.Item
          name="deviceTypeId"
          label="设备类型"
          rules={[{ required: true, message: '请选择设备类型' }]}
        >
          <Select placeholder="请选择设备类型" options={deviceTypeOptions} />
        </Form.Item>
        <Form.Item
          name="sn"
          label="设备SN(有多个，请用英文逗号分割)"
          rules={[{ required: true, message: '请输入设备SN' }]}
        >
          <Input.TextArea placeholder="请输入设备SN" />
        </Form.Item>
        <Form.Item style={{ textAlign: 'right' }}>
          <Button
            type="primary"
            htmlType="submit"
            style={{ marginRight: 16 }}
            loading={loading}
            disabled={loading}
          >
            提交
          </Button>
          <Button type="default" onClick={history.back}>
            取消
          </Button>
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default Edit;
