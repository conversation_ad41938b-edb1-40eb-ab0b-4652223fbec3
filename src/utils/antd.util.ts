import { Pagination, PaginatorParam } from '@/utils/request';
import { TablePaginationConfig } from 'antd';

export const antdUtils = {
  transferPaginationToTablePagination: (pagination: Pagination) => {
    const tablePagination: TablePaginationConfig = {
      total: pagination.total,
      current: Math.trunc(pagination.offset / pagination.limit) + 1,
      pageSize: pagination.limit,
    };
    return tablePagination;
  },

  getPaginatorParamByTablePaginationChange: (
    paginatorParam: PaginatorParam,
    page: number,
    pageSize: number,
  ) => {
    const _paginatorParam: PaginatorParam = {
      limit: pageSize,
      offset: 0,
    };
    let index = page;
    if (pageSize !== paginatorParam.limit) {
      index = 1;
    }
    _paginatorParam.offset = pageSize * (index - 1);
    return _paginatorParam;
  },
};
