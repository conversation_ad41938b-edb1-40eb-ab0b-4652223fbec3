export function arabicToChinese(num: number): string {
  const chineseNumbers = [
    '零',
    '一',
    '二',
    '三',
    '四',
    '五',
    '六',
    '七',
    '八',
    '九',
  ];
  const units = ['', '十'];
  let result = '';

  // 处理个位
  if (num % 10 !== 0) {
    result += chineseNumbers[num % 10];
  }

  // 单独处理10的情况
  if (num === 10) {
    result = units[1];
  }

  // 处理十位
  if (num > 10) {
    result = chineseNumbers[Math.floor(num / 10)] + units[1] + result;
  }

  // 处理零的情况
  if (num === 0) {
    result = chineseNumbers[0];
  } else if (result === '') {
    result = '零';
  }

  return result;
}
