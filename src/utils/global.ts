import {
  BreadcrumbInfo,
  SelectOption,
  ValueType,
} from '@/models/common.interface';
import { Location } from '@umijs/max';
import { StorageService } from './Storage';

class Global {
  static instance: Global;
  apiVersion = '9.0.0';
  baiduHmKey = '';
  iconfontUrl = '';
  // 蓝牙设备列表
  bluetoothDeviceList = [
    'hg',
    'fit',
    'go',
    'k3',
    'aq',
    'aqr',
    'p3',
    'h2',
    'w5',
    'r2',
    'ctw3',
  ];
  // wifi设备列表
  wifiDeviceList = [
    't3',
    'mate',
    'feeder',
    'cozy',
    'feedermini',
    'aqh1',
    'k2',
    't4',
    't5',
    't6',
    't7',
    'd3',
    'd4',
    'd4s',
    'd4sh',
    'd4h',
    'h3',
  ];
  centerDeviceList = [
    'aqh1',
    'd4s',
    'd4sh',
    't3',
    't4',
    't5',
    'h3',
    'd3',
    'd4',
    'feedermini',
    'k2',
  ];
  // 支持灰度发布的设备型号
  graySupportedDeviceList = [
    't4',
    'd4',
    'd4s',
    'd4sh',
    'd4h',
    't5',
    't6',
    't7',
  ];

  appMinVersionDeviceList = [
    'w5',
    't3',
    'd3',
    'd4',
    't3',
    't4',
    'd4s',
    'aq',
    'aqr',
    'k3',
    'p3',
    'r2',
    'hg',
    // 'k2',
    'aqh1',
    'd4sh',
    'd4h',
    'ctw3',
    't5',
    't6',
    't7',
  ];

  deviceNames: { [key: string]: string } = {
    hg: '烘干箱',
    h3: '摄像头',
    ctw3: '感应饮水机',
    d3: '智能行星喂食器',
    d4: '智能喂食器SOLO',
    cozy: '宠物窝',
    feedermini: '喂食器Mini',
    t3: 'T3猫厕所',
    k2: '智能全屋净味器',
    d4sh: '小佩智能双子星喂食器 可视版',
    d4h: '小佩智能喂食器 SOLO 可视版',
    t6: '小佩智能全自动猫厕所 ULTRA 可视版',
    t5: '小佩智能全自动猫厕所 MAX  Pro 可视版',
    t7: '小佩智能全自动双翼猫厕所 可视版',
  };

  bindDeviceByPetId = ['cozy', 'p3'];

  remoteHardwareDevice = [
    'feeder',
    'cozy',
    'feedermini',
    't3',
    't4',
    'aqh1',
    'k2',
    'h3',
    'd3',
    'd4',
    'd4s',
    'd4sh',
    'd4h',
    't6',
    't7',
    't5',
  ];
  localOneHardwareDevice = ['k3', 'aqr', 'p3'];
  localTwoHardwareDevice = ['hg', 'w5', 'ctw3', 'r2'];
  localThereHardwareDevice = ['aq'];
  newLogPageDeviceList = ['d4sh', 'd4h', 't5', 't6', 't7'];

  firmwareInternalRemarkDeviceList = [
    't3',
    't4',
    'd3',
    'd4',
    'd4s',
    'd4sh',
    'd4h',
    't5',
    't6',
    't7',
  ];

  storageService: StorageService;

  defaultFallbackImage =
    'data:image/png;base64,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';

  getToken: () => string;

  constructor() {
    this.storageService = new StorageService();
    this.getToken = () => this.storageService?.getLocalItem('token') || '';

    if (typeof Global.instance === 'object') {
      return Global.instance;
    }

    Global.instance = this;
    return this;
  }

  getOptionsByMap(map: Map<ValueType, string>): SelectOption[] {
    const options: SelectOption[] = [];
    map.forEach((value, key) => {
      options.push({
        label: value,
        value: key,
      });
    });
    return options;
  }

  getBreadcrumbInfo(
    oldBreadcrumbInfo: BreadcrumbInfo,
    prefix?: string,
    location?: Location,
  ): BreadcrumbInfo[] {
    const newRoutes = [oldBreadcrumbInfo];
    const listBreadcrumbInfo: BreadcrumbInfo = {
      path: location?.pathname || '',
      breadcrumbName: `${oldBreadcrumbInfo.breadcrumbName}${prefix}列表`,
    };
    const detailBreadcrumbInfo: BreadcrumbInfo = {
      path: location?.pathname || '',
      breadcrumbName: `${oldBreadcrumbInfo.breadcrumbName}${prefix}详情`,
    };
    const editBreadcrumbInfo: BreadcrumbInfo = {
      path: location?.pathname || '',
      breadcrumbName: `${oldBreadcrumbInfo.breadcrumbName}${prefix}编辑`,
    };
    if (location?.pathname.includes('list')) {
      newRoutes.push(listBreadcrumbInfo);
    } else if (location?.pathname.includes('detail')) {
      newRoutes.push(listBreadcrumbInfo);
      newRoutes.push(detailBreadcrumbInfo);
    } else if (location?.pathname.includes('edit')) {
      newRoutes.push(listBreadcrumbInfo);
      newRoutes.push(editBreadcrumbInfo);
    }
    return newRoutes;
  }

  getUrlParams<T extends { [key: string]: string | undefined }>(obj: T) {
    // 该方法会把值为0 的项过滤掉
    // 如果所有项都会空 则不会返回 ‘?’
    let str = '';
    for (const key in obj) {
      if (obj[key]) {
        const item = obj[key];
        if (item !== null && item !== undefined && item !== '') {
          str += `${key}=${item}&`;
        }
      }
    }
    if (str.length !== 0) {
      str = `?${str.substr(0, str.length - 1)}`;
    }
    return str;
  }
}

export default new Global();
