function videoAsync(url: string): Promise<HTMLVideoElement | null> {
  return new Promise((resolve) => {
    const video = document.createElement('video');

    // 当视频元数据加载完成后，获取视频的宽度和高度
    video.addEventListener('loadedmetadata', function () {
      // console.log(video);
      const width = video.videoWidth;
      const height = video.videoHeight;
      console.log(`视频宽度: ${width}, 视频高度: ${height}`);
      resolve(video);
    });

    video.src = url;
    video.load();
  });
}

export default videoAsync;
