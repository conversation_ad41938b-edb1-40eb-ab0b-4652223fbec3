import CryptoJS from 'crypto-js';

/**
 * 解密OSS链接中的加密信息并生成新的链接
 * @param encryptedUrl 包含加密信息的OSS链接
 * @param key AES解密密钥 (十六进制字符串)
 * @param iv AES解密IV (十六进制字符串)
 * @returns 解密后的新链接
 */
export function decryptOssUrl(
  encryptedUrl: string,
  key: string,
  iv: string,
): string {
  // 提取URL中加密的部分，这里假设加密信息在查询参数中，并且需要解密整个URL
  // 实际情况可能需要根据URL结构调整，例如只解密某个特定的参数值
  // 为了简化，这里假设整个URL字符串是需要解密的内容，这可能不完全符合实际OSS链接的加密方式
  // 通常，只有URL中的某个参数值（如签名、过期时间等）是加密的
  // 如果您需要解密URL中的特定参数，请告诉我具体是哪个参数。

  // 将十六进制密钥和IV转换为WordArray
  const aesKey = CryptoJS.enc.Hex.parse(key);
  const aesIv = CryptoJS.enc.Hex.parse(iv);

  // 假设整个URL字符串是加密的，这在实际中不常见，通常是URL中的某个参数值被加密
  // 如果加密的是URL中的某个参数，您需要先解析URL，提取出加密的参数值，然后进行解密
  // 这里为了演示解密过程，我们假设您需要解密的是一个经过Base64编码的字符串，该字符串是原始URL的一部分
  // 实际OSS链接的加密方式通常是签名，而不是整个链接被AES加密。
  // 如果您指的是OSS链接中的某个参数（例如 `x-oss-signature` 或 `x-oss-expires`）是AES加密的，
  // 您需要先从URL中提取出这些参数的值，然后对这些值进行解密。

  // 示例：如果URL本身是加密的（这不太可能），或者URL中某个参数的值是加密的
  // 假设我们有一个Base64编码的加密字符串，例如从URL中提取出来的某个参数值
  // 为了演示，我们先模拟一个加密过程，然后解密

  // 原始链接：http://petkit-cloud-storage-7-prod.oss-cn-hangzhou.aliyuncs.com/t5/108603/3_021SHIT_PICTURE1750041718?x-oss-date=20250616T024404Z&x-oss-expires=3600&x-oss-signature-version=OSS4-HMAC-SHA256&x-oss-credential=LTAI5tMrYpaAxKw1cLNGfVC5%2F20250616%2Fcn-hangzhou%2Foss%2Faliyun_v4_request&x-oss-signature=4309fb725219b1808c3ca72ae54a18521899784d87ad0e0ebc096cd16696224c

  // 鉴于您提供的链接是一个完整的OSS带签名的URL，通常这种URL本身不是AES加密的。
  // 而是URL中的某些参数（如 `x-oss-signature`）是基于密钥和算法生成的签名。
  // 如果您想解密的是这个签名，那它不是AES解密，而是验证签名。
  // 如果您确定是AES加密，请明确指出URL中哪一部分是AES加密的内容。

  // 如果您想解密的是URL中某个参数的值，例如 `x-oss-signature` 的值，
  // 您需要先从URL中解析出这个值，然后对它进行AES解密。
  // 假设您要解密的是一个Base64编码的字符串，这里以您提供的完整URL作为“加密内容”进行演示，
  // 但请注意，这可能不是您实际的用例。

  try {
    // 假设加密内容是Base64编码的
    // 如果加密内容不是Base64编码，请调整这里的解码方式
    const decrypted = CryptoJS.AES.decrypt(encryptedUrl, aesKey, {
      iv: aesIv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    });

    // 将解密后的WordArray转换为UTF-8字符串
    const originalText = decrypted.toString(CryptoJS.enc.Utf8);
    return originalText;
  } catch (error) {
    console.error('解密失败:', error);
    // 返回原始链接或抛出错误，取决于您的错误处理策略
    return encryptedUrl; // 解密失败时返回原始链接
  }
}

// 您的OSS链接
const ossLink = `http://petkit-cloud-storage-7-prod.oss-cn-hangzhou.aliyuncs.com/t5/108603/3_021SHIT_PICTURE1750041718?x-oss-date=20250616T024404Z&x-oss-expires=3600&x-oss-signature-version=OSS4-HMAC-SHA256&x-oss-credential=LTAI5tMrYpaAxKw1cLNGfVC5%2F20250616%2Fcn-hangzhou%2Foss%2Faliyun_v4_request&x-oss-signature=4309fb725219b1808c3ca72ae54a18521899784d87ad0e0ebc096cd16696224c`;

// AES密钥和IV
const aesKey = 'cfa382bb8aa2d1ca';
const aesIv = '61616161616161616161616161616161';

// 调用解密函数
const decryptedLink = decryptOssUrl(ossLink, aesKey, aesIv);

console.log('原始链接:', ossLink);
console.log('解密后的链接:', decryptedLink);
