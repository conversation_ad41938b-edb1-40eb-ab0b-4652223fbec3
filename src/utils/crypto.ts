/**
 * 图片解密工具
 */

// 首先需要安装 crypto-js 库: npm install crypto-js
// 或者使用 pnpm: pnpm add crypto-js
// 然后添加类型定义: npm install @types/crypto-js -D

/**
 * 将Blob对象转换为Base64字符串
 * @param blob Blob对象
 * @returns Promise<string> Base64字符串
 */
function blobToBase64(blob: Blob): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      resolve(reader.result as string);
    };
    reader.onerror = (error) => {
      console.error('Blob转Base64失败:', error);
      reject(error);
    };
    reader.readAsDataURL(blob);
  });
}

/**
 * AES解密图片
 * @param url 加密的图片URL
 * @param key AES密钥
 * @param iv 初始化向量
 * @returns 解密后的Blob对象
 */
export async function decryptImage(
  imageUrl: string,
  key?: Uint8Array,
  iv: string = '61616161616161616161616161616161',
): Promise<Blob | null> {
  // 如果没有提供密钥，使用默认密钥
  if (!key) {
    throw new Error('密钥不能为空');
  }

  // 确保iv是Uint8Array类型
  const ivArray = new Uint8Array(
    iv.match(/.{1,2}/g)?.map((byte) => parseInt(byte, 16)) || [],
  );

  try {
    // 下载加密的图片
    const response = await fetch(imageUrl);

    if (!response.ok) {
      throw new Error(`下载图片失败，状态码: ${response.status}`);
    }

    // 获取加密的图片数据
    const encryptedData = new Uint8Array(await response.arrayBuffer());

    // 使用Web Crypto API进行解密
    const cryptoKey = await window.crypto.subtle.importKey(
      'raw',
      key,
      { name: 'AES-CBC' },
      false,
      ['decrypt'],
    );

    // 解密数据
    const decryptedBuffer = await window.crypto.subtle.decrypt(
      {
        name: 'AES-CBC',
        iv: ivArray,
      },
      cryptoKey,
      encryptedData,
    );

    // 处理填充问题
    let decryptedData = new Uint8Array(decryptedBuffer);
    const paddingLen = decryptedData[decryptedData.length - 1];
    if (paddingLen < 16) {
      // AES块大小为16字节
      decryptedData = decryptedData.slice(0, decryptedData.length - paddingLen);
    }

    // 将解密后的数据转换为Blob
    const blob = new Blob([decryptedData], { type: 'image/jpeg' });

    return blob;
  } catch (e) {
    console.error(
      `解密图片时出错: ${e instanceof Error ? e.message : String(e)}`,
    );
    return null;
  }
}

/**
 * 创建解密后图片的URL
 * @param url 加密的图片URL
 * @param key AES密钥
 * @param iv 初始化向量
 * @returns 解密后图片的Blob URL或Base64 URL
 */
export const createDecryptedImageUrl = async (
  url: string,
  key: string,
  iv: string,
): Promise<{ url: string; blob: Blob }> => {
  try {
    console.log('开始创建解密图片URL...');
    const encoder = new TextEncoder();
    const encodedKey = encoder.encode(key);
    const blob = await decryptImage(url, encodedKey, iv);

    if (!blob) {
      throw new Error('解密图片失败');
    }

    // 首先尝试转换为Base64
    try {
      const base64Url = await blobToBase64(blob);
      console.log('成功创建Base64 URL:', base64Url.substring(0, 50) + '...');
      return {
        url: base64Url,
        blob: blob,
      };
    } catch (base64Error) {
      console.warn('创建Base64 URL失败，尝试创建Blob URL:', base64Error);

      // 如果转换为Base64失败，尝试创建Blob URL
      const blobUrl = URL.createObjectURL(blob);
      console.log('成功创建Blob URL:', blobUrl);
      return {
        url: blobUrl,
        blob: blob,
      };
    }
  } catch (error) {
    console.error('创建解密图片URL失败:', error);
    throw error;
  }
};
