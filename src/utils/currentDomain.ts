export const getCurrentDomain = () => {
  const { origin, port } = location;
  const cnOnline = 'api.petkit.cn/latest';
  const cnSandbox = 'api-sandbox.petkit.cn/6';
  const cnSandbox2 = 'api-sandbox2.petkit.com/6';

  if (port === '9997') {
    return cnSandbox2;
  }

  if (port === '9998') {
    return cnSandbox;
  }

  if (origin.includes('sandbox2')) return cnSandbox2;
  else if (origin.includes('sandbox')) return cnSandbox;
  else if (origin.includes('petkit')) return cnOnline;

  return '';
};
