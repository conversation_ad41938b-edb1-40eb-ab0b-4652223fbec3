function readFileAsync(file: File): Promise<FileReader | null> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (event) => {
      resolve(event.target); // 文件读取成功，resolve Promise
    };

    reader.onerror = (error) => {
      reject(error); // 文件读取失败，reject Promise
    };

    reader.onabort = () => {
      reject(new Error('File reading aborted')); // 文件读取中断，reject Promise
    };

    reader.readAsDataURL(file); // 或其他读取方法，例如 readAsText、readAsArrayBuffer
  });
}

export default readFileAsync;
