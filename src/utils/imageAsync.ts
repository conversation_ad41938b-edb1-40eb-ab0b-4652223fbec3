function imageAsync(url: string): Promise<HTMLImageElement | null> {
  return new Promise((resolve, reject) => {
    const image = new Image();

    image.onload = (event) => {
      resolve(image); // 文件读取成功，resolve Promise
    };

    image.onerror = (error) => {
      reject(error); // 文件读取失败，reject Promise
    };

    image.onabort = () => {
      reject(new Error('File reading aborted')); // 文件读取中断，reject Promise
    };

    image.src = url;
  });
}

export default imageAsync;
