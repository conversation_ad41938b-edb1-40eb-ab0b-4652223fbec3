import Request, {
  ErrorInfo,
  RequestOptionsInit,
  Result,
  TokenPlaceEnum,
} from '@mantas/request';
import { history } from '@umijs/max';
import { message } from 'antd';

interface ResponseResult<T> {
  result?: T;
  error?: ResponseError;
}

interface ResponseError {
  code: number;
  msg: string;
}

export interface PaginatorParam {
  limit: number;
  offset: number;
}

export interface NewPaginatorParam {
  pageSize: number;
  pageIndex: number;
}

export interface Pagination {
  limit: number;
  offset: number;
  total: number;
}

export type Data<T = unknown> = Pagination & {
  items: T[];
};

export const initPaginatorParam: PaginatorParam = {
  limit: 10,
  offset: 0,
};

export const initNewPaginatorParam: NewPaginatorParam = {
  pageSize: 10,
  pageIndex: 1,
};

export const initPagination: Pagination = {
  limit: 10,
  offset: 0,
  total: 0,
};

const skipValidUrls = ['?hash/md5'];
const skipTransferUrls: string[] = [];
const skipMessageUrls: string[] = ['/adm/attire/judge_priority'];

let request: Request | null = null;

if (!request) {
  request = new Request({
    token: localStorage.getItem('sessionToken') || '',
    tokenKey: 'X-Admin-Session',
    // 不知道为什么，此处的session携带位置必须设置为body or query中才可以实现跨域，否则会报跨域问题
    tokenPlaces: [TokenPlaceEnum.PARAMS],
    timeout: 30 * 1000,
    skipValidUrlList: (requestOptions: RequestOptionsInit) => {
      skipValidUrls.forEach((url) => {
        if (requestOptions.url.includes(url) || requestOptions.url === url) {
          skipTransferUrls.push(requestOptions.url);
        }
      });
      return skipTransferUrls;
    },
    customResolveResponseResult: (
      responseData: ResponseResult<any>,
      requestOption: RequestOptionsInit,
    ) => {
      const result: Result<any> = {
        code: -1,
        data: null,
        message: '请求失败！',
      };

      if (skipTransferUrls.some((url) => requestOption.url.includes(url))) {
        result.code = 0;
        result.data = responseData;
        result.message = '';
        return result;
      }

      if (
        JSON.stringify(responseData) === '{}' ||
        responseData.result ||
        typeof responseData.result === 'boolean' ||
        responseData.result === 0 ||
        responseData.result === '' ||
        responseData.result === null ||
        JSON.stringify(responseData) === '[]' ||
        // 获取音频固件包的md5时，只返回的md5 没有result外层
        (responseData as any).md5
      ) {
        result.code = 0;
        result.message = '';
        result.data = responseData.result ? responseData.result : responseData;
      } else if (responseData.error) {
        result.code = responseData.error.code;
        result.message = responseData.error.msg;
      }
      return result;
    },
    resolveHttpStatusOperation: {
      401: () => {
        const isLoginPage = history.location.pathname === '/login';
        if (!isLoginPage) {
          history.replace('/login');
        }
      },
    },
    // onErrorMessage: (errorMsg: string) => {
    //   message.error(errorMsg);
    // },
    onErrorInfo: (errorInfo: ErrorInfo) => {
      // console.log(errorInfo);
      const {
        response: {
          request: {
            url: outUrl,
            options: { url },
          },
        },
      } = errorInfo;
      if (
        skipMessageUrls.includes(url) ||
        skipMessageUrls.some((_url) => outUrl.includes(_url))
      )
        return;
      const errorMsg = errorInfo.errorMessage;
      message.error(errorMsg);
    },
    beforeRequestResult: () => {
      request.token = localStorage.getItem('sessionToken') || '';
    },
  });
}

export default request.getRequestResult();
